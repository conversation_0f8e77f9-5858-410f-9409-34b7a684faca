(()=>{var e={};e.id=555,e.ids=[555],e.modules={55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},6113:e=>{"use strict";e.exports=require("crypto")},9523:e=>{"use strict";e.exports=require("dns")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},85158:e=>{"use strict";e.exports=require("http2")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},77282:e=>{"use strict";e.exports=require("process")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},56670:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=s(67096),a=s(16132),o=s(37284),i=s.n(o),n=s(32564),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);s.d(t,l);let c=["",{children:["import-chat",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52051)),"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\src\\app\\import-chat\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,58095)),"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9291,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\src\\app\\import-chat\\page.tsx"],m="/import-chat/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/import-chat/page",pathname:"/import-chat",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97862:(e,t,s)=>{Promise.resolve().then(s.bind(s,18736))},18736:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ImportChatPage});var r=s(30784),a=s(9885),o=s(78157),i=s(57114),n=s(29904),l=s(31640),c=s(72373);function ImportChatPage(){let{user:e,loading:t}=(0,o.useAuth)(),s=(0,i.useRouter)(),[d,m]=(0,a.useState)(!1),[u,p]=(0,a.useState)(""),[h,x]=(0,a.useState)([]),[g,f]=(0,a.useState)(""),[b,v]=(0,a.useState)(null),getUsernameFromFirestore=async()=>{if(!e?.email)return"unknown";try{let t=(0,n.collection)(c.db,"usuarios"),s=(0,n.query)(t,(0,n.where)("email","==",e.email)),r=await (0,n.getDocs)(s);if(!r.empty){let t=r.docs[0],s=t.data();return s.username||e.email.split("@")[0]}return e.email.split("@")[0]}catch(t){return console.error("Erro ao buscar username:",t),e.email.split("@")[0]}};(0,a.useEffect)(()=>{let loadUsername=async()=>{if(e?.email){let e=await getUsernameFromFirestore();f(e)}};loadUsername()},[e?.email]),(0,a.useEffect)(()=>{t||e||s.push("/login")},[e,t,s]);let generateChatId=()=>{let e=Date.now(),t=Math.random().toString(36).substring(2,8);return`chat_${e}_${t}`},importSingleChat=async e=>{try{if("application/json"!==e.type)return{filename:e.name,chatName:"",chatId:"",status:"error",message:"Arquivo deve ser JSON"};let t=await e.text(),s=JSON.parse(t),r=generateChatId(),a=new Date().toISOString(),o={context:s.context||"",createdAt:a,folderId:null,frequencyPenalty:s.frequency_penalty||1,isFixed:!1,lastUpdatedAt:a,lastUsedModel:s.lastUsedModel||"",latexInstructions:s.latexInstructions||!1,maxTokens:s.maxTokens||2048,name:s.name||"Chat Importado",password:"",repetitionPenalty:s.repetition_penalty||1,sessionTime:{lastSessionStart:null,lastUpdated:null,totalTime:0},systemPrompt:s.system_prompt||"",temperature:s.temperature||1,ultimaMensagem:s.messages.length>0?s.messages[s.messages.length-1].content.substring(0,100)+"...":"Chat importado",ultimaMensagemEm:s.messages.length>0?new Date(s.messages[s.messages.length-1].timestamp).toISOString():a,updatedAt:a};await (0,n.pl)((0,n.doc)(c.db,"usuarios",g,"conversas",r),o);let i={id:r,name:s.name||"Chat Importado",messages:s.messages.map(e=>({id:e.id,content:e.content,role:e.role,timestamp:e.timestamp,isFavorite:!1,attachments:[],...e.usage&&{usage:e.usage},...e.responseTime&&{responseTime:e.responseTime}})),createdAt:a,lastUpdated:a},d=new Blob([JSON.stringify(i,null,2)],{type:"application/json"}),m=(0,l.iH)(c.tO,`usuarios/${g}/conversas/${r}/chat.json`);return await (0,l.KV)(m,d),{filename:e.name,chatName:s.name||"Chat Importado",chatId:r,status:"success",message:"Importado com sucesso"}}catch(t){return console.error("Erro ao importar chat:",t),{filename:e.name,chatName:"",chatId:"",status:"error",message:t instanceof Error?t.message:"Erro desconhecido"}}},handleImportChats=async()=>{if(!b||0===b.length||!g)return;m(!0),x([]),p("\uD83D\uDD04 Iniciando importa\xe7\xe3o...");let e=[],t=b.length;for(let s=0;s<t;s++){let r=b[s];p(`🔄 Importando ${s+1}/${t}: ${r.name}`);let a=await importSingleChat(r);e.push(a),x([...e])}let s=e.filter(e=>"success"===e.status).length,r=e.filter(e=>"error"===e.status).length;p(`✅ Importa\xe7\xe3o conclu\xedda! ${s} sucesso(s), ${r} erro(s)`),m(!1),v(null);let a=document.getElementById("chat-files");a&&(a.value="")};return t?r.jsx("div",{className:"min-h-screen bg-gradient-rafthor flex items-center justify-center",children:r.jsx("div",{className:"text-white text-xl",children:"Carregando..."})}):e?r.jsx("div",{className:"min-h-screen bg-gradient-rafthor",children:r.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[r.jsx("h1",{className:"text-4xl font-bold text-white mb-4",children:"Importar Chat do Rafthor Anterior"}),r.jsx("p",{className:"text-white/80 text-lg",children:"Fa\xe7a upload do arquivo chat.json para importar suas conversas"}),(0,r.jsxs)("p",{className:"text-white/60 text-sm mt-2",children:["Usu\xe1rio: ",g]})]}),(0,r.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20",children:[(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"mb-6",children:r.jsx("svg",{className:"mx-auto h-16 w-16 text-white/60",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),r.jsx("label",{htmlFor:"chat-files",className:"cursor-pointer",children:r.jsx("div",{className:"bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors duration-200 inline-block",children:d?"Importando...":"Selecionar arquivos chat.json"})}),r.jsx("input",{id:"chat-files",type:"file",accept:".json",multiple:!0,onChange:e=>{let t=e.target.files;t&&t.length>0&&(v(t),x([]),p(`📁 ${t.length} arquivo(s) selecionado(s). Clique em "Importar" para continuar.`))},disabled:d,className:"hidden"}),r.jsx("p",{className:"text-white/60 text-sm mt-4",children:"Selecione um ou m\xfaltiplos arquivos .json exportados do Rafthor anterior"}),b&&b.length>0&&r.jsx("div",{className:"mt-4",children:r.jsx("button",{onClick:handleImportChats,disabled:d,className:"bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors duration-200",children:d?"Importando...":`Importar ${b.length} arquivo(s)`})})]}),u&&r.jsx("div",{className:"mt-6 p-4 bg-white/5 rounded-lg border border-white/10",children:r.jsx("p",{className:"text-white text-center",children:u})}),h.length>0&&(0,r.jsxs)("div",{className:"mt-6 space-y-2",children:[r.jsx("h4",{className:"text-white font-semibold mb-3",children:"\uD83D\uDCCA Resultados da Importa\xe7\xe3o:"}),r.jsx("div",{className:"max-h-60 overflow-y-auto space-y-2",children:h.map((e,t)=>r.jsx("div",{className:`p-3 rounded-lg border ${"success"===e.status?"bg-green-500/10 border-green-500/30 text-green-300":"bg-red-500/10 border-red-500/30 text-red-300"}`,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[r.jsx("p",{className:"font-medium",children:e.filename}),e.chatName&&(0,r.jsxs)("p",{className:"text-sm opacity-80",children:["Chat: ",e.chatName]}),r.jsx("p",{className:"text-sm opacity-80",children:e.message})]}),r.jsx("div",{className:"ml-4",children:"success"===e.status?"✅":"❌"})]})},t))})]})]}),(0,r.jsxs)("div",{className:"mt-8 bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10",children:[r.jsx("h3",{className:"text-white font-semibold mb-3",children:"\uD83D\uDCCB Instru\xe7\xf5es:"}),(0,r.jsxs)("ul",{className:"text-white/80 space-y-2 text-sm",children:[r.jsx("li",{children:"• Selecione um ou m\xfaltiplos arquivos chat.json do seu Rafthor anterior"}),r.jsx("li",{children:"• Voc\xea pode selecionar v\xe1rios arquivos de uma vez usando Ctrl+clique"}),r.jsx("li",{children:"• Cada chat ser\xe1 importado com um novo ID para evitar conflitos"}),r.jsx("li",{children:"• Todas as mensagens e configura\xe7\xf5es ser\xe3o preservadas"}),r.jsx("li",{children:"• Ap\xf3s a importa\xe7\xe3o, voc\xea pode acessar os chats no dashboard"}),r.jsx("li",{children:"• O processo mostrar\xe1 o resultado de cada arquivo individualmente"})]})]}),r.jsx("div",{className:"text-center mt-8",children:r.jsx("button",{onClick:()=>s.push("/dashboard"),className:"bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-lg transition-all duration-200 backdrop-blur-sm border border-white/30",children:"← Voltar ao Dashboard"})})]})})}):null}},52051:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>o,default:()=>l});var r=s(95153);let a=(0,r.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\app\import-chat\page.tsx`),{__esModule:o,$$typeof:i}=a,n=a.default,l=n},57114:(e,t,s)=>{e.exports=s(4979)}};var t=require("../../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),s=t.X(0,[332,700],()=>__webpack_exec__(56670));module.exports=s})();