{"version": 4, "routes": {"/login": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/login", "dataRoute": "/login.rsc"}, "/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}, "/import-chat": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/import-chat", "dataRoute": "/import-chat.rsc"}, "/dashboard": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data"}], "initialRevalidateSeconds": false, "srcRoute": "/dashboard", "dataRoute": "/dashboard.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "98aef2c55da349e0261b9cdc803bec48", "previewModeSigningKey": "b62bcd4198843e68f73a868aa18ee6c6cd4723aa56988c79b6e76f8eee97517f", "previewModeEncryptionKey": "a86781b4b3b8fc208d2c9ce7de6a848f28a272eb1d69ee000a76d0ba1b6ce71f"}}