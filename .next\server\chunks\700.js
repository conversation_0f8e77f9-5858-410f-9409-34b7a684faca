exports.id=700,exports.ids=[700],exports.modules={88517:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,13724,23)),Promise.resolve().then(a.t.bind(a,35365,23)),Promise.resolve().then(a.t.bind(a,44900,23)),Promise.resolve().then(a.t.bind(a,44714,23)),Promise.resolve().then(a.t.bind(a,45392,23)),Promise.resolve().then(a.t.bind(a,8898,23))},19456:(e,t,a)=>{Promise.resolve().then(a.bind(a,78634)),Promise.resolve().then(a.bind(a,78157))},78634:(e,t,a)=>{"use strict";a.r(t),a.d(t,{AppearanceProvider:()=>AppearanceProvider,useAppearance:()=>useAppearance});var r=a(30784),o=a(9885),s=a(29904),n=a(72373),i=a(78157);let l={fonte:"Inter",tamanho<PERSON>onte:14,palavrasPor<PERSON>essao:5e3,sessionsEnabled:!0},c=(0,o.createContext)(void 0),useAppearance=()=>{let e=(0,o.useContext)(c);if(!e)throw Error("useAppearance must be used within an AppearanceProvider");return e},getUsernameFromFirestore=async e=>{try{let{collection:t,query:r,where:o,getDocs:s}=await Promise.resolve().then(a.bind(a,29904)),i=t(n.db,"usuarios"),l=r(i,o("email","==",e)),c=await s(l);if(!c.empty){let t=c.docs[0],a=t.data();return a.username||e.split("@")[0]}return e.split("@")[0]}catch(t){return console.error("Erro ao obter username:",t),e.split("@")[0]}},AppearanceProvider=({children:e})=>{let{user:t}=(0,i.useAuth)(),[a,u]=(0,o.useState)(l),[d,p]=(0,o.useState)(!0),loadSettings=async()=>{if(!t?.email){u(l),p(!1);return}try{p(!0);let e=await getUsernameFromFirestore(t.email),a=(0,s.doc)(n.db,"usuarios",e,"configuracoes","settings"),r=await (0,s.getDoc)(a);if(r.exists()){let e=r.data();e.aparencia?u({fonte:e.aparencia.fonte||l.fonte,tamanhoFonte:e.aparencia.tamanhoFonte||l.tamanhoFonte,palavrasPorSessao:e.aparencia.palavrasPorSessao||l.palavrasPorSessao,sessionsEnabled:void 0!==e.aparencia.sessionsEnabled?e.aparencia.sessionsEnabled:l.sessionsEnabled}):u(l)}else u(l)}catch(e){console.error("Erro ao carregar configura\xe7\xf5es de apar\xeancia:",e),u(l)}finally{p(!1)}};(0,o.useEffect)(()=>{loadSettings()},[t]),(0,o.useEffect)(()=>{if(!d){let e=document.documentElement;e.style.setProperty("--chat-font-family",a.fonte),e.style.setProperty("--chat-font-size",`${a.tamanhoFonte}px`),e.style.setProperty("--chat-words-per-session",a.palavrasPorSessao.toString()),e.style.setProperty("--chat-sessions-enabled",a.sessionsEnabled?"1":"0")}},[a,d]);let updateSettings=async e=>{let r={...a,...e};if(u(r),t)try{let e=await getUsernameFromFirestore(t.email),a=(0,s.doc)(n.db,"usuarios",e,"configuracoes","settings"),o=await (0,s.getDoc)(a),i=o.exists()?o.data():{};await (0,s.pl)(a,{...i,aparencia:r,updatedAt:new Date().toISOString()},{merge:!0}),console.log("✅ Configura\xe7\xf5es de apar\xeancia salvas automaticamente")}catch(e){console.error("❌ Erro ao salvar configura\xe7\xf5es de apar\xeancia:",e)}};return r.jsx(c.Provider,{value:{settings:a,updateSettings,isLoading:d,applyToElement:e=>{e.style.fontFamily=a.fonte,e.style.fontSize=`${a.tamanhoFonte}px`},getCSSVariables:()=>({"--chat-font-family":a.fonte,"--chat-font-size":`${a.tamanhoFonte}px`,"--chat-words-per-session":a.palavrasPorSessao.toString()})},children:e})}},78157:(e,t,a)=>{"use strict";a.r(t),a.d(t,{AuthProvider:()=>AuthProvider,useAuth:()=>useAuth});var r=a(30784),o=a(9885),s=a(11766),n=a(72373);let i=(0,o.createContext)({user:null,loading:!0,logout:async()=>{}}),useAuth=()=>{let e=(0,o.useContext)(i);if(!e)throw Error("useAuth must be used within an AuthProvider");return e},AuthProvider=({children:e})=>{let[t,a]=(0,o.useState)(null),[l,c]=(0,o.useState)(!0);(0,o.useEffect)(()=>{let e=(0,s.Aj)(n.I8,e=>{a(e),c(!1)});return()=>e()},[]);let logout=async()=>{try{await (0,s.w7)(n.I8)}catch(e){console.error("Erro ao fazer logout:",e)}};return r.jsx(i.Provider,{value:{user:t,loading:l,logout},children:e})}},72373:(e,t,a)=>{"use strict";a.d(t,{I8:()=>u,db:()=>d,tO:()=>p});var r=a(72856),o=a(11766),s=a(29904),n=a(31640),i=a(38635);let l={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!l.apiKey||l.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let c=(0,r.ZF)(l),u=(0,o.v0)(c),d=(0,s.ad)(c),p=(0,n.cF)(c);(0,i.$C)(c)},58095:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>RootLayout,metadata:()=>m});var r=a(4656),o=a(40186),s=a.n(o);a(5023);var n=a(95153);let i=(0,n.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\contexts\AuthContext.tsx`),{__esModule:l,$$typeof:c}=i;i.default,i.useAuth;let u=i.AuthProvider,d=(0,n.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\contexts\AppearanceContext.tsx`),{__esModule:p,$$typeof:h}=d;d.default,d.useAppearance;let f=d.AppearanceProvider,m={title:"Rafthor - AI Chatbot Platform",description:"Uma plataforma de chatbot com m\xfaltiplas IAs"};function RootLayout({children:e}){return r.jsx("html",{lang:"pt-BR",children:r.jsx("body",{className:s().className,children:r.jsx(u,{children:r.jsx(f,{children:e})})})})}},5023:()=>{}};