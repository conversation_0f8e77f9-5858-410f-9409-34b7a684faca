"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const MODELS_PER_PAGE = 10;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Load models when endpoint changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n                // Extrair favoritos do cache\n                const cachedFavorites = new Set(cachedData.models.filter((m)=>m.isFavorite).map((m)=>m.id));\n                setFavoriteModelIds(cachedFavorites);\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite: \".concat(model.id, \" (\").concat(model.isFavorite ? \"removing\" : \"adding\", \")\"));\n        // Calcular o novo status otimisticamente\n        const optimisticNewStatus = !model.isFavorite;\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            // ATUALIZAÇÃO OTIMISTA: Atualizar a UI imediatamente\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (optimisticNewStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Atualizar o array de modelos imediatamente\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: optimisticNewStatus\n                    } : m));\n            // Agora fazer a operação no Firestore\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            // Se o status real for diferente do otimista, corrigir\n            if (actualNewStatus !== optimisticNewStatus) {\n                console.warn(\"Optimistic update was incorrect, correcting...\");\n                // Corrigir o estado dos favoritos\n                const correctedFavoriteIds = new Set(favoriteModelIds);\n                if (actualNewStatus) {\n                    correctedFavoriteIds.add(model.id);\n                } else {\n                    correctedFavoriteIds.delete(model.id);\n                }\n                setFavoriteModelIds(correctedFavoriteIds);\n                // Corrigir o array de modelos\n                setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                            ...m,\n                            isFavorite: actualNewStatus\n                        } : m));\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            // Em caso de erro, reverter a atualização otimista\n            const revertedFavoriteIds = new Set(favoriteModelIds);\n            if (!optimisticNewStatus) {\n                revertedFavoriteIds.add(model.id);\n            } else {\n                revertedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(revertedFavoriteIds);\n            // Reverter o array de modelos\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: !optimisticNewStatus\n                    } : m));\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-blue-100\",\n                                            children: \"Selecionar Modelo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-blue-200\",\n                                                    children: \"Endpoint:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                                    onChange: (e)=>{\n                                                        const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                                        setSelectedEndpoint(endpoint || null);\n                                                    },\n                                                    className: \"bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-3 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Selecione um endpoint\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: endpoint.id,\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: endpoint.name\n                                                            }, endpoint.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleRefreshModels,\n                                                    disabled: loading || !selectedEndpoint,\n                                                    className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    title: \"Atualizar modelos\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full max-h-[calc(90vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 border-r border-blue-700/30 bg-blue-900/20 backdrop-blur-sm relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 space-y-4\",\n                                    children: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3\",\n                                                children: \"Categorias\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            [\n                                                \"paid\",\n                                                \"free\",\n                                                \"favorites\"\n                                            ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category\n                                                            })),\n                                                    className: \"w-full text-left py-3 px-4 rounded-xl text-sm font-medium transition-all duration-200 flex items-center space-x-3 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col\",\n                                children: [\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-blue-700/30 space-y-6 relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-blue-400\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Modelo Customizado\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"openai/gpt-4-1\",\n                                                                        value: customModelId,\n                                                                        onChange: (e)=>setCustomModelId(e.target.value),\n                                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                                        className: \"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleUseCustomModel,\n                                                                        disabled: !customModelId.trim(),\n                                                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100\",\n                                                                        children: \"Usar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-300/70 mt-3\",\n                                                                children: \"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            value: searchTerm,\n                                                                            onChange: setSearchTerm,\n                                                                            suggestions: [],\n                                                                            isSearching: isSearching,\n                                                                            placeholder: \"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')\",\n                                                                            showSuggestions: false\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: filters.sortBy,\n                                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                                    ...prev,\n                                                                                    sortBy: e.target.value\n                                                                                })),\n                                                                        className: \"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                                        disabled: hasSearched && searchTerm.trim().length > 0,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"newest\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Mais recentes\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 591,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"price_low\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Menor pre\\xe7o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"price_high\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Maior pre\\xe7o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"context_high\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Maior contexto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setSelectedCategory(null),\n                                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 \".concat(!selectedCategory ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                                        children: \"Todos\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    smartCategories.slice(0, 6).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setSelectedCategory(category.id),\n                                                                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 \".concat(selectedCategory === category.id ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                                            title: category.description,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: category.icon\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 622,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: category.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 623,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, category.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 27\n                                                                        }, undefined))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-300\",\n                                                                        children: [\n                                                                            filteredModels.length,\n                                                                            \" resultado\",\n                                                                            filteredModels.length !== 1 ? \"s\" : \"\",\n                                                                            ' para \"',\n                                                                            searchTerm,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: clearSearch,\n                                                                        className: \"text-blue-400 hover:text-blue-300 transition-colors duration-200\",\n                                                                        children: \"Limpar busca\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10\",\n                                                children: [\n                                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center py-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                                    children: \"Carregando modelos...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-red-400\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 662,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 661,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-300 font-medium\",\n                                                                    children: error\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-8 h-8 text-blue-400\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-300 font-medium\",\n                                                                children: hasSearched && searchTerm.trim() ? 'Nenhum resultado para \"'.concat(searchTerm, '\"') : selectedCategory ? \"Nenhum modelo na categoria selecionada\" : \"Nenhum modelo encontrado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-blue-400/70 text-sm mt-2 space-y-1\",\n                                                                children: hasSearched && searchTerm.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"Tente:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"list-disc list-inside space-y-1 text-left max-w-xs mx-auto\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Verificar a ortografia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 690,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Usar termos mais gen\\xe9ricos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Explorar as categorias\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 692,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Limpar filtros ativos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 693,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 689,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Tente ajustar os filtros ou usar as categorias\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: filteredModels.slice(0, displayedModelsCount).map((model)=>{\n                                                            // Encontrar o resultado da busca para este modelo (se houver)\n                                                            const searchResult = hasSearched ? searchResults.find((r)=>r.model.id === model.id) : null;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                                model: model,\n                                                                isSelected: currentModel === model.id,\n                                                                onSelect: ()=>handleSelectModel(model),\n                                                                onToggleFavorite: ()=>handleToggleFavorite(model),\n                                                                isToggling: favoriteToggling.has(model.id),\n                                                                service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                                searchTerm: hasSearched ? searchTerm : \"\",\n                                                                searchResult: searchResult\n                                                            }, model.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 25\n                                                            }, undefined);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center mt-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLoadMoreModels,\n                                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Carregar mais modelos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mt-4 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-blue-400/70\",\n                                                                children: [\n                                                                    \"Mostrando \",\n                                                                    Math.min(displayedModelsCount, filteredModels.length),\n                                                                    \" de \",\n                                                                    filteredModels.length,\n                                                                    \" modelos\",\n                                                                    models.length !== filteredModels.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-blue-300\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            models.length,\n                                                                            \" total)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center space-x-4 text-xs text-blue-400/60\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDD0D Busca: \",\n                                                                            searchTerm\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"⚡ \",\n                                                                            searchResults.length,\n                                                                            \" resultados\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-300\",\n                                            children: \"DeepSeek models aqui...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\" && (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"DeepSeek\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8 text-center relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-300 font-medium\",\n                                                    children: \"Sele\\xe7\\xe3o de modelos dispon\\xedvel para OpenRouter e DeepSeek\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-400/70 text-sm mt-1\",\n                                                    children: \"Selecione um desses endpoints para ver os modelos dispon\\xedveis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: handleCancelExpensiveModel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 790,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 450,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"rgm6jqAl28tIalE5FEhXMTtefrA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service === _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService && _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-model-id\": model.id,\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 836,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 835,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 834,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-blue-100 truncate\",\n                                                        children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedName) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: searchResult.highlightedName\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                            text: model.name,\n                                                            highlight: searchTerm\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 21\n                                                        }, undefined) : model.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium\",\n                                                        children: \"CARO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    searchResult && searchResult.matchedFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: searchResult.matchedFields.slice(0, 3).map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30\",\n                                                                title: \"Encontrado em: \".concat(field),\n                                                                children: field === \"name\" ? \"\\uD83D\\uDCDD\" : field === \"description\" ? \"\\uD83D\\uDCC4\" : field === \"provider\" ? \"\\uD83C\\uDFE2\" : field === \"tags\" ? \"\\uD83C\\uDFF7️\" : \"\\uD83D\\uDD0D\"\n                                                            }, field, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                    text: model.id,\n                                                    highlight: searchTerm\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    service.isFreeModel(model) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium\",\n                                        children: \"Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-300/80 line-clamp-2\",\n                                    children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedDescription) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: searchResult.highlightedDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 19\n                                    }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                        text: model.description,\n                                        highlight: searchTerm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 19\n                                    }, undefined) : model.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 903,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 844,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed \".concat(model.isFavorite ? \"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30\"),\n                                title: isToggling ? \"Processando...\" : model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 918,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 843,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 825,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});