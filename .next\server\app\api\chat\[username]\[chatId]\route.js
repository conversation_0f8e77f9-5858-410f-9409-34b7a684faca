"use strict";(()=>{var e={};e.id=361,e.ids=[361],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},6113:e=>{e.exports=require("crypto")},9523:e=>{e.exports=require("dns")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},85158:e=>{e.exports=require("http2")},41808:e=>{e.exports=require("net")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},77282:e=>{e.exports=require("process")},12781:e=>{e.exports=require("stream")},24404:e=>{e.exports=require("tls")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},39835:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>T,requestAsyncStorage:()=>l,routeModule:()=>u,serverHooks:()=>d,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>A});var o={};t.r(o),t.d(o,{GET:()=>GET,OPTIONS:()=>OPTIONS}),t(78976);var s=t(10884),a=t(16132),n=t(95798),i=t(37723),c=t(83479);async function GET(e,{params:r}){try{let{username:e,chatId:t}=r;if(!e||!t)return n.Z.json({error:"Username e chatId s\xe3o obrigat\xf3rios"},{status:400});let o=(0,i.iH)(c.tO,`usuarios/${e}/conversas/${t}/chat.json`);try{let e=await (0,i.Jt)(o),r=await fetch(e);if(!r.ok)throw Error(`Erro ao buscar arquivo: ${r.statusText}`);let t=await r.json();return n.Z.json(t,{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}catch(r){console.error("Erro ao acessar Firebase Storage:",r);let e={id:t,name:"Chat",messages:[],createdAt:new Date().toISOString(),lastUpdated:new Date().toISOString()};return n.Z.json(e,{headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}}catch(e){return console.error("Erro na API de chat:",e),n.Z.json({error:"Erro interno do servidor"},{status:500,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}}async function OPTIONS(){return new n.Z(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, PUT, DELETE, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let u=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/chat/[username]/[chatId]/route",pathname:"/api/chat/[username]/[chatId]",filename:"route",bundlePath:"app/api/chat/[username]/[chatId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\Rafthor\\RafthorIA\\src\\app\\api\\chat\\[username]\\[chatId]\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:d,headerHooks:h,staticGenerationBailout:A}=u,T="/api/chat/[username]/[chatId]/route"}};var r=require("../../../../../webpack-runtime.js");r.C(e);var __webpack_exec__=e=>r(r.s=e),t=r.X(0,[955,818,479],()=>__webpack_exec__(39835));module.exports=t})();