{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/chat/[username]/[chatId]", "regex": "^/api/chat/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername", "nxtPchatId": "nxtPchatId"}, "namedRegex": "^/api/chat/(?<nxtPusername>[^/]+?)/(?<nxtPchatId>[^/]+?)(?:/)?$"}, {"page": "/api/chat/[username]/[chatId]/message/[messageId]", "regex": "^/api/chat/([^/]+?)/([^/]+?)/message/([^/]+?)(?:/)?$", "routeKeys": {"nxtPusername": "nxtPusername", "nxtPchatId": "nxtPchatId", "nxtPmessageId": "nxtPmessageId"}, "namedRegex": "^/api/chat/(?<nxtPusername>[^/]+?)/(?<nxtPchatId>[^/]+?)/message/(?<nxtPmessageId>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/import-chat", "regex": "^/import\\-chat(?:/)?$", "routeKeys": {}, "namedRegex": "^/import\\-chat(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "contentTypeHeader": "text/x-component"}, "rewrites": []}