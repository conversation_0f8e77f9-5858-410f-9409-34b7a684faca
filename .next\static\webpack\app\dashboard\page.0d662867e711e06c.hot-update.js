"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Load models when endpoint changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n                // Extrair favoritos do cache\n                const cachedFavorites = new Set(cachedData.models.filter((m)=>m.isFavorite).map((m)=>m.id));\n                setFavoriteModelIds(cachedFavorites);\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const filteredModels = getFilteredModels();\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-blue-100\",\n                                            children: \"Selecionar Modelo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-blue-200\",\n                                                    children: \"Endpoint:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                                    onChange: (e)=>{\n                                                        const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                                        setSelectedEndpoint(endpoint || null);\n                                                    },\n                                                    className: \"bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-3 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Selecione um endpoint\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: endpoint.id,\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: endpoint.name\n                                                            }, endpoint.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 21\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleRefreshModels,\n                                                    disabled: loading || !selectedEndpoint,\n                                                    className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    title: \"Atualizar modelos\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 309,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full max-h-[calc(90vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 border-r border-blue-700/30 bg-blue-900/20 backdrop-blur-sm relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 space-y-4\",\n                                    children: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3\",\n                                                children: \"Categorias\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            [\n                                                \"paid\",\n                                                \"free\",\n                                                \"favorites\"\n                                            ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category\n                                                            })),\n                                                    className: \"w-full text-left py-3 px-4 rounded-xl text-sm font-medium transition-all duration-200 flex items-center space-x-3 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-300\",\n                                        children: \"Conte\\xfado principal aqui...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: ()=>{},\n                onCancel: ()=>{}\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 263,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"jlw8EWqkrBmgQiVJdSRGhw1B1SQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9Nb2RlbFNlbGVjdGlvbk1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFNEM7QUFDSztBQUNiO0FBQ21EO0FBQ2xCO0FBQ0o7QUFDWTtBQUVmO0FBQ2dDO0FBRWQ7QUFTaEYsd0JBQXdCO0FBQ3hCLE1BQU1XLGlCQUFpQixJQUFJLEtBQUssTUFBTSxZQUFZO0FBQ2xELE1BQU1DLDJCQUEyQixLQUFLLEtBQUssTUFBTSxhQUFhO0FBRTlELE1BQU1DLHNCQUFzQjtRQUFDLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUVDLGFBQWEsRUFBNEI7O0lBQ3JHLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdoQiw4REFBT0E7SUFDeEIsTUFBTSxDQUFDaUIsV0FBV0MsYUFBYSxHQUFHcEIsK0NBQVFBLENBQWdCLEVBQUU7SUFDNUQsTUFBTSxDQUFDcUIsa0JBQWtCQyxvQkFBb0IsR0FBR3RCLCtDQUFRQSxDQUFxQjtJQUM3RSxNQUFNLENBQUN1QixRQUFRQyxVQUFVLEdBQUd4QiwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ2xELE1BQU0sQ0FBQ3lCLGtCQUFrQkMsb0JBQW9CLEdBQUcxQiwrQ0FBUUEsQ0FBYyxJQUFJMkI7SUFDMUUsTUFBTSxDQUFDQyxTQUFTQyxXQUFXLEdBQUc3QiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM4QixPQUFPQyxTQUFTLEdBQUcvQiwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDZ0Msc0JBQXNCQyx3QkFBd0IsR0FBR2pDLCtDQUFRQSxDQUFDO0lBQ2pFLE1BQU1rQyxrQkFBa0I7SUFDeEIsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR3BDLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ3FDLHlCQUF5QkMsMkJBQTJCLEdBQUd0QywrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUN1Qyx1QkFBdUJDLHlCQUF5QixHQUFHeEMsK0NBQVFBLENBQWlCO0lBQ25GLE1BQU0sQ0FBQ3lDLGtCQUFrQkMsb0JBQW9CLEdBQUcxQywrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDMkMsaUJBQWlCQyxtQkFBbUIsR0FBRzVDLCtDQUFRQSxDQUFrQixFQUFFO0lBQzFFLE1BQU0sQ0FBQzZDLGtCQUFrQkMsb0JBQW9CLEdBQUc5QywrQ0FBUUEsQ0FBYyxJQUFJMkI7SUFDMUUsTUFBTSxDQUFDb0Isb0JBQW9CQyxzQkFBc0IsR0FBR2hELCtDQUFRQSxDQUFnQjtJQUM1RSxNQUFNLENBQUNpRCxhQUFhQyxlQUFlLEdBQUdsRCwrQ0FBUUEsQ0FBd0QsSUFBSW1EO0lBQzFHLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR3JELCtDQUFRQSxDQUF5RDtJQUU3RyxNQUFNLENBQUNzRCxTQUFTQyxXQUFXLEdBQUd2RCwrQ0FBUUEsQ0FBZTtRQUNuRHdELFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxZQUFZO0lBQ2Q7SUFFQSx5QkFBeUI7SUFDekIsTUFBTSxFQUNKQSxVQUFVLEVBQ1ZDLGFBQWEsRUFDYkMsYUFBYSxFQUNiQyxXQUFXLEVBQ1hDLFdBQVcsRUFDWEMsV0FBVyxFQUNYQyxXQUFXLEVBQ1osR0FBR3hELDJFQUFpQkEsQ0FBQ2UsUUFBUTtRQUM1QjBDLFlBQVk7UUFDWkMsbUJBQW1CO1FBQ25CQyxjQUFjO1FBQ2RDLGdCQUFnQjtRQUNoQkMsWUFBWTtRQUNaQyxnQkFBZ0I7SUFDbEI7SUFFQSwyQ0FBMkM7SUFDM0NyRSxnREFBU0EsQ0FBQztRQUNSLElBQUlpQixRQUFRSixRQUFRO1lBQ2xCLGtDQUFrQztZQUNsQyxJQUFJc0Msa0JBQWtCbUIsS0FBS0MsR0FBRyxLQUFLcEIsZUFBZXFCLFNBQVMsR0FBRzdELDBCQUEwQjtnQkFDdEZRLGFBQWFnQyxlQUFlakMsU0FBUztnQkFFckMsd0RBQXdEO2dCQUN4RCxJQUFJLENBQUNFLG9CQUFvQitCLGVBQWVqQyxTQUFTLENBQUN1RCxNQUFNLEdBQUcsR0FBRztvQkFDNUQsTUFBTUMscUJBQXFCdkIsZUFBZWpDLFNBQVMsQ0FBQ3lELElBQUksQ0FBQ0MsQ0FBQUEsS0FBTUEsR0FBR0MsSUFBSSxLQUFLO29CQUMzRSxNQUFNQyxtQkFBbUIzQixlQUFlakMsU0FBUyxDQUFDeUQsSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHQyxJQUFJLEtBQUs7b0JBRXpFLElBQUlILG9CQUFvQjt3QkFDdEJyRCxvQkFBb0JxRDtvQkFDdEIsT0FBTyxJQUFJSSxrQkFBa0I7d0JBQzNCekQsb0JBQW9CeUQ7b0JBQ3RCLE9BQU87d0JBQ0x6RCxvQkFBb0I4QixlQUFlakMsU0FBUyxDQUFDLEVBQUU7b0JBQ2pEO2dCQUNGO1lBQ0YsT0FBTztnQkFDTDZEO1lBQ0Y7UUFDRjtJQUNGLEdBQUc7UUFBQzlEO1FBQU1KO0tBQU87SUFFakIsb0NBQW9DO0lBQ3BDYixnREFBU0EsQ0FBQztRQUNSLElBQUlvQixrQkFBa0I7WUFDcEIsTUFBTTRELFdBQVcsR0FBMEI1RCxPQUF2QkEsaUJBQWlCNkQsRUFBRSxFQUFDLEtBQXlCLE9BQXRCN0QsaUJBQWlCeUQsSUFBSTtZQUNoRSxNQUFNSyxhQUFhbEMsWUFBWW1DLEdBQUcsQ0FBQ0g7WUFFbkMscURBQXFEO1lBQ3JELElBQUlFLGNBQWNaLEtBQUtDLEdBQUcsS0FBS1csV0FBV1YsU0FBUyxHQUFHOUQsZ0JBQWdCO2dCQUNwRWEsVUFBVTJELFdBQVc1RCxNQUFNO2dCQUMzQnlCLHNCQUFzQjNCLGlCQUFpQjZELEVBQUU7Z0JBRXpDLDZCQUE2QjtnQkFDN0IsTUFBTUcsa0JBQWtCLElBQUkxRCxJQUMxQndELFdBQVc1RCxNQUFNLENBQUMrRCxNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVDLFVBQVUsRUFBRUMsR0FBRyxDQUFDRixDQUFBQSxJQUFLQSxFQUFFTCxFQUFFO2dCQUUzRHhELG9CQUFvQjJEO1lBQ3RCLE9BQU87Z0JBQ0wsMERBQTBEO2dCQUMxRCxJQUFJdEMsdUJBQXVCMUIsaUJBQWlCNkQsRUFBRSxJQUFJLENBQUNDLFlBQVk7b0JBQzdELElBQUk5RCxpQkFBaUJ5RCxJQUFJLEtBQUssY0FBYzt3QkFDMUNZO29CQUNGLE9BQU8sSUFBSXJFLGlCQUFpQnlELElBQUksS0FBSyxZQUFZO3dCQUMvQ2E7b0JBQ0Y7Z0JBQ0Y7WUFDRjtRQUNGO0lBQ0YsR0FBRztRQUFDdEU7UUFBa0IwQjtRQUFvQkU7S0FBWTtJQUV0RCx3QkFBd0I7SUFDeEJoRCxnREFBU0EsQ0FBQztRQUNSMkMsbUJBQW1CbkMsd0ZBQXNCQSxDQUFDbUYsa0JBQWtCO0lBQzlELEdBQUcsRUFBRTtJQUVMLGlEQUFpRDtJQUNqRCxNQUFNQywyQkFBMkI7UUFDL0IsSUFBSSxFQUFDM0UsaUJBQUFBLDJCQUFBQSxLQUFNNEUsS0FBSyxHQUFFLE9BQU87UUFFekIsSUFBSTtZQUNGLE1BQU0sRUFBRUMsVUFBVSxFQUFFQyxLQUFLLEVBQUVDLEtBQUssRUFBRUMsT0FBTyxFQUFFLEdBQUcsTUFBTSw2TEFBTztZQUMzRCxNQUFNQyxjQUFjSixXQUFXNUYsNkNBQUVBLEVBQUU7WUFDbkMsTUFBTWlHLElBQUlKLE1BQU1HLGFBQWFGLE1BQU0sU0FBUyxNQUFNL0UsS0FBSzRFLEtBQUs7WUFDNUQsTUFBTU8sZ0JBQWdCLE1BQU1ILFFBQVFFO1lBRXBDLElBQUksQ0FBQ0MsY0FBY0MsS0FBSyxFQUFFO2dCQUN4QixNQUFNQyxVQUFVRixjQUFjRyxJQUFJLENBQUMsRUFBRTtnQkFDckMsT0FBT0QsUUFBUUUsSUFBSSxHQUFHQyxRQUFRLElBQUlILFFBQVFyQixFQUFFO1lBQzlDO1lBRUEsT0FBTztRQUNULEVBQUUsT0FBT3BELE9BQU87WUFDZDZFLFFBQVE3RSxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQyxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1rRCxnQkFBZ0I7UUFDcEIsSUFBSSxDQUFDOUQsTUFBTTtZQUNUeUYsUUFBUUMsR0FBRyxDQUFDO1lBQ1o7UUFDRjtRQUVBL0UsV0FBVztRQUNYRSxTQUFTO1FBRVQsSUFBSTtZQUNGLE1BQU0yRSxXQUFXLE1BQU1iO1lBQ3ZCLE1BQU1nQixnQkFBZ0IsTUFBTXpHLGtGQUFtQkEsQ0FBQ3NHO1lBRWhELGtCQUFrQjtZQUNsQnJELGtCQUFrQjtnQkFDaEJsQyxXQUFXMEY7Z0JBQ1hwQyxXQUFXRixLQUFLQyxHQUFHO1lBQ3JCO1lBRUFwRCxhQUFheUY7WUFFYixzRUFBc0U7WUFDdEUsTUFBTWxDLHFCQUFxQmtDLGNBQWNqQyxJQUFJLENBQUNDLENBQUFBLEtBQU1BLEdBQUdDLElBQUksS0FBSztZQUNoRSxNQUFNQyxtQkFBbUI4QixjQUFjakMsSUFBSSxDQUFDQyxDQUFBQSxLQUFNQSxHQUFHQyxJQUFJLEtBQUs7WUFFOUQsSUFBSUgsb0JBQW9CO2dCQUN0QnJELG9CQUFvQnFEO1lBQ3RCLE9BQU8sSUFBSUksa0JBQWtCO2dCQUMzQnpELG9CQUFvQnlEO1lBQ3RCLE9BQU8sSUFBSThCLGNBQWNuQyxNQUFNLEdBQUcsR0FBRztnQkFDbkNwRCxvQkFBb0J1RixhQUFhLENBQUMsRUFBRTtZQUN0QztRQUNGLEVBQUUsT0FBTy9FLE9BQU87WUFDZDZFLFFBQVE3RSxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQ0MsU0FBUyxpQ0FBaUMsTUFBaUIrRSxPQUFPO1FBQ3BFLFNBQVU7WUFDUmpGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTZELHVCQUF1QjtRQUMzQixJQUFJLENBQUNyRSxvQkFBb0IsQ0FBQ0gsTUFBTTtRQUVoQ1csV0FBVztRQUNYRSxTQUFTO1FBRVQsSUFBSTtZQUNGLDhCQUE4QjtZQUM5QixNQUFNZ0YsbUJBQW1CLE1BQU0xRyw4RUFBaUJBLENBQUMyRyxXQUFXO1lBRTVELDBCQUEwQjtZQUMxQixNQUFNTixXQUFXLE1BQU1iO1lBQ3ZCLE1BQU1vQixjQUFjLE1BQU0xRyxzRkFBcUJBLENBQUMyRyxtQkFBbUIsQ0FBQ1IsVUFBVXJGLGlCQUFpQjZELEVBQUU7WUFFakcsdUJBQXVCO1lBQ3ZCLE1BQU1pQyxzQkFBc0JKLGlCQUFpQnRCLEdBQUcsQ0FBQzJCLENBQUFBLFFBQVU7b0JBQ3pELEdBQUdBLEtBQUs7b0JBQ1I1QixZQUFZeUIsWUFBWUksR0FBRyxDQUFDRCxNQUFNbEMsRUFBRTtnQkFDdEM7WUFFQSxrQkFBa0I7WUFDbEIsTUFBTUQsV0FBVyxHQUEwQjVELE9BQXZCQSxpQkFBaUI2RCxFQUFFLEVBQUMsS0FBeUIsT0FBdEI3RCxpQkFBaUJ5RCxJQUFJO1lBQ2hFNUIsZUFBZW9FLENBQUFBLE9BQVEsSUFBSW5FLElBQUltRSxNQUFNQyxHQUFHLENBQUN0QyxVQUFVO29CQUNqRDFELFFBQVE0RjtvQkFDUjFDLFdBQVdGLEtBQUtDLEdBQUc7Z0JBQ3JCO1lBRUFoRCxVQUFVMkY7WUFDVnpGLG9CQUFvQnVGO1lBQ3BCakUsc0JBQXNCM0IsaUJBQWlCNkQsRUFBRTtRQUMzQyxFQUFFLE9BQU9wRCxPQUFPO1lBQ2Q2RSxRQUFRN0UsS0FBSyxDQUFDLHlCQUF5QkE7WUFDdkNDLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTThELHFCQUFxQjtRQUN6QixJQUFJLENBQUN0RSxvQkFBb0IsQ0FBQ0gsTUFBTTtRQUVoQ1csV0FBVztRQUNYRSxTQUFTO1FBRVQsSUFBSTtZQUNGLDRCQUE0QjtZQUM1QixNQUFNeUYsaUJBQWlCLE1BQU1sSCwwRUFBZUEsQ0FBQzBHLFdBQVc7WUFFeEQsa0JBQWtCO1lBQ2xCLE1BQU0vQixXQUFXLEdBQTBCNUQsT0FBdkJBLGlCQUFpQjZELEVBQUUsRUFBQyxLQUF5QixPQUF0QjdELGlCQUFpQnlELElBQUk7WUFDaEU1QixlQUFlb0UsQ0FBQUEsT0FBUSxJQUFJbkUsSUFBSW1FLE1BQU1DLEdBQUcsQ0FBQ3RDLFVBQVU7b0JBQ2pEMUQsUUFBUWlHO29CQUNSL0MsV0FBV0YsS0FBS0MsR0FBRztnQkFDckI7WUFFQWhELFVBQVVnRztZQUNWeEUsc0JBQXNCM0IsaUJBQWlCNkQsRUFBRTtRQUMzQyxFQUFFLE9BQU9wRCxPQUFPO1lBQ2Q2RSxRQUFRN0UsS0FBSyxDQUFDLGtDQUFrQ0E7WUFDaERDLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTRGLGlCQUFpQkM7SUFFdkIsSUFBSSxDQUFDNUcsUUFBUSxPQUFPO0lBRXBCLHFCQUNFLDhEQUFDNkc7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FFYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FHZiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQ0M7Z0RBQUlELFdBQVU7Z0RBQXFCRSxNQUFLO2dEQUFPQyxRQUFPO2dEQUFlQyxTQUFROzBEQUM1RSw0RUFBQ0M7b0RBQUtDLGVBQWM7b0RBQVFDLGdCQUFlO29EQUFRQyxhQUFhO29EQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NEQUd6RSw4REFBQ0M7NENBQUdWLFdBQVU7c0RBQXNDOzs7Ozs7Ozs7Ozs7OENBRXRELDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBRWIsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ1c7b0RBQU1YLFdBQVU7OERBQW9DOzs7Ozs7OERBQ3JELDhEQUFDWTtvREFDQ0MsT0FBT3BILENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCNkQsRUFBRSxLQUFJO29EQUMvQndELFVBQVUsQ0FBQ0M7d0RBQ1QsTUFBTUMsV0FBV3pILFVBQVV5RCxJQUFJLENBQUNDLENBQUFBLEtBQU1BLEdBQUdLLEVBQUUsS0FBS3lELEVBQUVFLE1BQU0sQ0FBQ0osS0FBSzt3REFDOURuSCxvQkFBb0JzSCxZQUFZO29EQUNsQztvREFDQWhCLFdBQVU7O3NFQUVWLDhEQUFDa0I7NERBQU9MLE9BQU07c0VBQUc7Ozs7Ozt3REFDaEJ0SCxVQUFVc0UsR0FBRyxDQUFDbUQsQ0FBQUEseUJBQ2IsOERBQUNFO2dFQUF5QkwsT0FBT0csU0FBUzFELEVBQUU7Z0VBQUUwQyxXQUFVOzBFQUNyRGdCLFNBQVM5RCxJQUFJOytEQURIOEQsU0FBUzFELEVBQUU7Ozs7Ozs7Ozs7OzhEQUs1Qiw4REFBQzZEO29EQUNDQyxTQUFTQztvREFDVEMsVUFBVXRILFdBQVcsQ0FBQ1A7b0RBQ3RCdUcsV0FBVTtvREFDVnVCLE9BQU07OERBRU4sNEVBQUN0Qjt3REFBSUQsV0FBVyxXQUF5QyxPQUE5QmhHLFVBQVUsaUJBQWlCO3dEQUFNa0csTUFBSzt3REFBT0MsUUFBTzt3REFBZUMsU0FBUTtrRUFDcEcsNEVBQUNDOzREQUFLQyxlQUFjOzREQUFRQyxnQkFBZTs0REFBUUMsYUFBYTs0REFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFJM0UsOERBQUNVOzRDQUNDQyxTQUFTakk7NENBQ1Q2RyxXQUFVO3NEQUVWLDRFQUFDQztnREFBSUQsV0FBVTtnREFBVUUsTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDakUsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVEvRSw4REFBQ1Y7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7OENBRVp2RyxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQnlELElBQUksTUFBSyw4QkFDMUIsOERBQUM2Qzt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUN3QjtnREFBR3hCLFdBQVU7MERBQXlDOzs7Ozs7NENBQ3JEO2dEQUFDO2dEQUFRO2dEQUFROzZDQUFZLENBQXFCbkMsR0FBRyxDQUFDakMsQ0FBQUEseUJBQ3RELDhEQUFDdUY7b0RBRUNDLFNBQVMsSUFBTXpGLFdBQVcrRCxDQUFBQSxPQUFTO2dFQUFFLEdBQUdBLElBQUk7Z0VBQUU5RDs0REFBUztvREFDdkRvRSxXQUFXLHFIQUlWLE9BSEN0RSxRQUFRRSxRQUFRLEtBQUtBLFdBQ2pCLG9FQUNBOztzRUFHTiw4REFBQ21FOzREQUFJQyxXQUFVOzs7Ozs7c0VBQ2YsOERBQUN5QjtzRUFBTTdGLGFBQWEsU0FBUyxVQUFVQSxhQUFhLFNBQVMsY0FBVzs7Ozs7OzttREFUbkVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBa0JqQiw4REFBQ21FO2dDQUFJQyxXQUFVOzBDQUViLDRFQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQzBCO3dDQUFFMUIsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT3JDLDhEQUFDbEgseUVBQStCQTtnQkFDOUJJLFFBQVF1QjtnQkFDUitFLE9BQU83RTtnQkFDUGdILFdBQVcsS0FBTztnQkFDbEJDLFVBQVUsS0FBTzs7Ozs7Ozs7Ozs7O0FBSXpCO0dBclZNM0k7O1FBQ2FYLDBEQUFPQTtRQWtDcEJNLHVFQUFpQkE7OztLQW5DakJLO0FBdVZOLCtEQUFlQSxtQkFBbUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvZGFzaGJvYXJkL01vZGVsU2VsZWN0aW9uTW9kYWwudHN4P2FhZDYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xuaW1wb3J0IHsgZGIgfSBmcm9tICdAL2xpYi9maXJlYmFzZSc7XG5pbXBvcnQgeyBnZXRVc2VyQVBJRW5kcG9pbnRzLCB0eXBlIEFQSUVuZHBvaW50IH0gZnJvbSAnQC9saWIvc2VydmljZXMvc2V0dGluZ3NTZXJ2aWNlJztcbmltcG9ydCB7IG9wZW5Sb3V0ZXJTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc2VydmljZXMvb3BlblJvdXRlclNlcnZpY2UnO1xuaW1wb3J0IHsgZGVlcFNlZWtTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc2VydmljZXMvZGVlcFNlZWtTZXJ2aWNlJztcbmltcG9ydCB7IG1vZGVsRmF2b3JpdGVzU2VydmljZSB9IGZyb20gJ0AvbGliL3NlcnZpY2VzL21vZGVsRmF2b3JpdGVzU2VydmljZSc7XG5pbXBvcnQgeyBBSU1vZGVsLCBNb2RlbENhdGVnb3J5LCBNb2RlbFNvcnRCeSwgTW9kZWxGaWx0ZXJzIH0gZnJvbSAnQC9saWIvdHlwZXMvY2hhdCc7XG5pbXBvcnQgeyB1c2VBZHZhbmNlZFNlYXJjaCB9IGZyb20gJ0AvaG9va3MvdXNlQWR2YW5jZWRTZWFyY2gnO1xuaW1wb3J0IHsgYWR2YW5jZWRGaWx0ZXJzU2VydmljZSwgU21hcnRDYXRlZ29yeSB9IGZyb20gJ0AvbGliL3NlcnZpY2VzL2FkdmFuY2VkRmlsdGVyc1NlcnZpY2UnO1xuaW1wb3J0IEFkdmFuY2VkU2VhcmNoSW5wdXQsIHsgSGlnaGxpZ2h0ZWRUZXh0IH0gZnJvbSAnQC9jb21wb25lbnRzL0FkdmFuY2VkU2VhcmNoSW5wdXQnO1xuaW1wb3J0IEV4cGVuc2l2ZU1vZGVsQ29uZmlybWF0aW9uTW9kYWwgZnJvbSAnLi9FeHBlbnNpdmVNb2RlbENvbmZpcm1hdGlvbk1vZGFsJztcblxuaW50ZXJmYWNlIE1vZGVsU2VsZWN0aW9uTW9kYWxQcm9wcyB7XG4gIGlzT3BlbjogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgY3VycmVudE1vZGVsOiBzdHJpbmc7XG4gIG9uTW9kZWxTZWxlY3Q6IChtb2RlbElkOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbi8vIENvbnN0YW50ZXMgcGFyYSBjYWNoZVxuY29uc3QgQ0FDSEVfRFVSQVRJT04gPSA1ICogNjAgKiAxMDAwOyAvLyA1IG1pbnV0b3NcbmNvbnN0IEVORFBPSU5UU19DQUNIRV9EVVJBVElPTiA9IDEwICogNjAgKiAxMDAwOyAvLyAxMCBtaW51dG9zXG5cbmNvbnN0IE1vZGVsU2VsZWN0aW9uTW9kYWwgPSAoeyBpc09wZW4sIG9uQ2xvc2UsIGN1cnJlbnRNb2RlbCwgb25Nb2RlbFNlbGVjdCB9OiBNb2RlbFNlbGVjdGlvbk1vZGFsUHJvcHMpID0+IHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IFtlbmRwb2ludHMsIHNldEVuZHBvaW50c10gPSB1c2VTdGF0ZTxBUElFbmRwb2ludFtdPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZEVuZHBvaW50LCBzZXRTZWxlY3RlZEVuZHBvaW50XSA9IHVzZVN0YXRlPEFQSUVuZHBvaW50IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFttb2RlbHMsIHNldE1vZGVsc10gPSB1c2VTdGF0ZTxBSU1vZGVsW10+KFtdKTtcbiAgY29uc3QgW2Zhdm9yaXRlTW9kZWxJZHMsIHNldEZhdm9yaXRlTW9kZWxJZHNdID0gdXNlU3RhdGU8U2V0PHN0cmluZz4+KG5ldyBTZXQoKSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2Rpc3BsYXllZE1vZGVsc0NvdW50LCBzZXREaXNwbGF5ZWRNb2RlbHNDb3VudF0gPSB1c2VTdGF0ZSg0KTtcbiAgY29uc3QgTU9ERUxTX1BFUl9QQUdFID0gNDtcbiAgY29uc3QgW2N1c3RvbU1vZGVsSWQsIHNldEN1c3RvbU1vZGVsSWRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbc2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWwsIHNldFNob3dFeHBlbnNpdmVNb2RlbE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3BlbmRpbmdFeHBlbnNpdmVNb2RlbCwgc2V0UGVuZGluZ0V4cGVuc2l2ZU1vZGVsXSA9IHVzZVN0YXRlPEFJTW9kZWwgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzbWFydENhdGVnb3JpZXMsIHNldFNtYXJ0Q2F0ZWdvcmllc10gPSB1c2VTdGF0ZTxTbWFydENhdGVnb3J5W10+KFtdKTtcbiAgY29uc3QgW2Zhdm9yaXRlVG9nZ2xpbmcsIHNldEZhdm9yaXRlVG9nZ2xpbmddID0gdXNlU3RhdGU8U2V0PHN0cmluZz4+KG5ldyBTZXQoKSk7XG4gIGNvbnN0IFtsYXN0TG9hZGVkRW5kcG9pbnQsIHNldExhc3RMb2FkZWRFbmRwb2ludF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW21vZGVsc0NhY2hlLCBzZXRNb2RlbHNDYWNoZV0gPSB1c2VTdGF0ZTxNYXA8c3RyaW5nLCB7IG1vZGVsczogQUlNb2RlbFtdLCB0aW1lc3RhbXA6IG51bWJlciB9Pj4obmV3IE1hcCgpKTtcbiAgY29uc3QgW2VuZHBvaW50c0NhY2hlLCBzZXRFbmRwb2ludHNDYWNoZV0gPSB1c2VTdGF0ZTx7IGVuZHBvaW50czogQVBJRW5kcG9pbnRbXSwgdGltZXN0YW1wOiBudW1iZXIgfSB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IFtmaWx0ZXJzLCBzZXRGaWx0ZXJzXSA9IHVzZVN0YXRlPE1vZGVsRmlsdGVycz4oe1xuICAgIGNhdGVnb3J5OiAncGFpZCcsXG4gICAgc29ydEJ5OiAnbmV3ZXN0JyxcbiAgICBzZWFyY2hUZXJtOiAnJ1xuICB9KTtcblxuICAvLyBIb29rIGRlIGJ1c2NhIGF2YW7Dp2FkYVxuICBjb25zdCB7XG4gICAgc2VhcmNoVGVybSxcbiAgICBzZXRTZWFyY2hUZXJtLFxuICAgIHNlYXJjaFJlc3VsdHMsXG4gICAgc3VnZ2VzdGlvbnMsXG4gICAgaXNTZWFyY2hpbmcsXG4gICAgaGFzU2VhcmNoZWQsXG4gICAgY2xlYXJTZWFyY2hcbiAgfSA9IHVzZUFkdmFuY2VkU2VhcmNoKG1vZGVscywge1xuICAgIGRlYm91bmNlTXM6IDMwMCxcbiAgICBlbmFibGVTdWdnZXN0aW9uczogZmFsc2UsXG4gICAgY2FjaGVSZXN1bHRzOiB0cnVlLFxuICAgIGZ1enp5VGhyZXNob2xkOiAwLjYsXG4gICAgbWF4UmVzdWx0czogNTAsXG4gICAgYm9vc3RGYXZvcml0ZXM6IHRydWVcbiAgfSk7XG5cbiAgLy8gTG9hZCB1c2VyIGVuZHBvaW50cyBhcGVuYXMgc2UgbmVjZXNzw6FyaW9cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlciAmJiBpc09wZW4pIHtcbiAgICAgIC8vIFZlcmlmaWNhciBzZSB0ZW1vcyBjYWNoZSB2w6FsaWRvXG4gICAgICBpZiAoZW5kcG9pbnRzQ2FjaGUgJiYgRGF0ZS5ub3coKSAtIGVuZHBvaW50c0NhY2hlLnRpbWVzdGFtcCA8IEVORFBPSU5UU19DQUNIRV9EVVJBVElPTikge1xuICAgICAgICBzZXRFbmRwb2ludHMoZW5kcG9pbnRzQ2FjaGUuZW5kcG9pbnRzKTtcblxuICAgICAgICAvLyBTZWxlY2lvbmFyIGVuZHBvaW50IHNlIGFpbmRhIG7Do28gdGl2ZXIgdW0gc2VsZWNpb25hZG9cbiAgICAgICAgaWYgKCFzZWxlY3RlZEVuZHBvaW50ICYmIGVuZHBvaW50c0NhY2hlLmVuZHBvaW50cy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgY29uc3Qgb3BlblJvdXRlckVuZHBvaW50ID0gZW5kcG9pbnRzQ2FjaGUuZW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAubmFtZSA9PT0gJ09wZW5Sb3V0ZXInKTtcbiAgICAgICAgICBjb25zdCBkZWVwU2Vla0VuZHBvaW50ID0gZW5kcG9pbnRzQ2FjaGUuZW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAubmFtZSA9PT0gJ0RlZXBTZWVrJyk7XG5cbiAgICAgICAgICBpZiAob3BlblJvdXRlckVuZHBvaW50KSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KG9wZW5Sb3V0ZXJFbmRwb2ludCk7XG4gICAgICAgICAgfSBlbHNlIGlmIChkZWVwU2Vla0VuZHBvaW50KSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KGRlZXBTZWVrRW5kcG9pbnQpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KGVuZHBvaW50c0NhY2hlLmVuZHBvaW50c1swXSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBsb2FkRW5kcG9pbnRzKCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbdXNlciwgaXNPcGVuXSk7XG5cbiAgLy8gTG9hZCBtb2RlbHMgd2hlbiBlbmRwb2ludCBjaGFuZ2VzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHNlbGVjdGVkRW5kcG9pbnQpIHtcbiAgICAgIGNvbnN0IGNhY2hlS2V5ID0gYCR7c2VsZWN0ZWRFbmRwb2ludC5pZH1fJHtzZWxlY3RlZEVuZHBvaW50Lm5hbWV9YDtcbiAgICAgIGNvbnN0IGNhY2hlZERhdGEgPSBtb2RlbHNDYWNoZS5nZXQoY2FjaGVLZXkpO1xuXG4gICAgICAvLyBWZXJpZmljYXIgc2UgdGVtb3MgY2FjaGUgdsOhbGlkbyBwYXJhIGVzdGUgZW5kcG9pbnRcbiAgICAgIGlmIChjYWNoZWREYXRhICYmIERhdGUubm93KCkgLSBjYWNoZWREYXRhLnRpbWVzdGFtcCA8IENBQ0hFX0RVUkFUSU9OKSB7XG4gICAgICAgIHNldE1vZGVscyhjYWNoZWREYXRhLm1vZGVscyk7XG4gICAgICAgIHNldExhc3RMb2FkZWRFbmRwb2ludChzZWxlY3RlZEVuZHBvaW50LmlkKTtcblxuICAgICAgICAvLyBFeHRyYWlyIGZhdm9yaXRvcyBkbyBjYWNoZVxuICAgICAgICBjb25zdCBjYWNoZWRGYXZvcml0ZXMgPSBuZXcgU2V0KFxuICAgICAgICAgIGNhY2hlZERhdGEubW9kZWxzLmZpbHRlcihtID0+IG0uaXNGYXZvcml0ZSkubWFwKG0gPT4gbS5pZClcbiAgICAgICAgKTtcbiAgICAgICAgc2V0RmF2b3JpdGVNb2RlbElkcyhjYWNoZWRGYXZvcml0ZXMpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gU8OzIGNhcnJlZ2FyIHNlIG11ZG91IGRlIGVuZHBvaW50IG91IG7Do28gaMOhIGNhY2hlIHbDoWxpZG9cbiAgICAgICAgaWYgKGxhc3RMb2FkZWRFbmRwb2ludCAhPT0gc2VsZWN0ZWRFbmRwb2ludC5pZCB8fCAhY2FjaGVkRGF0YSkge1xuICAgICAgICAgIGlmIChzZWxlY3RlZEVuZHBvaW50Lm5hbWUgPT09ICdPcGVuUm91dGVyJykge1xuICAgICAgICAgICAgbG9hZE9wZW5Sb3V0ZXJNb2RlbHMoKTtcbiAgICAgICAgICB9IGVsc2UgaWYgKHNlbGVjdGVkRW5kcG9pbnQubmFtZSA9PT0gJ0RlZXBTZWVrJykge1xuICAgICAgICAgICAgbG9hZERlZXBTZWVrTW9kZWxzKCk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9LCBbc2VsZWN0ZWRFbmRwb2ludCwgbGFzdExvYWRlZEVuZHBvaW50LCBtb2RlbHNDYWNoZV0pO1xuXG4gIC8vIExvYWQgc21hcnQgY2F0ZWdvcmllc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHNldFNtYXJ0Q2F0ZWdvcmllcyhhZHZhbmNlZEZpbHRlcnNTZXJ2aWNlLmdldFNtYXJ0Q2F0ZWdvcmllcygpKTtcbiAgfSwgW10pO1xuXG4gIC8vIEZ1bsOnw6NvIHV0aWxpdMOhcmlhIHBhcmEgYnVzY2FyIHVzZXJuYW1lIGNvcnJldG9cbiAgY29uc3QgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlID0gYXN5bmMgKCk6IFByb21pc2U8c3RyaW5nPiA9PiB7XG4gICAgaWYgKCF1c2VyPy5lbWFpbCkgcmV0dXJuICd1bmtub3duJztcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGNvbGxlY3Rpb24sIHF1ZXJ5LCB3aGVyZSwgZ2V0RG9jcyB9ID0gYXdhaXQgaW1wb3J0KCdmaXJlYmFzZS9maXJlc3RvcmUnKTtcbiAgICAgIGNvbnN0IHVzdWFyaW9zUmVmID0gY29sbGVjdGlvbihkYiwgJ3VzdWFyaW9zJyk7XG4gICAgICBjb25zdCBxID0gcXVlcnkodXN1YXJpb3NSZWYsIHdoZXJlKCdlbWFpbCcsICc9PScsIHVzZXIuZW1haWwpKTtcbiAgICAgIGNvbnN0IHF1ZXJ5U25hcHNob3QgPSBhd2FpdCBnZXREb2NzKHEpO1xuXG4gICAgICBpZiAoIXF1ZXJ5U25hcHNob3QuZW1wdHkpIHtcbiAgICAgICAgY29uc3QgdXNlckRvYyA9IHF1ZXJ5U25hcHNob3QuZG9jc1swXTtcbiAgICAgICAgcmV0dXJuIHVzZXJEb2MuZGF0YSgpLnVzZXJuYW1lIHx8IHVzZXJEb2MuaWQ7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiAndW5rbm93bic7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gYnVzY2FyIHVzZXJuYW1lOicsIGVycm9yKTtcbiAgICAgIHJldHVybiAndW5rbm93bic7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGxvYWRFbmRwb2ludHMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICBjb25zb2xlLmxvZygnTm8gdXNlciBmb3VuZCcpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdXNlcm5hbWUgPSBhd2FpdCBnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUoKTtcbiAgICAgIGNvbnN0IHVzZXJFbmRwb2ludHMgPSBhd2FpdCBnZXRVc2VyQVBJRW5kcG9pbnRzKHVzZXJuYW1lKTtcblxuICAgICAgLy8gU2FsdmFyIG5vIGNhY2hlXG4gICAgICBzZXRFbmRwb2ludHNDYWNoZSh7XG4gICAgICAgIGVuZHBvaW50czogdXNlckVuZHBvaW50cyxcbiAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgICB9KTtcblxuICAgICAgc2V0RW5kcG9pbnRzKHVzZXJFbmRwb2ludHMpO1xuXG4gICAgICAvLyBTZWxlY3QgZmlyc3QgYXZhaWxhYmxlIGVuZHBvaW50IGJ5IGRlZmF1bHQgKE9wZW5Sb3V0ZXIgb3IgRGVlcFNlZWspXG4gICAgICBjb25zdCBvcGVuUm91dGVyRW5kcG9pbnQgPSB1c2VyRW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAubmFtZSA9PT0gJ09wZW5Sb3V0ZXInKTtcbiAgICAgIGNvbnN0IGRlZXBTZWVrRW5kcG9pbnQgPSB1c2VyRW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAubmFtZSA9PT0gJ0RlZXBTZWVrJyk7XG5cbiAgICAgIGlmIChvcGVuUm91dGVyRW5kcG9pbnQpIHtcbiAgICAgICAgc2V0U2VsZWN0ZWRFbmRwb2ludChvcGVuUm91dGVyRW5kcG9pbnQpO1xuICAgICAgfSBlbHNlIGlmIChkZWVwU2Vla0VuZHBvaW50KSB7XG4gICAgICAgIHNldFNlbGVjdGVkRW5kcG9pbnQoZGVlcFNlZWtFbmRwb2ludCk7XG4gICAgICB9IGVsc2UgaWYgKHVzZXJFbmRwb2ludHMubGVuZ3RoID4gMCkge1xuICAgICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KHVzZXJFbmRwb2ludHNbMF0pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGVuZHBvaW50czonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignRXJybyBhbyBjYXJyZWdhciBlbmRwb2ludHM6ICcgKyAoZXJyb3IgYXMgRXJyb3IpLm1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgbG9hZE9wZW5Sb3V0ZXJNb2RlbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEVuZHBvaW50IHx8ICF1c2VyKSByZXR1cm47XG5cbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIExvYWQgbW9kZWxzIGZyb20gT3BlblJvdXRlclxuICAgICAgY29uc3Qgb3BlblJvdXRlck1vZGVscyA9IGF3YWl0IG9wZW5Sb3V0ZXJTZXJ2aWNlLmZldGNoTW9kZWxzKCk7XG5cbiAgICAgIC8vIExvYWQgZmF2b3JpdGUgbW9kZWwgSURzXG4gICAgICBjb25zdCB1c2VybmFtZSA9IGF3YWl0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSgpO1xuICAgICAgY29uc3QgZmF2b3JpdGVJZHMgPSBhd2FpdCBtb2RlbEZhdm9yaXRlc1NlcnZpY2UuZ2V0RmF2b3JpdGVNb2RlbElkcyh1c2VybmFtZSwgc2VsZWN0ZWRFbmRwb2ludC5pZCk7XG5cbiAgICAgIC8vIE1hcmsgZmF2b3JpdGUgbW9kZWxzXG4gICAgICBjb25zdCBtb2RlbHNXaXRoRmF2b3JpdGVzID0gb3BlblJvdXRlck1vZGVscy5tYXAobW9kZWwgPT4gKHtcbiAgICAgICAgLi4ubW9kZWwsXG4gICAgICAgIGlzRmF2b3JpdGU6IGZhdm9yaXRlSWRzLmhhcyhtb2RlbC5pZClcbiAgICAgIH0pKTtcblxuICAgICAgLy8gU2FsdmFyIG5vIGNhY2hlXG4gICAgICBjb25zdCBjYWNoZUtleSA9IGAke3NlbGVjdGVkRW5kcG9pbnQuaWR9XyR7c2VsZWN0ZWRFbmRwb2ludC5uYW1lfWA7XG4gICAgICBzZXRNb2RlbHNDYWNoZShwcmV2ID0+IG5ldyBNYXAocHJldikuc2V0KGNhY2hlS2V5LCB7XG4gICAgICAgIG1vZGVsczogbW9kZWxzV2l0aEZhdm9yaXRlcyxcbiAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpXG4gICAgICB9KSk7XG5cbiAgICAgIHNldE1vZGVscyhtb2RlbHNXaXRoRmF2b3JpdGVzKTtcbiAgICAgIHNldEZhdm9yaXRlTW9kZWxJZHMoZmF2b3JpdGVJZHMpO1xuICAgICAgc2V0TGFzdExvYWRlZEVuZHBvaW50KHNlbGVjdGVkRW5kcG9pbnQuaWQpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIG1vZGVsczonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignRXJybyBhbyBjYXJyZWdhciBtb2RlbG9zJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2FkRGVlcFNlZWtNb2RlbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEVuZHBvaW50IHx8ICF1c2VyKSByZXR1cm47XG5cbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIExvYWQgbW9kZWxzIGZyb20gRGVlcFNlZWtcbiAgICAgIGNvbnN0IGRlZXBTZWVrTW9kZWxzID0gYXdhaXQgZGVlcFNlZWtTZXJ2aWNlLmZldGNoTW9kZWxzKCk7XG5cbiAgICAgIC8vIFNhbHZhciBubyBjYWNoZVxuICAgICAgY29uc3QgY2FjaGVLZXkgPSBgJHtzZWxlY3RlZEVuZHBvaW50LmlkfV8ke3NlbGVjdGVkRW5kcG9pbnQubmFtZX1gO1xuICAgICAgc2V0TW9kZWxzQ2FjaGUocHJldiA9PiBuZXcgTWFwKHByZXYpLnNldChjYWNoZUtleSwge1xuICAgICAgICBtb2RlbHM6IGRlZXBTZWVrTW9kZWxzLFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KClcbiAgICAgIH0pKTtcblxuICAgICAgc2V0TW9kZWxzKGRlZXBTZWVrTW9kZWxzKTtcbiAgICAgIHNldExhc3RMb2FkZWRFbmRwb2ludChzZWxlY3RlZEVuZHBvaW50LmlkKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBEZWVwU2VlayBtb2RlbHM6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0Vycm8gYW8gY2FycmVnYXIgbW9kZWxvcyBEZWVwU2VlaycpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZmlsdGVyZWRNb2RlbHMgPSBnZXRGaWx0ZXJlZE1vZGVscygpO1xuXG4gIGlmICghaXNPcGVuKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCBiZy1ibGFjay81MCBiYWNrZHJvcC1ibHVyLXNtIHotNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC00XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS05NTAvOTUgdmlhLWJsdWUtOTAwLzk1IHRvLWJsdWUtOTUwLzk1IGJhY2tkcm9wLWJsdXIteGwgcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci1ibHVlLTYwMC8zMCBzaGFkb3ctMnhsIHctZnVsbCBtYXgtdy03eGwgbWF4LWgtWzkwdmhdIG92ZXJmbG93LWhpZGRlbiByZWxhdGl2ZVwiPlxuICAgICAgICB7LyogRWZlaXRvIGRlIGJyaWxobyBzdXRpbCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS01MDAvNSB2aWEtdHJhbnNwYXJlbnQgdG8tY3lhbi01MDAvNSBwb2ludGVyLWV2ZW50cy1ub25lIHJvdW5kZWQtMnhsXCI+PC9kaXY+XG5cbiAgICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgYm9yZGVyLWIgYm9yZGVyLWJsdWUtNzAwLzMwIHJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtNTAwIHRvLWN5YW4tNjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk05Ljc1IDE3TDkgMjBsLTEgMWg4bC0xLTEtLjc1LTNNMyAxM2gxOE01IDE3aDE0YTIgMiAwIDAwMi0yVjVhMiAyIDAgMDAtMi0ySDVhMiAyIDAgMDAtMiAydjEwYTIgMiAwIDAwMiAyelwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtYmx1ZS0xMDBcIj5TZWxlY2lvbmFyIE1vZGVsbzwvaDI+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIHsvKiBFbmRwb2ludCBTZWxlY3Rpb24gKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTIwMFwiPkVuZHBvaW50OjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkRW5kcG9pbnQ/LmlkIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVuZHBvaW50ID0gZW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAuaWQgPT09IGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRFbmRwb2ludChlbmRwb2ludCB8fCBudWxsKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTkwMC80MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItYmx1ZS02MDAvMzAgcm91bmRlZC14bCBweC0zIHB5LTIgdGV4dC1ibHVlLTEwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwLzUwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlNlbGVjaW9uZSB1bSBlbmRwb2ludDwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAge2VuZHBvaW50cy5tYXAoZW5kcG9pbnQgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17ZW5kcG9pbnQuaWR9IHZhbHVlPXtlbmRwb2ludC5pZH0gY2xhc3NOYW1lPVwiYmctYmx1ZS05MDAgdGV4dC1ibHVlLTEwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtlbmRwb2ludC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVJlZnJlc2hNb2RlbHN9XG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZyB8fCAhc2VsZWN0ZWRFbmRwb2ludH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtODAwLzQwIHRleHQtYmx1ZS0zMDAgaG92ZXI6dGV4dC1ibHVlLTIwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTA1IGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkXCJcbiAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQXR1YWxpemFyIG1vZGVsb3NcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPXtgdy00IGgtNCAke2xvYWRpbmcgPyAnYW5pbWF0ZS1zcGluJyA6ICcnfWB9IGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNNCA0djVoLjU4Mm0xNS4zNTYgMkE4LjAwMSA4LjAwMSAwIDAwNC41ODIgOW0wIDBIOW0xMSAxMXYtNWgtLjU4MW0wIDBhOC4wMDMgOC4wMDMgMCAwMS0xNS4zNTctMm0xNS4zNTcgMkgxNVwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtvbkNsb3NlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMiByb3VuZGVkLXhsIGhvdmVyOmJnLWJsdWUtODAwLzQwIHRleHQtYmx1ZS0zMDAgaG92ZXI6dGV4dC1ibHVlLTIwMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0yMDAgaG92ZXI6c2NhbGUtMTA1XCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy01IGgtNVwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTYgMThMMTggNk02IDZsMTIgMTJcIiAvPlxuICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogTWFpbiBDb250ZW50IC0gTGF5b3V0IEhvcml6b250YWwgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLWZ1bGwgbWF4LWgtW2NhbGMoOTB2aC0xMjBweCldXCI+XG4gICAgICAgICAgey8qIFNpZGViYXIgLSBBYmFzIG5hIGxhdGVyYWwgZXNxdWVyZGEgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTY0IGJvcmRlci1yIGJvcmRlci1ibHVlLTcwMC8zMCBiZy1ibHVlLTkwMC8yMCBiYWNrZHJvcC1ibHVyLXNtIHJlbGF0aXZlIHotMTBcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00IHNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICB7LyogQ2F0ZWdvcnkgVGFicyAtIFZlcnRpY2FsICovfVxuICAgICAgICAgICAgICB7c2VsZWN0ZWRFbmRwb2ludD8ubmFtZSA9PT0gJ09wZW5Sb3V0ZXInICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ibHVlLTIwMCBtYi0zXCI+Q2F0ZWdvcmlhczwvaDM+XG4gICAgICAgICAgICAgICAgICB7KFsncGFpZCcsICdmcmVlJywgJ2Zhdm9yaXRlcyddIGFzIE1vZGVsQ2F0ZWdvcnlbXSkubWFwKGNhdGVnb3J5ID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGtleT17Y2F0ZWdvcnl9XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RmlsdGVycyhwcmV2ID0+ICh7IC4uLnByZXYsIGNhdGVnb3J5IH0pKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgdGV4dC1sZWZ0IHB5LTMgcHgtNCByb3VuZGVkLXhsIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyAke1xuICAgICAgICAgICAgICAgICAgICAgICAgZmlsdGVycy5jYXRlZ29yeSA9PT0gY2F0ZWdvcnlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWN5YW4tNjAwIHRleHQtd2hpdGUgc2hhZG93LWxnJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LWJsdWUtMzAwIGhvdmVyOnRleHQtYmx1ZS0yMDAgaG92ZXI6YmctYmx1ZS04MDAvMzAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgcm91bmRlZC1mdWxsIGJnLWN1cnJlbnRcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj57Y2F0ZWdvcnkgPT09ICdwYWlkJyA/ICdQYWdvcycgOiBjYXRlZ29yeSA9PT0gJ2ZyZWUnID8gJ0dyw6F0aXMnIDogJ0Zhdm9yaXRvcyd9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTWFpbiBDb250ZW50IEFyZWEgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbFwiPlxuICAgICAgICAgICAgey8qIFBsYWNlaG9sZGVyIHBhcmEgY29udGXDumRvIHByaW5jaXBhbCAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNlwiPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwXCI+Q29udGXDumRvIHByaW5jaXBhbCBhcXVpLi4uPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBFeHBlbnNpdmUgTW9kZWwgQ29uZmlybWF0aW9uIE1vZGFsICovfVxuICAgICAgPEV4cGVuc2l2ZU1vZGVsQ29uZmlybWF0aW9uTW9kYWxcbiAgICAgICAgaXNPcGVuPXtzaG93RXhwZW5zaXZlTW9kZWxNb2RhbH1cbiAgICAgICAgbW9kZWw9e3BlbmRpbmdFeHBlbnNpdmVNb2RlbH1cbiAgICAgICAgb25Db25maXJtPXsoKSA9PiB7fX1cbiAgICAgICAgb25DYW5jZWw9eygpID0+IHt9fVxuICAgICAgLz5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IE1vZGVsU2VsZWN0aW9uTW9kYWw7XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VBdXRoIiwiZGIiLCJnZXRVc2VyQVBJRW5kcG9pbnRzIiwib3BlblJvdXRlclNlcnZpY2UiLCJkZWVwU2Vla1NlcnZpY2UiLCJtb2RlbEZhdm9yaXRlc1NlcnZpY2UiLCJ1c2VBZHZhbmNlZFNlYXJjaCIsImFkdmFuY2VkRmlsdGVyc1NlcnZpY2UiLCJFeHBlbnNpdmVNb2RlbENvbmZpcm1hdGlvbk1vZGFsIiwiQ0FDSEVfRFVSQVRJT04iLCJFTkRQT0lOVFNfQ0FDSEVfRFVSQVRJT04iLCJNb2RlbFNlbGVjdGlvbk1vZGFsIiwiaXNPcGVuIiwib25DbG9zZSIsImN1cnJlbnRNb2RlbCIsIm9uTW9kZWxTZWxlY3QiLCJ1c2VyIiwiZW5kcG9pbnRzIiwic2V0RW5kcG9pbnRzIiwic2VsZWN0ZWRFbmRwb2ludCIsInNldFNlbGVjdGVkRW5kcG9pbnQiLCJtb2RlbHMiLCJzZXRNb2RlbHMiLCJmYXZvcml0ZU1vZGVsSWRzIiwic2V0RmF2b3JpdGVNb2RlbElkcyIsIlNldCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiZXJyb3IiLCJzZXRFcnJvciIsImRpc3BsYXllZE1vZGVsc0NvdW50Iiwic2V0RGlzcGxheWVkTW9kZWxzQ291bnQiLCJNT0RFTFNfUEVSX1BBR0UiLCJjdXN0b21Nb2RlbElkIiwic2V0Q3VzdG9tTW9kZWxJZCIsInNob3dFeHBlbnNpdmVNb2RlbE1vZGFsIiwic2V0U2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWwiLCJwZW5kaW5nRXhwZW5zaXZlTW9kZWwiLCJzZXRQZW5kaW5nRXhwZW5zaXZlTW9kZWwiLCJzZWxlY3RlZENhdGVnb3J5Iiwic2V0U2VsZWN0ZWRDYXRlZ29yeSIsInNtYXJ0Q2F0ZWdvcmllcyIsInNldFNtYXJ0Q2F0ZWdvcmllcyIsImZhdm9yaXRlVG9nZ2xpbmciLCJzZXRGYXZvcml0ZVRvZ2dsaW5nIiwibGFzdExvYWRlZEVuZHBvaW50Iiwic2V0TGFzdExvYWRlZEVuZHBvaW50IiwibW9kZWxzQ2FjaGUiLCJzZXRNb2RlbHNDYWNoZSIsIk1hcCIsImVuZHBvaW50c0NhY2hlIiwic2V0RW5kcG9pbnRzQ2FjaGUiLCJmaWx0ZXJzIiwic2V0RmlsdGVycyIsImNhdGVnb3J5Iiwic29ydEJ5Iiwic2VhcmNoVGVybSIsInNldFNlYXJjaFRlcm0iLCJzZWFyY2hSZXN1bHRzIiwic3VnZ2VzdGlvbnMiLCJpc1NlYXJjaGluZyIsImhhc1NlYXJjaGVkIiwiY2xlYXJTZWFyY2giLCJkZWJvdW5jZU1zIiwiZW5hYmxlU3VnZ2VzdGlvbnMiLCJjYWNoZVJlc3VsdHMiLCJmdXp6eVRocmVzaG9sZCIsIm1heFJlc3VsdHMiLCJib29zdEZhdm9yaXRlcyIsIkRhdGUiLCJub3ciLCJ0aW1lc3RhbXAiLCJsZW5ndGgiLCJvcGVuUm91dGVyRW5kcG9pbnQiLCJmaW5kIiwiZXAiLCJuYW1lIiwiZGVlcFNlZWtFbmRwb2ludCIsImxvYWRFbmRwb2ludHMiLCJjYWNoZUtleSIsImlkIiwiY2FjaGVkRGF0YSIsImdldCIsImNhY2hlZEZhdm9yaXRlcyIsImZpbHRlciIsIm0iLCJpc0Zhdm9yaXRlIiwibWFwIiwibG9hZE9wZW5Sb3V0ZXJNb2RlbHMiLCJsb2FkRGVlcFNlZWtNb2RlbHMiLCJnZXRTbWFydENhdGVnb3JpZXMiLCJnZXRVc2VybmFtZUZyb21GaXJlc3RvcmUiLCJlbWFpbCIsImNvbGxlY3Rpb24iLCJxdWVyeSIsIndoZXJlIiwiZ2V0RG9jcyIsInVzdWFyaW9zUmVmIiwicSIsInF1ZXJ5U25hcHNob3QiLCJlbXB0eSIsInVzZXJEb2MiLCJkb2NzIiwiZGF0YSIsInVzZXJuYW1lIiwiY29uc29sZSIsImxvZyIsInVzZXJFbmRwb2ludHMiLCJtZXNzYWdlIiwib3BlblJvdXRlck1vZGVscyIsImZldGNoTW9kZWxzIiwiZmF2b3JpdGVJZHMiLCJnZXRGYXZvcml0ZU1vZGVsSWRzIiwibW9kZWxzV2l0aEZhdm9yaXRlcyIsIm1vZGVsIiwiaGFzIiwicHJldiIsInNldCIsImRlZXBTZWVrTW9kZWxzIiwiZmlsdGVyZWRNb2RlbHMiLCJnZXRGaWx0ZXJlZE1vZGVscyIsImRpdiIsImNsYXNzTmFtZSIsInN2ZyIsImZpbGwiLCJzdHJva2UiLCJ2aWV3Qm94IiwicGF0aCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsInN0cm9rZVdpZHRoIiwiZCIsImgyIiwibGFiZWwiLCJzZWxlY3QiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsImVuZHBvaW50IiwidGFyZ2V0Iiwib3B0aW9uIiwiYnV0dG9uIiwib25DbGljayIsImhhbmRsZVJlZnJlc2hNb2RlbHMiLCJkaXNhYmxlZCIsInRpdGxlIiwiaDMiLCJzcGFuIiwicCIsIm9uQ29uZmlybSIsIm9uQ2FuY2VsIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/advancedFiltersService.ts":
/*!****************************************************!*\
  !*** ./src/lib/services/advancedFiltersService.ts ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   advancedFiltersService: function() { return /* binding */ advancedFiltersService; }\n/* harmony export */ });\nclass AdvancedFiltersService {\n    getSmartCategories() {\n        return this.smartCategories;\n    }\n    getModelsByCategory(models, categoryId) {\n        const category = this.smartCategories.find((cat)=>cat.id === categoryId);\n        if (!category) {\n            return models;\n        }\n        return models.filter(category.filter);\n    }\n    getCategoryStats(models) {\n        const totalModels = models.length;\n        return this.smartCategories.map((category)=>{\n            const matchingModels = models.filter(category.filter);\n            return {\n                category,\n                count: matchingModels.length,\n                percentage: totalModels > 0 ? matchingModels.length / totalModels * 100 : 0\n            };\n        });\n    }\n    // Filtros avançados por preço\n    filterByPriceRange(models, minPrice, maxPrice) {\n        return models.filter((model)=>{\n            const promptPrice = parseFloat(model.pricing.prompt);\n            const completionPrice = parseFloat(model.pricing.completion);\n            const totalPrice = promptPrice + completionPrice;\n            return totalPrice >= minPrice && totalPrice <= maxPrice;\n        });\n    }\n    // Filtros por contexto\n    filterByContextRange(models, minContext, maxContext) {\n        return models.filter((model)=>{\n            return model.context_length >= minContext && model.context_length <= maxContext;\n        });\n    }\n    // Filtros por modalidades\n    filterByInputModalities(models, modalities) {\n        return models.filter((model)=>{\n            var _model_architecture;\n            if (!((_model_architecture = model.architecture) === null || _model_architecture === void 0 ? void 0 : _model_architecture.input_modalities)) return false;\n            return modalities.every((modality)=>model.architecture.input_modalities.includes(modality));\n        });\n    }\n    // Busca por tags/palavras-chave\n    filterByKeywords(models, keywords) {\n        return models.filter((model)=>{\n            const searchText = \"\".concat(model.name, \" \").concat(model.description || \"\", \" \").concat(model.id).toLowerCase();\n            return keywords.some((keyword)=>searchText.includes(keyword.toLowerCase()));\n        });\n    }\n    // Filtros combinados\n    applyAdvancedFilters(models, filters) {\n        let filteredModels = [\n            ...models\n        ];\n        // Filtrar por categorias\n        if (filters.categories && filters.categories.length > 0) {\n            filteredModels = filteredModels.filter((model)=>{\n                return filters.categories.some((categoryId)=>{\n                    const category = this.smartCategories.find((cat)=>cat.id === categoryId);\n                    return category ? category.filter(model) : false;\n                });\n            });\n        }\n        // Filtrar por faixa de preço\n        if (filters.priceRange) {\n            filteredModels = this.filterByPriceRange(filteredModels, filters.priceRange.min, filters.priceRange.max);\n        }\n        // Filtrar por faixa de contexto\n        if (filters.contextRange) {\n            filteredModels = this.filterByContextRange(filteredModels, filters.contextRange.min, filters.contextRange.max);\n        }\n        // Filtrar por modalidades de entrada\n        if (filters.inputModalities && filters.inputModalities.length > 0) {\n            filteredModels = this.filterByInputModalities(filteredModels, filters.inputModalities);\n        }\n        // Filtrar por palavras-chave\n        if (filters.keywords && filters.keywords.length > 0) {\n            filteredModels = this.filterByKeywords(filteredModels, filters.keywords);\n        }\n        // Filtrar apenas favoritos\n        if (filters.onlyFavorites) {\n            filteredModels = filteredModels.filter((model)=>model.isFavorite);\n        }\n        return filteredModels;\n    }\n    // Sugestões de filtros baseadas nos modelos disponíveis\n    getSuggestedFilters(models) {\n        const categoryStats = this.getCategoryStats(models);\n        const popularCategories = categoryStats.filter((stat)=>stat.count > 0).sort((a, b)=>b.count - a.count).slice(0, 5);\n        // Faixas de preço sugeridas (valores por token, não por 1M)\n        const priceRanges = [\n            {\n                label: \"Gratuito\",\n                min: 0,\n                max: 0\n            },\n            {\n                label: \"Muito Barato (< $1/1M)\",\n                min: 0.000000001,\n                max: 0.000001\n            },\n            {\n                label: \"Barato ($1-10/1M)\",\n                min: 0.000001,\n                max: 0.00001\n            },\n            {\n                label: \"M\\xe9dio ($10-100/1M)\",\n                min: 0.00001,\n                max: 0.0001\n            },\n            {\n                label: \"Caro (> $100/1M)\",\n                min: 0.0001,\n                max: Infinity\n            }\n        ].map((range)=>({\n                ...range,\n                count: this.filterByPriceRange(models, range.min, range.max).length\n            }));\n        // Faixas de contexto sugeridas\n        const contextRanges = [\n            {\n                label: \"Pequeno (< 8K)\",\n                min: 0,\n                max: 8000\n            },\n            {\n                label: \"M\\xe9dio (8K - 32K)\",\n                min: 8000,\n                max: 32000\n            },\n            {\n                label: \"Grande (32K - 128K)\",\n                min: 32000,\n                max: 128000\n            },\n            {\n                label: \"Muito Grande (> 128K)\",\n                min: 128000,\n                max: Infinity\n            }\n        ].map((range)=>({\n                ...range,\n                count: this.filterByContextRange(models, range.min, range.max).length\n            }));\n        return {\n            popularCategories,\n            priceRanges,\n            contextRanges\n        };\n    }\n    constructor(){\n        this.smartCategories = [\n            {\n                id: \"vision\",\n                name: \"Vis\\xe3o\",\n                description: \"Modelos que processam imagens\",\n                icon: \"\\uD83D\\uDC41️\",\n                filter: (model)=>{\n                    var _model_architecture_input_modalities, _model_architecture, _model_description, _model_description1;\n                    return !!(((_model_architecture = model.architecture) === null || _model_architecture === void 0 ? void 0 : (_model_architecture_input_modalities = _model_architecture.input_modalities) === null || _model_architecture_input_modalities === void 0 ? void 0 : _model_architecture_input_modalities.includes(\"image\")) || model.name.toLowerCase().includes(\"vision\") || ((_model_description = model.description) === null || _model_description === void 0 ? void 0 : _model_description.toLowerCase().includes(\"vision\")) || ((_model_description1 = model.description) === null || _model_description1 === void 0 ? void 0 : _model_description1.toLowerCase().includes(\"image\")));\n                }\n            },\n            {\n                id: \"coding\",\n                name: \"C\\xf3digo\",\n                description: \"Modelos especializados em programa\\xe7\\xe3o\",\n                icon: \"\\uD83D\\uDCBB\",\n                filter: (model)=>{\n                    const keywords = [\n                        \"code\",\n                        \"coding\",\n                        \"programming\",\n                        \"developer\",\n                        \"coder\"\n                    ];\n                    return keywords.some((keyword)=>{\n                        var _model_description;\n                        return model.name.toLowerCase().includes(keyword) || !!((_model_description = model.description) === null || _model_description === void 0 ? void 0 : _model_description.toLowerCase().includes(keyword));\n                    });\n                }\n            },\n            {\n                id: \"reasoning\",\n                name: \"Racioc\\xednio\",\n                description: \"Modelos otimizados para racioc\\xednio l\\xf3gico\",\n                icon: \"\\uD83E\\uDDE0\",\n                filter: (model)=>{\n                    const keywords = [\n                        \"reasoning\",\n                        \"logic\",\n                        \"math\",\n                        \"analysis\",\n                        \"thinking\"\n                    ];\n                    return keywords.some((keyword)=>{\n                        var _model_description;\n                        return model.name.toLowerCase().includes(keyword) || !!((_model_description = model.description) === null || _model_description === void 0 ? void 0 : _model_description.toLowerCase().includes(keyword));\n                    });\n                }\n            },\n            {\n                id: \"creative\",\n                name: \"Criativo\",\n                description: \"Modelos para tarefas criativas\",\n                icon: \"\\uD83C\\uDFA8\",\n                filter: (model)=>{\n                    const keywords = [\n                        \"creative\",\n                        \"writing\",\n                        \"story\",\n                        \"art\",\n                        \"creative\"\n                    ];\n                    return keywords.some((keyword)=>{\n                        var _model_description;\n                        return model.name.toLowerCase().includes(keyword) || !!((_model_description = model.description) === null || _model_description === void 0 ? void 0 : _model_description.toLowerCase().includes(keyword));\n                    });\n                }\n            },\n            {\n                id: \"fast\",\n                name: \"R\\xe1pido\",\n                description: \"Modelos otimizados para velocidade\",\n                icon: \"⚡\",\n                filter: (model)=>{\n                    const keywords = [\n                        \"fast\",\n                        \"quick\",\n                        \"speed\",\n                        \"turbo\",\n                        \"instant\"\n                    ];\n                    return keywords.some((keyword)=>{\n                        var _model_description;\n                        return model.name.toLowerCase().includes(keyword) || !!((_model_description = model.description) === null || _model_description === void 0 ? void 0 : _model_description.toLowerCase().includes(keyword));\n                    });\n                }\n            },\n            {\n                id: \"large_context\",\n                name: \"Grande Contexto\",\n                description: \"Modelos com contexto extenso (>32K)\",\n                icon: \"\\uD83D\\uDCDA\",\n                filter: (model)=>{\n                    return model.context_length > 32000;\n                }\n            },\n            {\n                id: \"cheap\",\n                name: \"Econ\\xf4mico\",\n                description: \"Modelos com pre\\xe7os baixos\",\n                icon: \"\\uD83D\\uDCB0\",\n                filter: (model)=>{\n                    const promptPrice = parseFloat(model.pricing.prompt);\n                    const completionPrice = parseFloat(model.pricing.completion);\n                    const totalPrice = promptPrice + completionPrice;\n                    return totalPrice < 0.000001; // Menos de $1 por 1M tokens\n                }\n            },\n            {\n                id: \"premium\",\n                name: \"Premium\",\n                description: \"Modelos de alta qualidade\",\n                icon: \"⭐\",\n                filter: (model)=>{\n                    const keywords = [\n                        \"gpt-4\",\n                        \"claude-3\",\n                        \"premium\",\n                        \"pro\",\n                        \"advanced\"\n                    ];\n                    return keywords.some((keyword)=>model.name.toLowerCase().includes(keyword) || model.id.toLowerCase().includes(keyword));\n                }\n            }\n        ];\n    }\n}\nconst advancedFiltersService = new AdvancedFiltersService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/deepSeekService.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/deepSeekService.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepSeekService: function() { return /* binding */ deepSeekService; }\n/* harmony export */ });\nclass DeepSeekService {\n    async fetchModels() {\n        // Verificar cache\n        if (this.cache && Date.now() - this.cache.timestamp < this.CACHE_DURATION) {\n            return this.cache.models;\n        }\n        try {\n            // DeepSeek tem modelos fixos conhecidos\n            const models = [\n                {\n                    id: \"deepseek-chat\",\n                    name: \"DeepSeek Chat\",\n                    description: \"Modelo de chat geral da DeepSeek, otimizado para conversas e tarefas diversas.\",\n                    context_length: 32768,\n                    pricing: {\n                        prompt: \"0.00014\",\n                        completion: \"0.00028\"\n                    },\n                    architecture: {\n                        input_modalities: [\n                            \"text\"\n                        ],\n                        output_modalities: [\n                            \"text\"\n                        ],\n                        tokenizer: \"deepseek\"\n                    },\n                    created: Date.now(),\n                    isFavorite: false\n                },\n                {\n                    id: \"deepseek-coder\",\n                    name: \"DeepSeek Coder\",\n                    description: \"Modelo especializado em programa\\xe7\\xe3o e desenvolvimento de c\\xf3digo.\",\n                    context_length: 16384,\n                    pricing: {\n                        prompt: \"0.00014\",\n                        completion: \"0.00028\"\n                    },\n                    architecture: {\n                        input_modalities: [\n                            \"text\"\n                        ],\n                        output_modalities: [\n                            \"text\"\n                        ],\n                        tokenizer: \"deepseek\"\n                    },\n                    created: Date.now(),\n                    isFavorite: false\n                }\n            ];\n            // Atualizar cache\n            this.cache = {\n                models,\n                timestamp: Date.now()\n            };\n            return models;\n        } catch (error) {\n            console.error(\"Error fetching DeepSeek models:\", error);\n            // Retornar cache se disponível, mesmo que expirado\n            if (this.cache) {\n                return this.cache.models;\n            }\n            throw error;\n        }\n    }\n    filterByCategory(models, category) {\n        switch(category){\n            case \"free\":\n                return models.filter((model)=>this.isFreeModel(model));\n            case \"paid\":\n                return models.filter((model)=>!this.isFreeModel(model));\n            default:\n                return models;\n        }\n    }\n    sortModels(models, sortBy) {\n        const sortedModels = [\n            ...models\n        ];\n        switch(sortBy){\n            case \"newest\":\n                return sortedModels.sort((a, b)=>(b.created || 0) - (a.created || 0));\n            case \"price_low\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(a) - this.getTotalPrice(b));\n            case \"price_high\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(b) - this.getTotalPrice(a));\n            case \"context_high\":\n                return sortedModels.sort((a, b)=>b.context_length - a.context_length);\n            default:\n                return sortedModels;\n        }\n    }\n    isFreeModel(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice === 0 && completionPrice === 0;\n    }\n    getTotalPrice(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice + completionPrice;\n    }\n    formatPrice(price) {\n        const numPrice = parseFloat(price) * 1000000; // Multiplicar por 1M para mostrar preço por 1M tokens\n        if (numPrice === 0) return \"Gr\\xe1tis\";\n        if (numPrice < 0.001) return \"< $0.001\";\n        return \"$\".concat(numPrice.toFixed(3));\n    }\n    formatContextLength(length) {\n        return length.toLocaleString(); // Mostra o número completo com separadores de milhares\n    }\n    // Busca avançada com fuzzy matching\n    searchModels(models, searchTerm) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n        if (!searchTerm.trim()) {\n            return [];\n        }\n        const term = searchTerm.toLowerCase();\n        const results = [];\n        for (const model of models){\n            let score = 0;\n            const matchedFields = [];\n            let highlightedName = model.name;\n            let highlightedDescription = model.description || \"\";\n            // Busca no nome (peso maior)\n            if (model.name.toLowerCase().includes(term)) {\n                score += 10;\n                matchedFields.push(\"name\");\n                highlightedName = this.highlightText(model.name, term);\n            }\n            // Busca no ID (peso médio)\n            if (model.id.toLowerCase().includes(term)) {\n                score += 7;\n                matchedFields.push(\"id\");\n            }\n            // Busca na descrição (peso menor)\n            if (model.description && model.description.toLowerCase().includes(term)) {\n                score += 3;\n                matchedFields.push(\"description\");\n                highlightedDescription = this.highlightText(model.description, term);\n            }\n            // Boost para favoritos\n            if (boostFavorites && model.isFavorite) {\n                score *= 1.5;\n            }\n            // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n            if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && this.isFreeModel(model)) {\n                score += 5;\n            }\n            // Boost para modelos de código se buscar por \"code\" ou \"programming\"\n            if ((term.includes(\"code\") || term.includes(\"programming\") || term.includes(\"coder\")) && model.id.includes(\"coder\")) {\n                score += 5;\n            }\n            if (score > 0) {\n                results.push({\n                    model,\n                    score,\n                    matchedFields,\n                    highlightedName,\n                    highlightedDescription\n                });\n            }\n        }\n        // Ordenar por score e limitar resultados\n        return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n    }\n    highlightText(text, term) {\n        const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n        return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n    }\n    // Limpar cache\n    clearCache() {\n        this.cache = null;\n    }\n    constructor(){\n        this.cache = null;\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutos\n        ;\n    }\n}\nconst deepSeekService = new DeepSeekService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/deepSeekService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/modelFavoritesService.ts":
/*!***************************************************!*\
  !*** ./src/lib/services/modelFavoritesService.ts ***!
  \***************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   modelFavoritesService: function() { return /* binding */ modelFavoritesService; }\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\nclass ModelFavoritesService {\n    // Função para escapar IDs que contêm caracteres especiais\n    escapeModelId(modelId) {\n        return modelId.replace(/\\//g, \"_SLASH_\").replace(/\\./g, \"_DOT_\");\n    }\n    // Função para desescapar IDs\n    unescapeModelId(escapedId) {\n        return escapedId.replace(/_SLASH_/g, \"/\").replace(/_DOT_/g, \".\");\n    }\n    // Adicionar modelo aos favoritos\n    async addToFavorites(userId, endpointId, modelId, modelName) {\n        try {\n            const escapedModelId = this.escapeModelId(modelId);\n            const favoriteRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"usuarios\", userId, \"endpoints\", endpointId, \"modelos_favoritos\", escapedModelId);\n            const favoriteData = {\n                modelId,\n                modelName,\n                endpointId,\n                addedAt: Date.now()\n            };\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(favoriteRef, favoriteData);\n            console.log(\"Model added to favorites successfully\");\n        } catch (error) {\n            console.error(\"Error adding model to favorites:\", error);\n            throw error;\n        }\n    }\n    // Remover modelo dos favoritos\n    async removeFromFavorites(userId, endpointId, modelId) {\n        try {\n            const escapedModelId = this.escapeModelId(modelId);\n            const favoriteRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"usuarios\", userId, \"endpoints\", endpointId, \"modelos_favoritos\", escapedModelId);\n            await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)(favoriteRef);\n            console.log(\"Model removed from favorites successfully\");\n        } catch (error) {\n            console.error(\"Error removing model from favorites:\", error);\n            throw error;\n        }\n    }\n    // Verificar se um modelo é favorito\n    async isModelFavorited(userId, endpointId, modelId) {\n        try {\n            const escapedModelId = this.escapeModelId(modelId);\n            const favoriteRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"usuarios\", userId, \"endpoints\", endpointId, \"modelos_favoritos\", escapedModelId);\n            const favoriteDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(favoriteRef);\n            return favoriteDoc.exists();\n        } catch (error) {\n            console.error(\"Error checking if model is favorited:\", error);\n            return false;\n        }\n    }\n    // Alternar status de favorito (adicionar se não existe, remover se existe)\n    async toggleFavorite(userId, endpointId, modelId, modelName) {\n        try {\n            const isFavorited = await this.isModelFavorited(userId, endpointId, modelId);\n            if (isFavorited) {\n                await this.removeFromFavorites(userId, endpointId, modelId);\n                return false;\n            } else {\n                await this.addToFavorites(userId, endpointId, modelId, modelName);\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            throw error;\n        }\n    }\n    // Obter todos os modelos favoritos de um endpoint\n    async getFavoriteModels(userId, endpointId) {\n        try {\n            const favoritesRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_1__.db, \"usuarios\", userId, \"endpoints\", endpointId, \"modelos_favoritos\");\n            const favoritesSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(favoritesRef);\n            const favorites = [];\n            favoritesSnapshot.forEach((doc)=>{\n                const data = doc.data();\n                // Desescapar o modelId se necessário\n                data.modelId = this.unescapeModelId(data.modelId);\n                favorites.push(data);\n            });\n            // Ordenar por data de adição (mais recente primeiro)\n            favorites.sort((a, b)=>b.addedAt - a.addedAt);\n            return favorites;\n        } catch (error) {\n            console.error(\"Error getting favorite models:\", error);\n            throw error;\n        }\n    }\n    // Obter apenas os IDs dos modelos favoritos (para performance)\n    async getFavoriteModelIds(userId, endpointId) {\n        try {\n            const favorites = await this.getFavoriteModels(userId, endpointId);\n            // Os IDs já estão desescapados pela função getFavoriteModels\n            return new Set(favorites.map((fav)=>fav.modelId));\n        } catch (error) {\n            console.error(\"Error getting favorite model IDs:\", error);\n            return new Set();\n        }\n    }\n    // Obter estatísticas dos favoritos\n    async getFavoritesStats(userId, endpointId) {\n        try {\n            const favorites = await this.getFavoriteModels(userId, endpointId);\n            if (favorites.length === 0) {\n                return {\n                    totalFavorites: 0\n                };\n            }\n            const sortedByDate = [\n                ...favorites\n            ].sort((a, b)=>b.addedAt - a.addedAt);\n            return {\n                totalFavorites: favorites.length,\n                mostRecentlyAdded: sortedByDate[0],\n                oldestFavorite: sortedByDate[sortedByDate.length - 1]\n            };\n        } catch (error) {\n            console.error(\"Error getting favorites stats:\", error);\n            return {\n                totalFavorites: 0\n            };\n        }\n    }\n    // Limpar todos os favoritos de um endpoint\n    async clearAllFavorites(userId, endpointId) {\n        try {\n            const favorites = await this.getFavoriteModels(userId, endpointId);\n            // Os IDs já estão desescapados, então a função removeFromFavorites vai escapá-los novamente\n            const deletePromises = favorites.map((favorite)=>this.removeFromFavorites(userId, endpointId, favorite.modelId));\n            await Promise.all(deletePromises);\n            console.log(\"All favorites cleared successfully\");\n        } catch (error) {\n            console.error(\"Error clearing all favorites:\", error);\n            throw error;\n        }\n    }\n    // Exportar favoritos para backup\n    async exportFavorites(userId, endpointId) {\n        try {\n            return await this.getFavoriteModels(userId, endpointId);\n        } catch (error) {\n            console.error(\"Error exporting favorites:\", error);\n            throw error;\n        }\n    }\n    // Importar favoritos de backup\n    async importFavorites(userId, endpointId, favorites) {\n        try {\n            const importPromises = favorites.map((favorite)=>this.addToFavorites(userId, endpointId, favorite.modelId, favorite.modelName));\n            await Promise.all(importPromises);\n            console.log(\"Favorites imported successfully\");\n        } catch (error) {\n            console.error(\"Error importing favorites:\", error);\n            throw error;\n        }\n    }\n}\nconst modelFavoritesService = new ModelFavoritesService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/openRouterService.ts":
/*!***********************************************!*\
  !*** ./src/lib/services/openRouterService.ts ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   openRouterService: function() { return /* binding */ openRouterService; }\n/* harmony export */ });\nclass OpenRouterService {\n    async fetchModels() {\n        // Verificar cache\n        if (this.cache && Date.now() - this.cache.timestamp < this.CACHE_DURATION) {\n            return this.cache.models;\n        }\n        try {\n            const response = await fetch(\"https://openrouter.ai/api/v1/models\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(\"your-openrouter-api-key-here\" || 0),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            const models = data.data.map((model)=>({\n                    id: model.id,\n                    name: model.name,\n                    description: model.description || \"\",\n                    context_length: model.context_length,\n                    pricing: {\n                        prompt: model.pricing.prompt,\n                        completion: model.pricing.completion,\n                        image: model.pricing.image\n                    },\n                    architecture: model.architecture,\n                    created: model.created,\n                    isFavorite: false\n                }));\n            // Atualizar cache\n            this.cache = {\n                models,\n                timestamp: Date.now()\n            };\n            return models;\n        } catch (error) {\n            console.error(\"Error fetching OpenRouter models:\", error);\n            // Retornar cache se disponível, mesmo que expirado\n            if (this.cache) {\n                return this.cache.models;\n            }\n            throw error;\n        }\n    }\n    async fetchCredits(apiKey) {\n        // Verificar cache\n        if (this.creditsCache && Date.now() - this.creditsCache.timestamp < this.CREDITS_CACHE_DURATION) {\n            const balance = this.creditsCache.credits.total_credits - this.creditsCache.credits.total_usage;\n            return {\n                balance\n            };\n        }\n        try {\n            const response = await fetch(\"https://openrouter.ai/api/v1/credits\", {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(apiKey),\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            const data = await response.json();\n            const credits = data.data;\n            // Atualizar cache\n            this.creditsCache = {\n                credits,\n                timestamp: Date.now()\n            };\n            const balance = credits.total_credits - credits.total_usage;\n            return {\n                balance\n            };\n        } catch (error) {\n            console.error(\"Error fetching OpenRouter credits:\", error);\n            // Retornar cache se disponível, mesmo que expirado\n            if (this.creditsCache) {\n                const balance = this.creditsCache.credits.total_credits - this.creditsCache.credits.total_usage;\n                return {\n                    balance\n                };\n            }\n            return {\n                balance: 0,\n                error: error instanceof Error ? error.message : \"Erro desconhecido\"\n            };\n        }\n    }\n    filterByCategory(models, category) {\n        switch(category){\n            case \"free\":\n                return models.filter((model)=>this.isFreeModel(model));\n            case \"paid\":\n                return models.filter((model)=>!this.isFreeModel(model));\n            case \"favorites\":\n                return models.filter((model)=>model.isFavorite);\n            default:\n                return models;\n        }\n    }\n    sortModels(models, sortBy) {\n        const sortedModels = [\n            ...models\n        ];\n        switch(sortBy){\n            case \"newest\":\n                return sortedModels.sort((a, b)=>(b.created || 0) - (a.created || 0));\n            case \"price_low\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(a) - this.getTotalPrice(b));\n            case \"price_high\":\n                return sortedModels.sort((a, b)=>this.getTotalPrice(b) - this.getTotalPrice(a));\n            case \"context_high\":\n                return sortedModels.sort((a, b)=>b.context_length - a.context_length);\n            default:\n                return sortedModels;\n        }\n    }\n    isFreeModel(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice === 0 && completionPrice === 0;\n    }\n    getTotalPrice(model) {\n        const promptPrice = parseFloat(model.pricing.prompt);\n        const completionPrice = parseFloat(model.pricing.completion);\n        return promptPrice + completionPrice;\n    }\n    formatPrice(price) {\n        const numPrice = parseFloat(price) * 1000000; // Multiplicar por 1M para mostrar preço por 1M tokens\n        if (numPrice === 0) return \"Gr\\xe1tis\";\n        if (numPrice < 0.001) return \"< $0.001\";\n        return \"$\".concat(numPrice.toFixed(3));\n    }\n    formatContextLength(length) {\n        return length.toLocaleString(); // Mostra o número completo com separadores de milhares\n    }\n    // Busca avançada com fuzzy matching\n    searchModels(models, searchTerm) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const { fuzzyThreshold = 0.6, maxResults = 50, boostFavorites = false } = options;\n        if (!searchTerm.trim()) {\n            return [];\n        }\n        const term = searchTerm.toLowerCase();\n        const results = [];\n        for (const model of models){\n            let score = 0;\n            const matchedFields = [];\n            let highlightedName = model.name;\n            let highlightedDescription = model.description || \"\";\n            // Busca no nome (peso maior)\n            if (model.name.toLowerCase().includes(term)) {\n                score += 10;\n                matchedFields.push(\"name\");\n                highlightedName = this.highlightText(model.name, term);\n            }\n            // Busca no ID (peso médio)\n            if (model.id.toLowerCase().includes(term)) {\n                score += 7;\n                matchedFields.push(\"id\");\n            }\n            // Busca na descrição (peso menor)\n            if (model.description && model.description.toLowerCase().includes(term)) {\n                score += 3;\n                matchedFields.push(\"description\");\n                highlightedDescription = this.highlightText(model.description, term);\n            }\n            // Boost para favoritos\n            if (boostFavorites && model.isFavorite) {\n                score *= 1.5;\n            }\n            // Boost para modelos gratuitos se buscar por \"free\" ou \"grátis\"\n            if ((term.includes(\"free\") || term.includes(\"gr\\xe1tis\")) && this.isFreeModel(model)) {\n                score += 5;\n            }\n            // Boost para modelos caros se buscar por \"expensive\" ou \"caro\"\n            if ((term.includes(\"expensive\") || term.includes(\"caro\")) && this.getTotalPrice(model) > 0.00002) {\n                score += 5;\n            }\n            if (score > 0) {\n                results.push({\n                    model,\n                    score,\n                    matchedFields,\n                    highlightedName,\n                    highlightedDescription\n                });\n            }\n        }\n        // Ordenar por score e limitar resultados\n        return results.sort((a, b)=>b.score - a.score).slice(0, maxResults);\n    }\n    highlightText(text, term) {\n        const regex = new RegExp(\"(\".concat(term, \")\"), \"gi\");\n        return text.replace(regex, '<mark class=\"bg-yellow-300 text-black px-1 rounded\">$1</mark>');\n    }\n    // Limpar cache\n    clearCache() {\n        this.cache = null;\n    }\n    constructor(){\n        this.cache = null;\n        this.creditsCache = null;\n        this.CACHE_DURATION = 5 * 60 * 1000 // 5 minutos\n        ;\n        this.CREDITS_CACHE_DURATION = 30 * 1000 // 30 segundos para créditos\n        ;\n    }\n}\nconst openRouterService = new OpenRouterService();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/openRouterService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/services/settingsService.ts":
/*!*********************************************!*\
  !*** ./src/lib/services/settingsService.ts ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAPIEndpoint: function() { return /* binding */ createAPIEndpoint; },\n/* harmony export */   deleteAPIEndpoint: function() { return /* binding */ deleteAPIEndpoint; },\n/* harmony export */   getActiveAPIEndpoint: function() { return /* binding */ getActiveAPIEndpoint; },\n/* harmony export */   getStreamingSettings: function() { return /* binding */ getStreamingSettings; },\n/* harmony export */   getUserAPIEndpoints: function() { return /* binding */ getUserAPIEndpoints; },\n/* harmony export */   getUserSettings: function() { return /* binding */ getUserSettings; },\n/* harmony export */   setActiveAPIEndpoint: function() { return /* binding */ setActiveAPIEndpoint; },\n/* harmony export */   updateAPIEndpoint: function() { return /* binding */ updateAPIEndpoint; },\n/* harmony export */   updateStreamingSettings: function() { return /* binding */ updateStreamingSettings; },\n/* harmony export */   updateUserSettings: function() { return /* binding */ updateUserSettings; },\n/* harmony export */   uploadProfilePhoto: function() { return /* binding */ uploadProfilePhoto; }\n/* harmony export */ });\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/storage */ \"(app-pages-browser)/./node_modules/firebase/storage/dist/esm/index.esm.js\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n\n\n\n// Serviços para configurações do usuário\nconst updateUserSettings = async (userId, settings)=>{\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId);\n        // Usar setDoc com merge para criar o documento se não existir ou atualizar se existir\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(userRef, settings, {\n            merge: true\n        });\n        console.log(\"User settings updated successfully\");\n    } catch (error) {\n        console.error(\"Error updating user settings:\", error);\n        throw error;\n    }\n};\nconst getUserSettings = async (userId)=>{\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId);\n        const userDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(userRef);\n        if (userDoc.exists()) {\n            const data = userDoc.data();\n            return {\n                username: data.username || \"\",\n                email: data.email || \"\",\n                profilePhotoURL: data.profilePhotoURL,\n                chatAppearance: data.chatAppearance || {\n                    fontSize: 14,\n                    fontFamily: \"Inter\"\n                },\n                streaming: data.streaming || {\n                    enabled: false\n                },\n                chatSessions: data.chatSessions || {\n                    enabled: false,\n                    wordsPerSession: 5000\n                }\n            };\n        }\n        return null;\n    } catch (error) {\n        console.error(\"Error getting user settings:\", error);\n        throw error;\n    }\n};\n// Serviços para foto de perfil\nconst uploadProfilePhoto = async (userId, file)=>{\n    try {\n        // Validar arquivo\n        if (!file.type.startsWith(\"image/\")) {\n            throw new Error(\"Arquivo deve ser uma imagem\");\n        }\n        if (file.size > 5 * 1024 * 1024) {\n            throw new Error(\"Arquivo deve ter menos de 5MB\");\n        }\n        // Upload para Storage\n        const photoRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.storage, \"usuarios/\".concat(userId, \"/configuracoes/foto-perfil\"));\n        await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.uploadBytes)(photoRef, file);\n        // Obter URL de download\n        const downloadURL = await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_1__.getDownloadURL)(photoRef);\n        // Atualizar Firestore\n        await updateUserSettings(userId, {\n            profilePhotoURL: downloadURL\n        });\n        return downloadURL;\n    } catch (error) {\n        console.error(\"Error uploading profile photo:\", error);\n        throw error;\n    }\n};\n// Serviços para API Endpoints\nconst createAPIEndpoint = async (userId, endpoint)=>{\n    try {\n        const endpointId = \"endpoint_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n        const endpointData = {\n            ...endpoint,\n            id: endpointId,\n            createdAt: Date.now()\n        };\n        const endpointRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId, \"endpoints\", endpointId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(endpointRef, endpointData);\n        console.log(\"API endpoint created successfully\");\n        return endpointId;\n    } catch (error) {\n        console.error(\"Error creating API endpoint:\", error);\n        throw error;\n    }\n};\nconst getUserAPIEndpoints = async (userId)=>{\n    try {\n        // Primeiro, tentar buscar na nova estrutura\n        const endpointsRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId, \"endpoints\");\n        const endpointsSnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDocs)(endpointsRef);\n        if (!endpointsSnapshot.empty) {\n            const endpoints = [];\n            endpointsSnapshot.forEach((doc)=>{\n                endpoints.push(doc.data());\n            });\n            // Ordenar por data de criação (mais recente primeiro)\n            endpoints.sort((a, b)=>b.createdAt - a.createdAt);\n            return endpoints;\n        }\n        // Se não encontrou na nova estrutura, buscar na estrutura antiga\n        const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId, \"configuracoes\", \"settings\");\n        const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.getDoc)(configRef);\n        if (configDoc.exists()) {\n            const config = configDoc.data();\n            if (config.endpoints) {\n                const endpoints = [];\n                // Converter da estrutura antiga para a nova\n                Object.entries(config.endpoints).forEach((param)=>{\n                    let [key, endpoint] = param;\n                    endpoints.push({\n                        id: \"legacy_\".concat(key),\n                        name: endpoint.nome || key,\n                        url: endpoint.url || \"\",\n                        apiKey: endpoint.apiKey || \"\",\n                        isActive: endpoint.ativo || false,\n                        createdAt: Date.now(),\n                        settings: {\n                            defaultModel: endpoint.modeloPadrao\n                        }\n                    });\n                });\n                return endpoints;\n            }\n        }\n        // Se não encontrou nada, retornar endpoints padrão\n        return [\n            {\n                id: \"default_openrouter\",\n                name: \"OpenRouter\",\n                url: \"https://openrouter.ai/api/v1/chat/completions\",\n                apiKey: \"\",\n                isActive: false,\n                createdAt: Date.now(),\n                settings: {\n                    defaultModel: \"meta-llama/llama-3.1-8b-instruct:free\"\n                }\n            },\n            {\n                id: \"default_deepseek\",\n                name: \"DeepSeek\",\n                url: \"https://api.deepseek.com/v1/chat/completions\",\n                apiKey: \"\",\n                isActive: false,\n                createdAt: Date.now(),\n                settings: {\n                    defaultModel: \"deepseek-chat\"\n                }\n            }\n        ];\n    } catch (error) {\n        console.error(\"Error getting user API endpoints:\", error);\n        throw error;\n    }\n};\nconst updateAPIEndpoint = async (userId, endpointId, updates)=>{\n    try {\n        const endpointRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId, \"endpoints\", endpointId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.updateDoc)(endpointRef, updates);\n        console.log(\"API endpoint updated successfully\");\n    } catch (error) {\n        console.error(\"Error updating API endpoint:\", error);\n        throw error;\n    }\n};\nconst deleteAPIEndpoint = async (userId, endpointId)=>{\n    try {\n        const endpointRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId, \"endpoints\", endpointId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.deleteDoc)(endpointRef);\n        console.log(\"API endpoint deleted successfully\");\n    } catch (error) {\n        console.error(\"Error deleting API endpoint:\", error);\n        throw error;\n    }\n};\nconst getActiveAPIEndpoint = async (userId)=>{\n    try {\n        const endpoints = await getUserAPIEndpoints(userId);\n        return endpoints.find((endpoint)=>endpoint.isActive) || null;\n    } catch (error) {\n        console.error(\"Error getting active API endpoint:\", error);\n        throw error;\n    }\n};\nconst setActiveAPIEndpoint = async (userId, endpointId, isActive)=>{\n    try {\n        // Apenas ativar/desativar o endpoint específico\n        await updateAPIEndpoint(userId, endpointId, {\n            isActive\n        });\n        console.log(\"API endpoint \".concat(isActive ? \"activated\" : \"deactivated\", \" successfully\"));\n    } catch (error) {\n        console.error(\"Error setting active API endpoint:\", error);\n        throw error;\n    }\n};\n// Serviços para configurações de streaming\nconst updateStreamingSettings = async (userId, streaming)=>{\n    try {\n        const userRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_2__.db, \"usuarios\", userId);\n        await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_0__.setDoc)(userRef, {\n            streaming\n        }, {\n            merge: true\n        });\n        console.log(\"Streaming settings updated successfully\");\n    } catch (error) {\n        console.error(\"Error updating streaming settings:\", error);\n        throw error;\n    }\n};\nconst getStreamingSettings = async (userId)=>{\n    try {\n        const userSettings = await getUserSettings(userId);\n        return (userSettings === null || userSettings === void 0 ? void 0 : userSettings.streaming) || {\n            enabled: false\n        };\n    } catch (error) {\n        console.error(\"Error getting streaming settings:\", error);\n        return {\n            enabled: false\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/settingsService.ts\n"));

/***/ })

});