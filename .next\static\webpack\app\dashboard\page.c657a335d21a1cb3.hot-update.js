"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Load models when endpoint changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n                // Extrair favoritos do cache\n                const cachedFavorites = new Set(cachedData.models.filter((m)=>m.isFavorite).map((m)=>m.id));\n                setFavoriteModelIds(cachedFavorites);\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite: \".concat(model.id, \" (\").concat(model.isFavorite ? \"removing\" : \"adding\", \")\"));\n        // Calcular o novo status otimisticamente\n        const optimisticNewStatus = !model.isFavorite;\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            // ATUALIZAÇÃO OTIMISTA: Atualizar a UI imediatamente\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (optimisticNewStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Atualizar o array de modelos imediatamente\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: optimisticNewStatus\n                    } : m));\n            // Agora fazer a operação no Firestore\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            // Se o status real for diferente do otimista, corrigir\n            if (actualNewStatus !== optimisticNewStatus) {\n                console.warn(\"Optimistic update was incorrect, correcting...\");\n                // Corrigir o estado dos favoritos\n                const correctedFavoriteIds = new Set(favoriteModelIds);\n                if (actualNewStatus) {\n                    correctedFavoriteIds.add(model.id);\n                } else {\n                    correctedFavoriteIds.delete(model.id);\n                }\n                setFavoriteModelIds(correctedFavoriteIds);\n                // Corrigir o array de modelos\n                setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                            ...m,\n                            isFavorite: actualNewStatus\n                        } : m));\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            // Em caso de erro, reverter a atualização otimista\n            const revertedFavoriteIds = new Set(favoriteModelIds);\n            if (!optimisticNewStatus) {\n                revertedFavoriteIds.add(model.id);\n            } else {\n                revertedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(revertedFavoriteIds);\n            // Reverter o array de modelos\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: !optimisticNewStatus\n                    } : m));\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-blue-100\",\n                                            children: \"Selecionar Modelo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-blue-200\",\n                                                    children: \"Endpoint:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                                    onChange: (e)=>{\n                                                        const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                                        setSelectedEndpoint(endpoint || null);\n                                                    },\n                                                    className: \"bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-3 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Selecione um endpoint\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: endpoint.id,\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: endpoint.name\n                                                            }, endpoint.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleRefreshModels,\n                                                    disabled: loading || !selectedEndpoint,\n                                                    className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    title: \"Atualizar modelos\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full max-h-[calc(90vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 border-r border-blue-700/30 bg-blue-900/20 backdrop-blur-sm relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 space-y-4\",\n                                    children: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3\",\n                                                children: \"Categorias\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            [\n                                                \"paid\",\n                                                \"free\",\n                                                \"favorites\"\n                                            ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category\n                                                            })),\n                                                    className: \"w-full text-left py-3 px-4 rounded-xl text-sm font-medium transition-all duration-200 flex items-center space-x-3 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col\",\n                                children: [\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-blue-700/30 space-y-6 relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-blue-400\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 546,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Modelo Customizado\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"text\",\n                                                                        placeholder: \"openai/gpt-4-1\",\n                                                                        value: customModelId,\n                                                                        onChange: (e)=>setCustomModelId(e.target.value),\n                                                                        onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                                        className: \"flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 551,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: handleUseCustomModel,\n                                                                        disabled: !customModelId.trim(),\n                                                                        className: \"px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100\",\n                                                                        children: \"Usar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 550,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-blue-300/70 mt-3\",\n                                                                children: \"Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            value: searchTerm,\n                                                                            onChange: setSearchTerm,\n                                                                            suggestions: [],\n                                                                            isSearching: isSearching,\n                                                                            placeholder: \"Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')\",\n                                                                            showSuggestions: false\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 576,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 575,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: filters.sortBy,\n                                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                                    ...prev,\n                                                                                    sortBy: e.target.value\n                                                                                })),\n                                                                        className: \"bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                                        disabled: hasSearched && searchTerm.trim().length > 0,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"newest\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Mais recentes\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 591,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"price_low\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Menor pre\\xe7o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 592,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"price_high\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Maior pre\\xe7o\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 593,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"context_high\",\n                                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                                children: \"Maior contexto\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 594,\n                                                                                columnNumber: 25\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 585,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            !hasSearched && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>setSelectedCategory(null),\n                                                                        className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 \".concat(!selectedCategory ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                                        children: \"Todos\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 601,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    smartCategories.slice(0, 6).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setSelectedCategory(category.id),\n                                                                            className: \"px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 \".concat(selectedCategory === category.id ? \"bg-blue-600 text-white\" : \"bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200\"),\n                                                                            title: category.description,\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: category.icon\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 622,\n                                                                                    columnNumber: 29\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: category.name\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 623,\n                                                                                    columnNumber: 29\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, category.id, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 612,\n                                                                            columnNumber: 27\n                                                                        }, undefined))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 600,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-300\",\n                                                                        children: [\n                                                                            filteredModels.length,\n                                                                            \" resultado\",\n                                                                            filteredModels.length !== 1 ? \"s\" : \"\",\n                                                                            ' para \"',\n                                                                            searchTerm,\n                                                                            '\"'\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 632,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: clearSearch,\n                                                                        className: \"text-blue-400 hover:text-blue-300 transition-colors duration-200\",\n                                                                        children: \"Limpar busca\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 635,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10\",\n                                                children: [\n                                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center py-8\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 651,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-blue-200 text-sm font-medium\",\n                                                                    children: \"Carregando modelos...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 652,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 650,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"w-4 h-4 text-red-400\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 662,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 661,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-red-300 font-medium\",\n                                                                    children: error\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 665,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-8 h-8 text-blue-400\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 673,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-300 font-medium\",\n                                                                children: hasSearched && searchTerm.trim() ? 'Nenhum resultado para \"'.concat(searchTerm, '\"') : selectedCategory ? \"Nenhum modelo na categoria selecionada\" : \"Nenhum modelo encontrado\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-blue-400/70 text-sm mt-2 space-y-1\",\n                                                                children: hasSearched && searchTerm.trim() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            children: \"Tente:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 688,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                            className: \"list-disc list-inside space-y-1 text-left max-w-xs mx-auto\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Verificar a ortografia\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 690,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Usar termos mais gen\\xe9ricos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 691,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Explorar as categorias\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 692,\n                                                                                    columnNumber: 31\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                                    children: \"Limpar filtros ativos\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                    lineNumber: 693,\n                                                                                    columnNumber: 31\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 689,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Tente ajustar os filtros ou usar as categorias\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 671,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: filteredModels.slice(0, displayedModelsCount).map((model)=>{\n                                                            // Encontrar o resultado da busca para este modelo (se houver)\n                                                            const searchResult = hasSearched ? searchResults.find((r)=>r.model.id === model.id) : null;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                                model: model,\n                                                                isSelected: currentModel === model.id,\n                                                                onSelect: ()=>handleSelectModel(model),\n                                                                onToggleFavorite: ()=>handleToggleFavorite(model),\n                                                                isToggling: favoriteToggling.has(model.id),\n                                                                service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                                searchTerm: hasSearched ? searchTerm : \"\",\n                                                                searchResult: searchResult\n                                                            }, model.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 709,\n                                                                columnNumber: 25\n                                                            }, undefined);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center mt-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLoadMoreModels,\n                                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Carregar mais modelos\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 731,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-4 h-4\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M19 9l-7 7-7-7\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 733,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 727,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center mt-4 space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-blue-400/70\",\n                                                                children: [\n                                                                    \"Mostrando \",\n                                                                    Math.min(displayedModelsCount, filteredModels.length),\n                                                                    \" de \",\n                                                                    filteredModels.length,\n                                                                    \" modelos\",\n                                                                    models.length !== filteredModels.length && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-blue-300\",\n                                                                        children: [\n                                                                            \"(\",\n                                                                            models.length,\n                                                                            \" total)\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            hasSearched && searchTerm.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center space-x-4 text-xs text-blue-400/60\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDD0D Busca: \",\n                                                                            searchTerm\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 754,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    searchResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"⚡ \",\n                                                                            searchResults.length,\n                                                                            \" resultados\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 756,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 753,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-300\",\n                                            children: \"DeepSeek models aqui...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 768,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\" && (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"DeepSeek\" && selectedEndpoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8 text-center relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-blue-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 777,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 776,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 775,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-300 font-medium\",\n                                                    children: \"Sele\\xe7\\xe3o de modelos dispon\\xedvel para OpenRouter e DeepSeek\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 780,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-400/70 text-sm mt-1\",\n                                                    children: \"Selecione um desses endpoints para ver os modelos dispon\\xedveis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 781,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 774,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: handleCancelExpensiveModel\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 790,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 450,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"jlw8EWqkrBmgQiVJdSRGhw1B1SQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service === _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService && _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-model-id\": model.id,\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 837,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 836,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 835,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 834,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-blue-100 truncate\",\n                                                        children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedName) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: searchResult.highlightedName\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 21\n                                                        }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                            text: model.name,\n                                                            highlight: searchTerm\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 21\n                                                        }, undefined) : model.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 848,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium\",\n                                                        children: \"CARO\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 858,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    searchResult && searchResult.matchedFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-1\",\n                                                        children: searchResult.matchedFields.slice(0, 3).map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30\",\n                                                                title: \"Encontrado em: \".concat(field),\n                                                                children: field === \"name\" ? \"\\uD83D\\uDCDD\" : field === \"description\" ? \"\\uD83D\\uDCC4\" : field === \"provider\" ? \"\\uD83C\\uDFE2\" : field === \"tags\" ? \"\\uD83C\\uDFF7️\" : \"\\uD83D\\uDD0D\"\n                                                            }, field, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 23\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 863,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                                    text: model.id,\n                                                    highlight: searchTerm\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    service.isFreeModel(model) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium\",\n                                        children: \"Gr\\xe1tis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-blue-300/80 line-clamp-2\",\n                                    children: (searchResult === null || searchResult === void 0 ? void 0 : searchResult.highlightedDescription) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        dangerouslySetInnerHTML: {\n                                            __html: searchResult.highlightedDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 19\n                                    }, undefined) : searchTerm ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                        text: model.description,\n                                        highlight: searchTerm\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 894,\n                                        columnNumber: 19\n                                    }, undefined) : model.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 890,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 889,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 904,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 905,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 903,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3 border border-blue-600/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 font-medium mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 902,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 844,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed \".concat(model.isFavorite ? \"text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30\"),\n                                title: isToggling ? \"Processando...\" : model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 930,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 932,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 919,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 918,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 843,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 825,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});