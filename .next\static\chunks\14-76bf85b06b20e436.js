(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[14],{4534:function(e,i,o){"use strict";o.d(i,{BH:function(){return Deferred},LL:function(){return ErrorFactory},ZR:function(){return FirebaseError},tV:function(){return base64Decode},L:function(){return base64urlEncodeWithoutPadding},Sg:function(){return createMockUserToken},ne:function(){return createSubscribe},vZ:function(){return function deepEqual(e,i){if(e===i)return!0;let o=Object.keys(e),s=Object.keys(i);for(let h of o){if(!s.includes(h))return!1;let o=e[h],l=i[h];if(isObject(o)&&isObject(l)){if(!deepEqual(o,l))return!1}else if(o!==l)return!1}for(let e of s)if(!o.includes(e))return!1;return!0}},pd:function(){return extractQuerystring},aH:function(){return getDefaultAppConfig},q4:function(){return getDefaultEmulatorHost},P0:function(){return getDefaultEmulatorHostnameAndPort},Pz:function(){return getExperimentalSetting},m9:function(){return getModularInstance},z$:function(){return getUA},ru:function(){return isBrowserExtension},Xx:function(){return isCloudWorkstation},L_:function(){return isCloudflareWorker},xb:function(){return isEmpty},w1:function(){return isIE},hl:function(){return isIndexedDBAvailable},uI:function(){return isMobileCordova},b$:function(){return isReactNative},G6:function(){return isSafari},Uo:function(){return pingServer},xO:function(){return querystring},zd:function(){return querystringDecode},dp:function(){return updateEmulatorBanner},eu:function(){return validateIndexedDBOpenable}});let getDefaultsFromPostinstall=()=>void 0;var s=o(2601);/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let stringToByteArray$1=function(e){let i=[],o=0;for(let s=0;s<e.length;s++){let h=e.charCodeAt(s);h<128?i[o++]=h:(h<2048?i[o++]=h>>6|192:((64512&h)==55296&&s+1<e.length&&(64512&e.charCodeAt(s+1))==56320?(h=65536+((1023&h)<<10)+(1023&e.charCodeAt(++s)),i[o++]=h>>18|240,i[o++]=h>>12&63|128):i[o++]=h>>12|224,i[o++]=h>>6&63|128),i[o++]=63&h|128)}return i},byteArrayToString=function(e){let i=[],o=0,s=0;for(;o<e.length;){let h=e[o++];if(h<128)i[s++]=String.fromCharCode(h);else if(h>191&&h<224){let l=e[o++];i[s++]=String.fromCharCode((31&h)<<6|63&l)}else if(h>239&&h<365){let l=e[o++],f=e[o++],d=e[o++],g=((7&h)<<18|(63&l)<<12|(63&f)<<6|63&d)-65536;i[s++]=String.fromCharCode(55296+(g>>10)),i[s++]=String.fromCharCode(56320+(1023&g))}else{let l=e[o++],f=e[o++];i[s++]=String.fromCharCode((15&h)<<12|(63&l)<<6|63&f)}}return i.join("")},h={byteToCharMap_:null,charToByteMap_:null,byteToCharMapWebSafe_:null,charToByteMapWebSafe_:null,ENCODED_VALS_BASE:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",get ENCODED_VALS(){return this.ENCODED_VALS_BASE+"+/="},get ENCODED_VALS_WEBSAFE(){return this.ENCODED_VALS_BASE+"-_."},HAS_NATIVE_SUPPORT:"function"==typeof atob,encodeByteArray(e,i){if(!Array.isArray(e))throw Error("encodeByteArray takes an array as a parameter");this.init_();let o=i?this.byteToCharMapWebSafe_:this.byteToCharMap_,s=[];for(let i=0;i<e.length;i+=3){let h=e[i],l=i+1<e.length,f=l?e[i+1]:0,d=i+2<e.length,g=d?e[i+2]:0,b=h>>2,w=(3&h)<<4|f>>4,_=(15&f)<<2|g>>6,k=63&g;d||(k=64,l||(_=64)),s.push(o[b],o[w],o[_],o[k])}return s.join("")},encodeString(e,i){return this.HAS_NATIVE_SUPPORT&&!i?btoa(e):this.encodeByteArray(stringToByteArray$1(e),i)},decodeString(e,i){return this.HAS_NATIVE_SUPPORT&&!i?atob(e):byteArrayToString(this.decodeStringToByteArray(e,i))},decodeStringToByteArray(e,i){this.init_();let o=i?this.charToByteMapWebSafe_:this.charToByteMap_,s=[];for(let i=0;i<e.length;){let h=o[e.charAt(i++)],l=i<e.length,f=l?o[e.charAt(i)]:0;++i;let d=i<e.length,g=d?o[e.charAt(i)]:64;++i;let b=i<e.length,w=b?o[e.charAt(i)]:64;if(++i,null==h||null==f||null==g||null==w)throw new DecodeBase64StringError;let _=h<<2|f>>4;if(s.push(_),64!==g){let e=f<<4&240|g>>2;if(s.push(e),64!==w){let e=g<<6&192|w;s.push(e)}}}return s},init_(){if(!this.byteToCharMap_){this.byteToCharMap_={},this.charToByteMap_={},this.byteToCharMapWebSafe_={},this.charToByteMapWebSafe_={};for(let e=0;e<this.ENCODED_VALS.length;e++)this.byteToCharMap_[e]=this.ENCODED_VALS.charAt(e),this.charToByteMap_[this.byteToCharMap_[e]]=e,this.byteToCharMapWebSafe_[e]=this.ENCODED_VALS_WEBSAFE.charAt(e),this.charToByteMapWebSafe_[this.byteToCharMapWebSafe_[e]]=e,e>=this.ENCODED_VALS_BASE.length&&(this.charToByteMap_[this.ENCODED_VALS_WEBSAFE.charAt(e)]=e,this.charToByteMapWebSafe_[this.ENCODED_VALS.charAt(e)]=e)}}};let DecodeBase64StringError=class DecodeBase64StringError extends Error{constructor(){super(...arguments),this.name="DecodeBase64StringError"}};let base64Encode=function(e){let i=stringToByteArray$1(e);return h.encodeByteArray(i,!0)},base64urlEncodeWithoutPadding=function(e){return base64Encode(e).replace(/\./g,"")},base64Decode=function(e){try{return h.decodeString(e,!0)}catch(e){console.error("base64Decode failed: ",e)}return null},getDefaultsFromGlobal=()=>/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==o.g)return o.g;throw Error("Unable to locate global object.")})().__FIREBASE_DEFAULTS__,getDefaultsFromEnvVariable=()=>{if(void 0===s||void 0===s.env)return;let e=s.env.__FIREBASE_DEFAULTS__;if(e)return JSON.parse(e)},getDefaultsFromCookie=()=>{let e;if("undefined"==typeof document)return;try{e=document.cookie.match(/__FIREBASE_DEFAULTS__=([^;]+)/)}catch(e){return}let i=e&&base64Decode(e[1]);return i&&JSON.parse(i)},getDefaults=()=>{try{return getDefaultsFromPostinstall()||getDefaultsFromGlobal()||getDefaultsFromEnvVariable()||getDefaultsFromCookie()}catch(e){console.info(`Unable to get __FIREBASE_DEFAULTS__ due to: ${e}`);return}},getDefaultEmulatorHost=e=>getDefaults()?.emulatorHosts?.[e],getDefaultEmulatorHostnameAndPort=e=>{let i=getDefaultEmulatorHost(e);if(!i)return;let o=i.lastIndexOf(":");if(o<=0||o+1===i.length)throw Error(`Invalid host ${i} with no separate hostname and port!`);let s=parseInt(i.substring(o+1),10);return"["===i[0]?[i.substring(1,o-1),s]:[i.substring(0,o),s]},getDefaultAppConfig=()=>getDefaults()?.config,getExperimentalSetting=e=>getDefaults()?.[`_${e}`];/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Deferred=class Deferred{constructor(){this.reject=()=>{},this.resolve=()=>{},this.promise=new Promise((e,i)=>{this.resolve=e,this.reject=i})}wrapCallback(e){return(i,o)=>{i?this.reject(i):this.resolve(o),"function"==typeof e&&(this.promise.catch(()=>{}),1===e.length?e(i):e(i,o))}}};/**
 * @license
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function isCloudWorkstation(e){try{let i=e.startsWith("http://")||e.startsWith("https://")?new URL(e).hostname:e;return i.endsWith(".cloudworkstations.dev")}catch{return!1}}async function pingServer(e){let i=await fetch(e,{credentials:"include"});return i.ok}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function createMockUserToken(e,i){if(e.uid)throw Error('The "uid" field is no longer supported by mockUserToken. Please use "sub" instead for Firebase Auth User ID.');let o=i||"demo-project",s=e.iat||0,h=e.sub||e.user_id;if(!h)throw Error("mockUserToken must contain 'sub' or 'user_id' field!");let l={iss:`https://securetoken.google.com/${o}`,aud:o,iat:s,exp:s+3600,auth_time:s,sub:h,user_id:h,firebase:{sign_in_provider:"custom",identities:{}},...e};return[base64urlEncodeWithoutPadding(JSON.stringify({alg:"none",type:"JWT"})),base64urlEncodeWithoutPadding(JSON.stringify(l)),""].join(".")}let l={},f=!1;function updateEmulatorBanner(e,i){if("undefined"==typeof window||"undefined"==typeof document||!isCloudWorkstation(window.location.host)||l[e]===i||l[e]||f)return;function prefixedId(e){return`__firebase__banner__${e}`}l[e]=i;let o="__firebase__banner",s=function(){let e={prod:[],emulator:[]};for(let i of Object.keys(l))l[i]?e.emulator.push(i):e.prod.push(i);return e}(),h=s.prod.length>0;function setupDom(){let e,i;let s=(e=document.getElementById(o),i=!1,e||((e=document.createElement("div")).setAttribute("id",o),i=!0),{created:i,element:e}),l=prefixedId("text"),d=document.getElementById(l)||document.createElement("span"),g=prefixedId("learnmore"),b=document.getElementById(g)||document.createElement("a"),w=prefixedId("preprendIcon"),_=document.getElementById(w)||document.createElementNS("http://www.w3.org/2000/svg","svg");if(s.created){let e=s.element;e.style.display="flex",e.style.background="#7faaf0",e.style.position="fixed",e.style.bottom="5px",e.style.left="5px",e.style.padding=".5em",e.style.borderRadius="5px",e.style.alignItems="center",b.setAttribute("id",g),b.innerText="Learn more",b.href="https://firebase.google.com/docs/studio/preview-apps#preview-backend",b.setAttribute("target","__blank"),b.style.paddingLeft="5px",b.style.textDecoration="underline";let i=function(){let e=document.createElement("span");return e.style.cursor="pointer",e.style.marginLeft="16px",e.style.fontSize="24px",e.innerHTML=" &times;",e.onclick=()=>{f=!0,function(){let e=document.getElementById(o);e&&e.remove()}()},e}();_.setAttribute("width","24"),_.setAttribute("id",w),_.setAttribute("height","24"),_.setAttribute("viewBox","0 0 24 24"),_.setAttribute("fill","none"),_.style.marginLeft="-6px",e.append(_,d,b,i),document.body.appendChild(e)}h?(d.innerText="Preview backend disconnected.",_.innerHTML=`<g clip-path="url(#clip0_6013_33858)">
<path d="M4.8 17.6L12 5.6L19.2 17.6H4.8ZM6.91667 16.4H17.0833L12 7.93333L6.91667 16.4ZM12 15.6C12.1667 15.6 12.3056 15.5444 12.4167 15.4333C12.5389 15.3111 12.6 15.1667 12.6 15C12.6 14.8333 12.5389 14.6944 12.4167 14.5833C12.3056 14.4611 12.1667 14.4 12 14.4C11.8333 14.4 11.6889 14.4611 11.5667 14.5833C11.4556 14.6944 11.4 14.8333 11.4 15C11.4 15.1667 11.4556 15.3111 11.5667 15.4333C11.6889 15.5444 11.8333 15.6 12 15.6ZM11.4 13.6H12.6V10.4H11.4V13.6Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6013_33858">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`):(_.innerHTML=`<g clip-path="url(#clip0_6083_34804)">
<path d="M11.4 15.2H12.6V11.2H11.4V15.2ZM12 10C12.1667 10 12.3056 9.94444 12.4167 9.83333C12.5389 9.71111 12.6 9.56667 12.6 9.4C12.6 9.23333 12.5389 9.09444 12.4167 8.98333C12.3056 8.86111 12.1667 8.8 12 8.8C11.8333 8.8 11.6889 8.86111 11.5667 8.98333C11.4556 9.09444 11.4 9.23333 11.4 9.4C11.4 9.56667 11.4556 9.71111 11.5667 9.83333C11.6889 9.94444 11.8333 10 12 10ZM12 18.4C11.1222 18.4 10.2944 18.2333 9.51667 17.9C8.73889 17.5667 8.05556 17.1111 7.46667 16.5333C6.88889 15.9444 6.43333 15.2611 6.1 14.4833C5.76667 13.7056 5.6 12.8778 5.6 12C5.6 11.1111 5.76667 10.2833 6.1 9.51667C6.43333 8.73889 6.88889 8.06111 7.46667 7.48333C8.05556 6.89444 8.73889 6.43333 9.51667 6.1C10.2944 5.76667 11.1222 5.6 12 5.6C12.8889 5.6 13.7167 5.76667 14.4833 6.1C15.2611 6.43333 15.9389 6.89444 16.5167 7.48333C17.1056 8.06111 17.5667 8.73889 17.9 9.51667C18.2333 10.2833 18.4 11.1111 18.4 12C18.4 12.8778 18.2333 13.7056 17.9 14.4833C17.5667 15.2611 17.1056 15.9444 16.5167 16.5333C15.9389 17.1111 15.2611 17.5667 14.4833 17.9C13.7167 18.2333 12.8889 18.4 12 18.4ZM12 17.2C13.4444 17.2 14.6722 16.6944 15.6833 15.6833C16.6944 14.6722 17.2 13.4444 17.2 12C17.2 10.5556 16.6944 9.32778 15.6833 8.31667C14.6722 7.30555 13.4444 6.8 12 6.8C10.5556 6.8 9.32778 7.30555 8.31667 8.31667C7.30556 9.32778 6.8 10.5556 6.8 12C6.8 13.4444 7.30556 14.6722 8.31667 15.6833C9.32778 16.6944 10.5556 17.2 12 17.2Z" fill="#212121"/>
</g>
<defs>
<clipPath id="clip0_6083_34804">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>`,d.innerText="Preview backend running in this workspace."),d.setAttribute("id",l)}"loading"===document.readyState?window.addEventListener("DOMContentLoaded",setupDom):setupDom()}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function getUA(){return"undefined"!=typeof navigator&&"string"==typeof navigator.userAgent?navigator.userAgent:""}function isMobileCordova(){return"undefined"!=typeof window&&!!(window.cordova||window.phonegap||window.PhoneGap)&&/ios|iphone|ipod|ipad|android|blackberry|iemobile/i.test(getUA())}function isCloudflareWorker(){return"undefined"!=typeof navigator&&"Cloudflare-Workers"===navigator.userAgent}function isBrowserExtension(){let e="object"==typeof chrome?chrome.runtime:"object"==typeof browser?browser.runtime:void 0;return"object"==typeof e&&void 0!==e.id}function isReactNative(){return"object"==typeof navigator&&"ReactNative"===navigator.product}function isIE(){let e=getUA();return e.indexOf("MSIE ")>=0||e.indexOf("Trident/")>=0}function isSafari(){return!function(){let e=getDefaults()?.forceEnvironment;if("node"===e)return!0;if("browser"===e)return!1;try{return"[object process]"===Object.prototype.toString.call(o.g.process)}catch(e){return!1}}()&&!!navigator.userAgent&&navigator.userAgent.includes("Safari")&&!navigator.userAgent.includes("Chrome")}function isIndexedDBAvailable(){try{return"object"==typeof indexedDB}catch(e){return!1}}function validateIndexedDBOpenable(){return new Promise((e,i)=>{try{let o=!0,s="validate-browser-context-for-indexeddb-analytics-module",h=self.indexedDB.open(s);h.onsuccess=()=>{h.result.close(),o||self.indexedDB.deleteDatabase(s),e(!0)},h.onupgradeneeded=()=>{o=!1},h.onerror=()=>{i(h.error?.message||"")}}catch(e){i(e)}})}let FirebaseError=class FirebaseError extends Error{constructor(e,i,o){super(i),this.code=e,this.customData=o,this.name="FirebaseError",Object.setPrototypeOf(this,FirebaseError.prototype),Error.captureStackTrace&&Error.captureStackTrace(this,ErrorFactory.prototype.create)}};let ErrorFactory=class ErrorFactory{constructor(e,i,o){this.service=e,this.serviceName=i,this.errors=o}create(e,...i){let o=i[0]||{},s=`${this.service}/${e}`,h=this.errors[e],l=h?h.replace(d,(e,i)=>{let s=o[i];return null!=s?String(s):`<${i}?>`}):"Error",f=`${this.serviceName}: ${l} (${s}).`,g=new FirebaseError(s,f,o);return g}};let d=/\{\$([^}]+)}/g;function isEmpty(e){for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i))return!1;return!0}function isObject(e){return null!==e&&"object"==typeof e}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function querystring(e){let i=[];for(let[o,s]of Object.entries(e))Array.isArray(s)?s.forEach(e=>{i.push(encodeURIComponent(o)+"="+encodeURIComponent(e))}):i.push(encodeURIComponent(o)+"="+encodeURIComponent(s));return i.length?"&"+i.join("&"):""}function querystringDecode(e){let i={},o=e.replace(/^\?/,"").split("&");return o.forEach(e=>{if(e){let[o,s]=e.split("=");i[decodeURIComponent(o)]=decodeURIComponent(s)}}),i}function extractQuerystring(e){let i=e.indexOf("?");if(!i)return"";let o=e.indexOf("#",i);return e.substring(i,o>0?o:void 0)}function createSubscribe(e,i){let o=new ObserverProxy(e,i);return o.subscribe.bind(o)}let ObserverProxy=class ObserverProxy{constructor(e,i){this.observers=[],this.unsubscribes=[],this.observerCount=0,this.task=Promise.resolve(),this.finalized=!1,this.onNoObservers=i,this.task.then(()=>{e(this)}).catch(e=>{this.error(e)})}next(e){this.forEachObserver(i=>{i.next(e)})}error(e){this.forEachObserver(i=>{i.error(e)}),this.close(e)}complete(){this.forEachObserver(e=>{e.complete()}),this.close()}subscribe(e,i,o){let s;if(void 0===e&&void 0===i&&void 0===o)throw Error("Missing Observer.");void 0===(s=!function(e,i){if("object"!=typeof e||null===e)return!1;for(let o of i)if(o in e&&"function"==typeof e[o])return!0;return!1}(e,["next","error","complete"])?{next:e,error:i,complete:o}:e).next&&(s.next=noop),void 0===s.error&&(s.error=noop),void 0===s.complete&&(s.complete=noop);let h=this.unsubscribeOne.bind(this,this.observers.length);return this.finalized&&this.task.then(()=>{try{this.finalError?s.error(this.finalError):s.complete()}catch(e){}}),this.observers.push(s),h}unsubscribeOne(e){void 0!==this.observers&&void 0!==this.observers[e]&&(delete this.observers[e],this.observerCount-=1,0===this.observerCount&&void 0!==this.onNoObservers&&this.onNoObservers(this))}forEachObserver(e){if(!this.finalized)for(let i=0;i<this.observers.length;i++)this.sendOne(i,e)}sendOne(e,i){this.task.then(()=>{if(void 0!==this.observers&&void 0!==this.observers[e])try{i(this.observers[e])}catch(e){"undefined"!=typeof console&&console.error&&console.error(e)}})}close(e){this.finalized||(this.finalized=!0,void 0!==e&&(this.finalError=e),this.task.then(()=>{this.observers=void 0,this.onNoObservers=void 0}))}};function noop(){}/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function getModularInstance(e){return e&&e._delegate?e._delegate:e}},2601:function(e,i,o){"use strict";var s,h;e.exports=(null==(s=o.g.process)?void 0:s.env)&&"object"==typeof(null==(h=o.g.process)?void 0:h.env)?o.g.process:o(8960)},263:function(e){!function(){var i={675:function(e,i){"use strict";i.byteLength=function(e){var i=getLens(e),o=i[0],s=i[1];return(o+s)*3/4-s},i.toByteArray=function(e){var i,o,l=getLens(e),f=l[0],d=l[1],g=new h((f+d)*3/4-d),b=0,w=d>0?f-4:f;for(o=0;o<w;o+=4)i=s[e.charCodeAt(o)]<<18|s[e.charCodeAt(o+1)]<<12|s[e.charCodeAt(o+2)]<<6|s[e.charCodeAt(o+3)],g[b++]=i>>16&255,g[b++]=i>>8&255,g[b++]=255&i;return 2===d&&(i=s[e.charCodeAt(o)]<<2|s[e.charCodeAt(o+1)]>>4,g[b++]=255&i),1===d&&(i=s[e.charCodeAt(o)]<<10|s[e.charCodeAt(o+1)]<<4|s[e.charCodeAt(o+2)]>>2,g[b++]=i>>8&255,g[b++]=255&i),g},i.fromByteArray=function(e){for(var i,s=e.length,h=s%3,l=[],f=0,d=s-h;f<d;f+=16383)l.push(function(e,i,s){for(var h,l=[],f=i;f<s;f+=3)l.push(o[(h=(e[f]<<16&16711680)+(e[f+1]<<8&65280)+(255&e[f+2]))>>18&63]+o[h>>12&63]+o[h>>6&63]+o[63&h]);return l.join("")}(e,f,f+16383>d?d:f+16383));return 1===h?l.push(o[(i=e[s-1])>>2]+o[i<<4&63]+"=="):2===h&&l.push(o[(i=(e[s-2]<<8)+e[s-1])>>10]+o[i>>4&63]+o[i<<2&63]+"="),l.join("")};for(var o=[],s=[],h="undefined"!=typeof Uint8Array?Uint8Array:Array,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,d=l.length;f<d;++f)o[f]=l[f],s[l.charCodeAt(f)]=f;function getLens(e){var i=e.length;if(i%4>0)throw Error("Invalid string. Length must be a multiple of 4");var o=e.indexOf("=");-1===o&&(o=i);var s=o===i?0:4-o%4;return[o,s]}s["-".charCodeAt(0)]=62,s["_".charCodeAt(0)]=63},72:function(e,i,o){"use strict";/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var s=o(675),h=o(783),l="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function createBuffer(e){if(e>**********)throw RangeError('The value "'+e+'" is invalid for option "size"');var i=new Uint8Array(e);return Object.setPrototypeOf(i,Buffer.prototype),i}function Buffer(e,i,o){if("number"==typeof e){if("string"==typeof i)throw TypeError('The "string" argument must be of type string. Received type number');return allocUnsafe(e)}return from(e,i,o)}function from(e,i,o){if("string"==typeof e)return function(e,i){if(("string"!=typeof i||""===i)&&(i="utf8"),!Buffer.isEncoding(i))throw TypeError("Unknown encoding: "+i);var o=0|byteLength(e,i),s=createBuffer(o),h=s.write(e,i);return h!==o&&(s=s.slice(0,h)),s}(e,i);if(ArrayBuffer.isView(e))return fromArrayLike(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(isInstance(e,ArrayBuffer)||e&&isInstance(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(isInstance(e,SharedArrayBuffer)||e&&isInstance(e.buffer,SharedArrayBuffer)))return function(e,i,o){var s;if(i<0||e.byteLength<i)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<i+(o||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(s=void 0===i&&void 0===o?new Uint8Array(e):void 0===o?new Uint8Array(e,i):new Uint8Array(e,i,o),Buffer.prototype),s}(e,i,o);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return Buffer.from(s,i,o);var h=function(e){if(Buffer.isBuffer(e)){var i,o=0|checked(e.length),s=createBuffer(o);return 0===s.length||e.copy(s,0,0,o),s}return void 0!==e.length?"number"!=typeof e.length||(i=e.length)!=i?createBuffer(0):fromArrayLike(e):"Buffer"===e.type&&Array.isArray(e.data)?fromArrayLike(e.data):void 0}(e);if(h)return h;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return Buffer.from(e[Symbol.toPrimitive]("string"),i,o);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function assertSize(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function allocUnsafe(e){return assertSize(e),createBuffer(e<0?0:0|checked(e))}function fromArrayLike(e){for(var i=e.length<0?0:0|checked(e.length),o=createBuffer(i),s=0;s<i;s+=1)o[s]=255&e[s];return o}function checked(e){if(e>=**********)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function byteLength(e,i){if(Buffer.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||isInstance(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var o=e.length,s=arguments.length>2&&!0===arguments[2];if(!s&&0===o)return 0;for(var h=!1;;)switch(i){case"ascii":case"latin1":case"binary":return o;case"utf8":case"utf-8":return utf8ToBytes(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*o;case"hex":return o>>>1;case"base64":return base64ToBytes(e).length;default:if(h)return s?-1:utf8ToBytes(e).length;i=(""+i).toLowerCase(),h=!0}}function slowToString(e,i,o){var h,l,f=!1;if((void 0===i||i<0)&&(i=0),i>this.length||((void 0===o||o>this.length)&&(o=this.length),o<=0||(o>>>=0)<=(i>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,i,o){var s=e.length;(!i||i<0)&&(i=0),(!o||o<0||o>s)&&(o=s);for(var h="",l=i;l<o;++l)h+=d[e[l]];return h}(this,i,o);case"utf8":case"utf-8":return utf8Slice(this,i,o);case"ascii":return function(e,i,o){var s="";o=Math.min(e.length,o);for(var h=i;h<o;++h)s+=String.fromCharCode(127&e[h]);return s}(this,i,o);case"latin1":case"binary":return function(e,i,o){var s="";o=Math.min(e.length,o);for(var h=i;h<o;++h)s+=String.fromCharCode(e[h]);return s}(this,i,o);case"base64":return h=i,l=o,0===h&&l===this.length?s.fromByteArray(this):s.fromByteArray(this.slice(h,l));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,i,o){for(var s=e.slice(i,o),h="",l=0;l<s.length;l+=2)h+=String.fromCharCode(s[l]+256*s[l+1]);return h}(this,i,o);default:if(f)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),f=!0}}function swap(e,i,o){var s=e[i];e[i]=e[o],e[o]=s}function bidirectionalIndexOf(e,i,o,s,h){var l;if(0===e.length)return -1;if("string"==typeof o?(s=o,o=0):o>**********?o=**********:o<-2147483648&&(o=-2147483648),(l=o=+o)!=l&&(o=h?0:e.length-1),o<0&&(o=e.length+o),o>=e.length){if(h)return -1;o=e.length-1}else if(o<0){if(!h)return -1;o=0}if("string"==typeof i&&(i=Buffer.from(i,s)),Buffer.isBuffer(i))return 0===i.length?-1:arrayIndexOf(e,i,o,s,h);if("number"==typeof i)return(i&=255,"function"==typeof Uint8Array.prototype.indexOf)?h?Uint8Array.prototype.indexOf.call(e,i,o):Uint8Array.prototype.lastIndexOf.call(e,i,o):arrayIndexOf(e,[i],o,s,h);throw TypeError("val must be string, number or Buffer")}function arrayIndexOf(e,i,o,s,h){var l,f=1,d=e.length,g=i.length;if(void 0!==s&&("ucs2"===(s=String(s).toLowerCase())||"ucs-2"===s||"utf16le"===s||"utf-16le"===s)){if(e.length<2||i.length<2)return -1;f=2,d/=2,g/=2,o/=2}function read(e,i){return 1===f?e[i]:e.readUInt16BE(i*f)}if(h){var b=-1;for(l=o;l<d;l++)if(read(e,l)===read(i,-1===b?0:l-b)){if(-1===b&&(b=l),l-b+1===g)return b*f}else -1!==b&&(l-=l-b),b=-1}else for(o+g>d&&(o=d-g),l=o;l>=0;l--){for(var w=!0,_=0;_<g;_++)if(read(e,l+_)!==read(i,_)){w=!1;break}if(w)return l}return -1}function utf8Slice(e,i,o){o=Math.min(e.length,o);for(var s=[],h=i;h<o;){var l,f,d,g,b=e[h],w=null,_=b>239?4:b>223?3:b>191?2:1;if(h+_<=o)switch(_){case 1:b<128&&(w=b);break;case 2:(192&(l=e[h+1]))==128&&(g=(31&b)<<6|63&l)>127&&(w=g);break;case 3:l=e[h+1],f=e[h+2],(192&l)==128&&(192&f)==128&&(g=(15&b)<<12|(63&l)<<6|63&f)>2047&&(g<55296||g>57343)&&(w=g);break;case 4:l=e[h+1],f=e[h+2],d=e[h+3],(192&l)==128&&(192&f)==128&&(192&d)==128&&(g=(15&b)<<18|(63&l)<<12|(63&f)<<6|63&d)>65535&&g<1114112&&(w=g)}null===w?(w=65533,_=1):w>65535&&(w-=65536,s.push(w>>>10&1023|55296),w=56320|1023&w),s.push(w),h+=_}return function(e){var i=e.length;if(i<=4096)return String.fromCharCode.apply(String,e);for(var o="",s=0;s<i;)o+=String.fromCharCode.apply(String,e.slice(s,s+=4096));return o}(s)}function checkOffset(e,i,o){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+i>o)throw RangeError("Trying to access beyond buffer length")}function checkInt(e,i,o,s,h,l){if(!Buffer.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(i>h||i<l)throw RangeError('"value" argument is out of bounds');if(o+s>e.length)throw RangeError("Index out of range")}function checkIEEE754(e,i,o,s,h,l){if(o+s>e.length||o<0)throw RangeError("Index out of range")}function writeFloat(e,i,o,s,l){return i=+i,o>>>=0,l||checkIEEE754(e,i,o,4,34028234663852886e22,-34028234663852886e22),h.write(e,i,o,s,23,4),o+4}function writeDouble(e,i,o,s,l){return i=+i,o>>>=0,l||checkIEEE754(e,i,o,8,17976931348623157e292,-17976931348623157e292),h.write(e,i,o,s,52,8),o+8}i.Buffer=Buffer,i.SlowBuffer=function(e){return+e!=e&&(e=0),Buffer.alloc(+e)},i.INSPECT_MAX_BYTES=50,i.kMaxLength=**********,Buffer.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),i={foo:function(){return 42}};return Object.setPrototypeOf(i,Uint8Array.prototype),Object.setPrototypeOf(e,i),42===e.foo()}catch(e){return!1}}(),Buffer.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(Buffer.prototype,"parent",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.buffer}}),Object.defineProperty(Buffer.prototype,"offset",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.byteOffset}}),Buffer.poolSize=8192,Buffer.from=function(e,i,o){return from(e,i,o)},Object.setPrototypeOf(Buffer.prototype,Uint8Array.prototype),Object.setPrototypeOf(Buffer,Uint8Array),Buffer.alloc=function(e,i,o){return(assertSize(e),e<=0)?createBuffer(e):void 0!==i?"string"==typeof o?createBuffer(e).fill(i,o):createBuffer(e).fill(i):createBuffer(e)},Buffer.allocUnsafe=function(e){return allocUnsafe(e)},Buffer.allocUnsafeSlow=function(e){return allocUnsafe(e)},Buffer.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==Buffer.prototype},Buffer.compare=function(e,i){if(isInstance(e,Uint8Array)&&(e=Buffer.from(e,e.offset,e.byteLength)),isInstance(i,Uint8Array)&&(i=Buffer.from(i,i.offset,i.byteLength)),!Buffer.isBuffer(e)||!Buffer.isBuffer(i))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===i)return 0;for(var o=e.length,s=i.length,h=0,l=Math.min(o,s);h<l;++h)if(e[h]!==i[h]){o=e[h],s=i[h];break}return o<s?-1:s<o?1:0},Buffer.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Buffer.concat=function(e,i){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return Buffer.alloc(0);if(void 0===i)for(o=0,i=0;o<e.length;++o)i+=e[o].length;var o,s=Buffer.allocUnsafe(i),h=0;for(o=0;o<e.length;++o){var l=e[o];if(isInstance(l,Uint8Array)&&(l=Buffer.from(l)),!Buffer.isBuffer(l))throw TypeError('"list" argument must be an Array of Buffers');l.copy(s,h),h+=l.length}return s},Buffer.byteLength=byteLength,Buffer.prototype._isBuffer=!0,Buffer.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var i=0;i<e;i+=2)swap(this,i,i+1);return this},Buffer.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var i=0;i<e;i+=4)swap(this,i,i+3),swap(this,i+1,i+2);return this},Buffer.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var i=0;i<e;i+=8)swap(this,i,i+7),swap(this,i+1,i+6),swap(this,i+2,i+5),swap(this,i+3,i+4);return this},Buffer.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?utf8Slice(this,0,e):slowToString.apply(this,arguments)},Buffer.prototype.toLocaleString=Buffer.prototype.toString,Buffer.prototype.equals=function(e){if(!Buffer.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===Buffer.compare(this,e)},Buffer.prototype.inspect=function(){var e="",o=i.INSPECT_MAX_BYTES;return e=this.toString("hex",0,o).replace(/(.{2})/g,"$1 ").trim(),this.length>o&&(e+=" ... "),"<Buffer "+e+">"},l&&(Buffer.prototype[l]=Buffer.prototype.inspect),Buffer.prototype.compare=function(e,i,o,s,h){if(isInstance(e,Uint8Array)&&(e=Buffer.from(e,e.offset,e.byteLength)),!Buffer.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===i&&(i=0),void 0===o&&(o=e?e.length:0),void 0===s&&(s=0),void 0===h&&(h=this.length),i<0||o>e.length||s<0||h>this.length)throw RangeError("out of range index");if(s>=h&&i>=o)return 0;if(s>=h)return -1;if(i>=o)return 1;if(i>>>=0,o>>>=0,s>>>=0,h>>>=0,this===e)return 0;for(var l=h-s,f=o-i,d=Math.min(l,f),g=this.slice(s,h),b=e.slice(i,o),w=0;w<d;++w)if(g[w]!==b[w]){l=g[w],f=b[w];break}return l<f?-1:f<l?1:0},Buffer.prototype.includes=function(e,i,o){return -1!==this.indexOf(e,i,o)},Buffer.prototype.indexOf=function(e,i,o){return bidirectionalIndexOf(this,e,i,o,!0)},Buffer.prototype.lastIndexOf=function(e,i,o){return bidirectionalIndexOf(this,e,i,o,!1)},Buffer.prototype.write=function(e,i,o,s){if(void 0===i)s="utf8",o=this.length,i=0;else if(void 0===o&&"string"==typeof i)s=i,o=this.length,i=0;else if(isFinite(i))i>>>=0,isFinite(o)?(o>>>=0,void 0===s&&(s="utf8")):(s=o,o=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var h,l,f,d,g,b,w,_,k,O,j,$,tt=this.length-i;if((void 0===o||o>tt)&&(o=tt),e.length>0&&(o<0||i<0)||i>this.length)throw RangeError("Attempt to write outside buffer bounds");s||(s="utf8");for(var te=!1;;)switch(s){case"hex":return function(e,i,o,s){o=Number(o)||0;var h=e.length-o;s?(s=Number(s))>h&&(s=h):s=h;var l=i.length;s>l/2&&(s=l/2);for(var f=0;f<s;++f){var d=parseInt(i.substr(2*f,2),16);if(d!=d)break;e[o+f]=d}return f}(this,e,i,o);case"utf8":case"utf-8":return g=i,b=o,blitBuffer(utf8ToBytes(e,this.length-g),this,g,b);case"ascii":return w=i,_=o,blitBuffer(asciiToBytes(e),this,w,_);case"latin1":case"binary":return h=this,l=e,f=i,d=o,blitBuffer(asciiToBytes(l),h,f,d);case"base64":return k=i,O=o,blitBuffer(base64ToBytes(e),this,k,O);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return j=i,$=o,blitBuffer(function(e,i){for(var o,s,h=[],l=0;l<e.length&&!((i-=2)<0);++l)s=(o=e.charCodeAt(l))>>8,h.push(o%256),h.push(s);return h}(e,this.length-j),this,j,$);default:if(te)throw TypeError("Unknown encoding: "+s);s=(""+s).toLowerCase(),te=!0}},Buffer.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},Buffer.prototype.slice=function(e,i){var o=this.length;e=~~e,i=void 0===i?o:~~i,e<0?(e+=o)<0&&(e=0):e>o&&(e=o),i<0?(i+=o)<0&&(i=0):i>o&&(i=o),i<e&&(i=e);var s=this.subarray(e,i);return Object.setPrototypeOf(s,Buffer.prototype),s},Buffer.prototype.readUIntLE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=this[e],h=1,l=0;++l<i&&(h*=256);)s+=this[e+l]*h;return s},Buffer.prototype.readUIntBE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=this[e+--i],h=1;i>0&&(h*=256);)s+=this[e+--i]*h;return s},Buffer.prototype.readUInt8=function(e,i){return e>>>=0,i||checkOffset(e,1,this.length),this[e]},Buffer.prototype.readUInt16LE=function(e,i){return e>>>=0,i||checkOffset(e,2,this.length),this[e]|this[e+1]<<8},Buffer.prototype.readUInt16BE=function(e,i){return e>>>=0,i||checkOffset(e,2,this.length),this[e]<<8|this[e+1]},Buffer.prototype.readUInt32LE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},Buffer.prototype.readUInt32BE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},Buffer.prototype.readIntLE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=this[e],h=1,l=0;++l<i&&(h*=256);)s+=this[e+l]*h;return s>=(h*=128)&&(s-=Math.pow(2,8*i)),s},Buffer.prototype.readIntBE=function(e,i,o){e>>>=0,i>>>=0,o||checkOffset(e,i,this.length);for(var s=i,h=1,l=this[e+--s];s>0&&(h*=256);)l+=this[e+--s]*h;return l>=(h*=128)&&(l-=Math.pow(2,8*i)),l},Buffer.prototype.readInt8=function(e,i){return(e>>>=0,i||checkOffset(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},Buffer.prototype.readInt16LE=function(e,i){e>>>=0,i||checkOffset(e,2,this.length);var o=this[e]|this[e+1]<<8;return 32768&o?4294901760|o:o},Buffer.prototype.readInt16BE=function(e,i){e>>>=0,i||checkOffset(e,2,this.length);var o=this[e+1]|this[e]<<8;return 32768&o?4294901760|o:o},Buffer.prototype.readInt32LE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},Buffer.prototype.readInt32BE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},Buffer.prototype.readFloatLE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),h.read(this,e,!0,23,4)},Buffer.prototype.readFloatBE=function(e,i){return e>>>=0,i||checkOffset(e,4,this.length),h.read(this,e,!1,23,4)},Buffer.prototype.readDoubleLE=function(e,i){return e>>>=0,i||checkOffset(e,8,this.length),h.read(this,e,!0,52,8)},Buffer.prototype.readDoubleBE=function(e,i){return e>>>=0,i||checkOffset(e,8,this.length),h.read(this,e,!1,52,8)},Buffer.prototype.writeUIntLE=function(e,i,o,s){if(e=+e,i>>>=0,o>>>=0,!s){var h=Math.pow(2,8*o)-1;checkInt(this,e,i,o,h,0)}var l=1,f=0;for(this[i]=255&e;++f<o&&(l*=256);)this[i+f]=e/l&255;return i+o},Buffer.prototype.writeUIntBE=function(e,i,o,s){if(e=+e,i>>>=0,o>>>=0,!s){var h=Math.pow(2,8*o)-1;checkInt(this,e,i,o,h,0)}var l=o-1,f=1;for(this[i+l]=255&e;--l>=0&&(f*=256);)this[i+l]=e/f&255;return i+o},Buffer.prototype.writeUInt8=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,1,255,0),this[i]=255&e,i+1},Buffer.prototype.writeUInt16LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,65535,0),this[i]=255&e,this[i+1]=e>>>8,i+2},Buffer.prototype.writeUInt16BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,65535,0),this[i]=e>>>8,this[i+1]=255&e,i+2},Buffer.prototype.writeUInt32LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,4294967295,0),this[i+3]=e>>>24,this[i+2]=e>>>16,this[i+1]=e>>>8,this[i]=255&e,i+4},Buffer.prototype.writeUInt32BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,4294967295,0),this[i]=e>>>24,this[i+1]=e>>>16,this[i+2]=e>>>8,this[i+3]=255&e,i+4},Buffer.prototype.writeIntLE=function(e,i,o,s){if(e=+e,i>>>=0,!s){var h=Math.pow(2,8*o-1);checkInt(this,e,i,o,h-1,-h)}var l=0,f=1,d=0;for(this[i]=255&e;++l<o&&(f*=256);)e<0&&0===d&&0!==this[i+l-1]&&(d=1),this[i+l]=(e/f>>0)-d&255;return i+o},Buffer.prototype.writeIntBE=function(e,i,o,s){if(e=+e,i>>>=0,!s){var h=Math.pow(2,8*o-1);checkInt(this,e,i,o,h-1,-h)}var l=o-1,f=1,d=0;for(this[i+l]=255&e;--l>=0&&(f*=256);)e<0&&0===d&&0!==this[i+l+1]&&(d=1),this[i+l]=(e/f>>0)-d&255;return i+o},Buffer.prototype.writeInt8=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,1,127,-128),e<0&&(e=255+e+1),this[i]=255&e,i+1},Buffer.prototype.writeInt16LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,32767,-32768),this[i]=255&e,this[i+1]=e>>>8,i+2},Buffer.prototype.writeInt16BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,2,32767,-32768),this[i]=e>>>8,this[i+1]=255&e,i+2},Buffer.prototype.writeInt32LE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,**********,-2147483648),this[i]=255&e,this[i+1]=e>>>8,this[i+2]=e>>>16,this[i+3]=e>>>24,i+4},Buffer.prototype.writeInt32BE=function(e,i,o){return e=+e,i>>>=0,o||checkInt(this,e,i,4,**********,-2147483648),e<0&&(e=4294967295+e+1),this[i]=e>>>24,this[i+1]=e>>>16,this[i+2]=e>>>8,this[i+3]=255&e,i+4},Buffer.prototype.writeFloatLE=function(e,i,o){return writeFloat(this,e,i,!0,o)},Buffer.prototype.writeFloatBE=function(e,i,o){return writeFloat(this,e,i,!1,o)},Buffer.prototype.writeDoubleLE=function(e,i,o){return writeDouble(this,e,i,!0,o)},Buffer.prototype.writeDoubleBE=function(e,i,o){return writeDouble(this,e,i,!1,o)},Buffer.prototype.copy=function(e,i,o,s){if(!Buffer.isBuffer(e))throw TypeError("argument should be a Buffer");if(o||(o=0),s||0===s||(s=this.length),i>=e.length&&(i=e.length),i||(i=0),s>0&&s<o&&(s=o),s===o||0===e.length||0===this.length)return 0;if(i<0)throw RangeError("targetStart out of bounds");if(o<0||o>=this.length)throw RangeError("Index out of range");if(s<0)throw RangeError("sourceEnd out of bounds");s>this.length&&(s=this.length),e.length-i<s-o&&(s=e.length-i+o);var h=s-o;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(i,o,s);else if(this===e&&o<i&&i<s)for(var l=h-1;l>=0;--l)e[l+i]=this[l+o];else Uint8Array.prototype.set.call(e,this.subarray(o,s),i);return h},Buffer.prototype.fill=function(e,i,o,s){if("string"==typeof e){if("string"==typeof i?(s=i,i=0,o=this.length):"string"==typeof o&&(s=o,o=this.length),void 0!==s&&"string"!=typeof s)throw TypeError("encoding must be a string");if("string"==typeof s&&!Buffer.isEncoding(s))throw TypeError("Unknown encoding: "+s);if(1===e.length){var h,l=e.charCodeAt(0);("utf8"===s&&l<128||"latin1"===s)&&(e=l)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(i<0||this.length<i||this.length<o)throw RangeError("Out of range index");if(o<=i)return this;if(i>>>=0,o=void 0===o?this.length:o>>>0,e||(e=0),"number"==typeof e)for(h=i;h<o;++h)this[h]=e;else{var f=Buffer.isBuffer(e)?e:Buffer.from(e,s),d=f.length;if(0===d)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(h=0;h<o-i;++h)this[h+i]=f[h%d]}return this};var f=/[^+/0-9A-Za-z-_]/g;function utf8ToBytes(e,i){i=i||1/0;for(var o,s=e.length,h=null,l=[],f=0;f<s;++f){if((o=e.charCodeAt(f))>55295&&o<57344){if(!h){if(o>56319||f+1===s){(i-=3)>-1&&l.push(239,191,189);continue}h=o;continue}if(o<56320){(i-=3)>-1&&l.push(239,191,189),h=o;continue}o=(h-55296<<10|o-56320)+65536}else h&&(i-=3)>-1&&l.push(239,191,189);if(h=null,o<128){if((i-=1)<0)break;l.push(o)}else if(o<2048){if((i-=2)<0)break;l.push(o>>6|192,63&o|128)}else if(o<65536){if((i-=3)<0)break;l.push(o>>12|224,o>>6&63|128,63&o|128)}else if(o<1114112){if((i-=4)<0)break;l.push(o>>18|240,o>>12&63|128,o>>6&63|128,63&o|128)}else throw Error("Invalid code point")}return l}function asciiToBytes(e){for(var i=[],o=0;o<e.length;++o)i.push(255&e.charCodeAt(o));return i}function base64ToBytes(e){return s.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(f,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function blitBuffer(e,i,o,s){for(var h=0;h<s&&!(h+o>=i.length)&&!(h>=e.length);++h)i[h+o]=e[h];return h}function isInstance(e,i){return e instanceof i||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===i.name}var d=function(){for(var e="0123456789abcdef",i=Array(256),o=0;o<16;++o)for(var s=16*o,h=0;h<16;++h)i[s+h]=e[o]+e[h];return i}()},783:function(e,i){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */i.read=function(e,i,o,s,h){var l,f,d=8*h-s-1,g=(1<<d)-1,b=g>>1,w=-7,_=o?h-1:0,k=o?-1:1,O=e[i+_];for(_+=k,l=O&(1<<-w)-1,O>>=-w,w+=d;w>0;l=256*l+e[i+_],_+=k,w-=8);for(f=l&(1<<-w)-1,l>>=-w,w+=s;w>0;f=256*f+e[i+_],_+=k,w-=8);if(0===l)l=1-b;else{if(l===g)return f?NaN:(O?-1:1)*(1/0);f+=Math.pow(2,s),l-=b}return(O?-1:1)*f*Math.pow(2,l-s)},i.write=function(e,i,o,s,h,l){var f,d,g,b=8*l-h-1,w=(1<<b)-1,_=w>>1,k=23===h?5960464477539062e-23:0,O=s?0:l-1,j=s?1:-1,$=i<0||0===i&&1/i<0?1:0;for(isNaN(i=Math.abs(i))||i===1/0?(d=isNaN(i)?1:0,f=w):(f=Math.floor(Math.log(i)/Math.LN2),i*(g=Math.pow(2,-f))<1&&(f--,g*=2),f+_>=1?i+=k/g:i+=k*Math.pow(2,1-_),i*g>=2&&(f++,g/=2),f+_>=w?(d=0,f=w):f+_>=1?(d=(i*g-1)*Math.pow(2,h),f+=_):(d=i*Math.pow(2,_-1)*Math.pow(2,h),f=0));h>=8;e[o+O]=255&d,O+=j,d/=256,h-=8);for(f=f<<h|d,b+=h;b>0;e[o+O]=255&f,O+=j,f/=256,b-=8);e[o+O-j]|=128*$}}},o={};function __nccwpck_require__(e){var s=o[e];if(void 0!==s)return s.exports;var h=o[e]={exports:{}},l=!0;try{i[e](h,h.exports,__nccwpck_require__),l=!1}finally{l&&delete o[e]}return h.exports}__nccwpck_require__.ab="//";var s=__nccwpck_require__(72);e.exports=s}()},8960:function(e){!function(){var i={229:function(e){var i,o,s,h=e.exports={};function defaultSetTimout(){throw Error("setTimeout has not been defined")}function defaultClearTimeout(){throw Error("clearTimeout has not been defined")}function runTimeout(e){if(i===setTimeout)return setTimeout(e,0);if((i===defaultSetTimout||!i)&&setTimeout)return i=setTimeout,setTimeout(e,0);try{return i(e,0)}catch(o){try{return i.call(null,e,0)}catch(o){return i.call(this,e,0)}}}!function(){try{i="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(e){i=defaultSetTimout}try{o="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(e){o=defaultClearTimeout}}();var l=[],f=!1,d=-1;function cleanUpNextTick(){f&&s&&(f=!1,s.length?l=s.concat(l):d=-1,l.length&&drainQueue())}function drainQueue(){if(!f){var e=runTimeout(cleanUpNextTick);f=!0;for(var i=l.length;i;){for(s=l,l=[];++d<i;)s&&s[d].run();d=-1,i=l.length}s=null,f=!1,function(e){if(o===clearTimeout)return clearTimeout(e);if((o===defaultClearTimeout||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{o(e)}catch(i){try{return o.call(null,e)}catch(i){return o.call(this,e)}}}(e)}}function Item(e,i){this.fun=e,this.array=i}function noop(){}h.nextTick=function(e){var i=Array(arguments.length-1);if(arguments.length>1)for(var o=1;o<arguments.length;o++)i[o-1]=arguments[o];l.push(new Item(e,i)),1!==l.length||f||runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},h.title="browser",h.browser=!0,h.env={},h.argv=[],h.version="",h.versions={},h.on=noop,h.addListener=noop,h.once=noop,h.off=noop,h.removeListener=noop,h.removeAllListeners=noop,h.emit=noop,h.prependListener=noop,h.prependOnceListener=noop,h.listeners=function(e){return[]},h.binding=function(e){throw Error("process.binding is not supported")},h.cwd=function(){return"/"},h.chdir=function(e){throw Error("process.chdir is not supported")},h.umask=function(){return 0}}},o={};function __nccwpck_require__(e){var s=o[e];if(void 0!==s)return s.exports;var h=o[e]={exports:{}},l=!0;try{i[e](h,h.exports,__nccwpck_require__),l=!1}finally{l&&delete o[e]}return h.exports}__nccwpck_require__.ab="//";var s=__nccwpck_require__(229);e.exports=s}()},622:function(e,i,o){"use strict";/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=o(2265),h=Symbol.for("react.element"),l=Symbol.for("react.fragment"),f=Object.prototype.hasOwnProperty,d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,g={key:!0,ref:!0,__self:!0,__source:!0};function q(e,i,o){var s,l={},b=null,w=null;for(s in void 0!==o&&(b=""+o),void 0!==i.key&&(b=""+i.key),void 0!==i.ref&&(w=i.ref),i)f.call(i,s)&&!g.hasOwnProperty(s)&&(l[s]=i[s]);if(e&&e.defaultProps)for(s in i=e.defaultProps)void 0===l[s]&&(l[s]=i[s]);return{$$typeof:h,type:e,key:b,ref:w,props:l,_owner:d.current}}i.Fragment=l,i.jsx=q,i.jsxs=q},7437:function(e,i,o){"use strict";e.exports=o(622)},3304:function(e,i,o){"use strict";let s,h;o.d(i,{Jn:function(){return tf},qX:function(){return _getProvider},rh:function(){return _isFirebaseServerApp},Xd:function(){return _registerComponent},Mq:function(){return getApp},ZF:function(){return initializeApp},KN:function(){return registerVersion}});var l,f=o(3576),d=o(8650),g=o(4534);let instanceOfAny=(e,i)=>i.some(i=>e instanceof i),b=new WeakMap,w=new WeakMap,_=new WeakMap,k=new WeakMap,O=new WeakMap,j={get(e,i,o){if(e instanceof IDBTransaction){if("done"===i)return w.get(e);if("objectStoreNames"===i)return e.objectStoreNames||_.get(e);if("store"===i)return o.objectStoreNames[1]?void 0:o.objectStore(o.objectStoreNames[0])}return wrap_idb_value_wrap(e[i])},set:(e,i,o)=>(e[i]=o,!0),has:(e,i)=>e instanceof IDBTransaction&&("done"===i||"store"===i)||i in e};function wrap_idb_value_wrap(e){var i;if(e instanceof IDBRequest)return function(e){let i=new Promise((i,o)=>{let unlisten=()=>{e.removeEventListener("success",success),e.removeEventListener("error",error)},success=()=>{i(wrap_idb_value_wrap(e.result)),unlisten()},error=()=>{o(e.error),unlisten()};e.addEventListener("success",success),e.addEventListener("error",error)});return i.then(i=>{i instanceof IDBCursor&&b.set(i,e)}).catch(()=>{}),O.set(i,e),i}(e);if(k.has(e))return k.get(e);let o="function"==typeof(i=e)?i!==IDBDatabase.prototype.transaction||"objectStoreNames"in IDBTransaction.prototype?(h||(h=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(i)?function(...e){return i.apply(unwrap(this),e),wrap_idb_value_wrap(b.get(this))}:function(...e){return wrap_idb_value_wrap(i.apply(unwrap(this),e))}:function(e,...o){let s=i.call(unwrap(this),e,...o);return _.set(s,e.sort?e.sort():[e]),wrap_idb_value_wrap(s)}:(i instanceof IDBTransaction&&function(e){if(w.has(e))return;let i=new Promise((i,o)=>{let unlisten=()=>{e.removeEventListener("complete",complete),e.removeEventListener("error",error),e.removeEventListener("abort",error)},complete=()=>{i(),unlisten()},error=()=>{o(e.error||new DOMException("AbortError","AbortError")),unlisten()};e.addEventListener("complete",complete),e.addEventListener("error",error),e.addEventListener("abort",error)});w.set(e,i)}(i),instanceOfAny(i,s||(s=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction])))?new Proxy(i,j):i;return o!==e&&(k.set(e,o),O.set(o,e)),o}let unwrap=e=>O.get(e),$=["get","getKey","getAll","getAllKeys","count"],tt=["put","add","delete","clear"],te=new Map;function getMethod(e,i){if(!(e instanceof IDBDatabase&&!(i in e)&&"string"==typeof i))return;if(te.get(i))return te.get(i);let o=i.replace(/FromIndex$/,""),s=i!==o,h=tt.includes(o);if(!(o in(s?IDBIndex:IDBObjectStore).prototype)||!(h||$.includes(o)))return;let method=async function(e,...i){let l=this.transaction(e,h?"readwrite":"readonly"),f=l.store;return s&&(f=f.index(i.shift())),(await Promise.all([f[o](...i),h&&l.done]))[0]};return te.set(i,method),method}j={...l=j,get:(e,i,o)=>getMethod(e,i)||l.get(e,i,o),has:(e,i)=>!!getMethod(e,i)||l.has(e,i)};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let PlatformLoggerServiceImpl=class PlatformLoggerServiceImpl{constructor(e){this.container=e}getPlatformInfoString(){let e=this.container.getProviders();return e.map(e=>{if(!function(e){let i=e.getComponent();return i?.type==="VERSION"}(e))return null;{let i=e.getImmediate();return`${i.library}/${i.version}`}}).filter(e=>e).join(" ")}};let tr="@firebase/app",tn="0.14.0",ti=new d.Yd("@firebase/app"),to="[DEFAULT]",ts={[tr]:"fire-core","@firebase/app-compat":"fire-core-compat","@firebase/analytics":"fire-analytics","@firebase/analytics-compat":"fire-analytics-compat","@firebase/app-check":"fire-app-check","@firebase/app-check-compat":"fire-app-check-compat","@firebase/auth":"fire-auth","@firebase/auth-compat":"fire-auth-compat","@firebase/database":"fire-rtdb","@firebase/data-connect":"fire-data-connect","@firebase/database-compat":"fire-rtdb-compat","@firebase/functions":"fire-fn","@firebase/functions-compat":"fire-fn-compat","@firebase/installations":"fire-iid","@firebase/installations-compat":"fire-iid-compat","@firebase/messaging":"fire-fcm","@firebase/messaging-compat":"fire-fcm-compat","@firebase/performance":"fire-perf","@firebase/performance-compat":"fire-perf-compat","@firebase/remote-config":"fire-rc","@firebase/remote-config-compat":"fire-rc-compat","@firebase/storage":"fire-gcs","@firebase/storage-compat":"fire-gcs-compat","@firebase/firestore":"fire-fst","@firebase/firestore-compat":"fire-fst-compat","@firebase/ai":"fire-vertex","fire-js":"fire-js",firebase:"fire-js-all"},ta=new Map,th=new Map,tu=new Map;function _addComponent(e,i){try{e.container.addComponent(i)}catch(o){ti.debug(`Component ${i.name} failed to register with FirebaseApp ${e.name}`,o)}}function _registerComponent(e){let i=e.name;if(tu.has(i))return ti.debug(`There were multiple attempts to register component ${i}.`),!1;for(let o of(tu.set(i,e),ta.values()))_addComponent(o,e);for(let i of th.values())_addComponent(i,e);return!0}function _getProvider(e,i){let o=e.container.getProvider("heartbeat").getImmediate({optional:!0});return o&&o.triggerHeartbeat(),e.container.getProvider(i)}function _isFirebaseServerApp(e){return null!=e&&void 0!==e.settings}let tl=new g.LL("app","Firebase",{"no-app":"No Firebase App '{$appName}' has been created - call initializeApp() first","bad-app-name":"Illegal App name: '{$appName}'","duplicate-app":"Firebase App named '{$appName}' already exists with different options or config","app-deleted":"Firebase App named '{$appName}' already deleted","server-app-deleted":"Firebase Server App has been deleted","no-options":"Need to provide options, when not being deployed to hosting via source.","invalid-app-argument":"firebase.{$appName}() takes either no argument or a Firebase App instance.","invalid-log-argument":"First argument to `onLog` must be null or a function.","idb-open":"Error thrown when opening IndexedDB. Original error: {$originalErrorMessage}.","idb-get":"Error thrown when reading from IndexedDB. Original error: {$originalErrorMessage}.","idb-set":"Error thrown when writing to IndexedDB. Original error: {$originalErrorMessage}.","idb-delete":"Error thrown when deleting from IndexedDB. Original error: {$originalErrorMessage}.","finalization-registry-not-supported":"FirebaseServerApp deleteOnDeref field defined but the JS runtime does not support FinalizationRegistry.","invalid-server-app-environment":"FirebaseServerApp is not for use in browser environments."});/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let FirebaseAppImpl=class FirebaseAppImpl{constructor(e,i,o){this._isDeleted=!1,this._options={...e},this._config={...i},this._name=i.name,this._automaticDataCollectionEnabled=i.automaticDataCollectionEnabled,this._container=o,this.container.addComponent(new f.wA("app",()=>this,"PUBLIC"))}get automaticDataCollectionEnabled(){return this.checkDestroyed(),this._automaticDataCollectionEnabled}set automaticDataCollectionEnabled(e){this.checkDestroyed(),this._automaticDataCollectionEnabled=e}get name(){return this.checkDestroyed(),this._name}get options(){return this.checkDestroyed(),this._options}get config(){return this.checkDestroyed(),this._config}get container(){return this._container}get isDeleted(){return this._isDeleted}set isDeleted(e){this._isDeleted=e}checkDestroyed(){if(this.isDeleted)throw tl.create("app-deleted",{appName:this._name})}};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let tf="12.0.0";function initializeApp(e,i={}){let o=e;if("object"!=typeof i){let e=i;i={name:e}}let s={name:to,automaticDataCollectionEnabled:!0,...i},h=s.name;if("string"!=typeof h||!h)throw tl.create("bad-app-name",{appName:String(h)});if(o||(o=(0,g.aH)()),!o)throw tl.create("no-options");let l=ta.get(h);if(l){if((0,g.vZ)(o,l.options)&&(0,g.vZ)(s,l.config))return l;throw tl.create("duplicate-app",{appName:h})}let d=new f.H0(h);for(let e of tu.values())d.addComponent(e);let b=new FirebaseAppImpl(o,s,d);return ta.set(h,b),b}function getApp(e=to){let i=ta.get(e);if(!i&&e===to&&(0,g.aH)())return initializeApp();if(!i)throw tl.create("no-app",{appName:e});return i}function registerVersion(e,i,o){let s=ts[e]??e;o&&(s+=`-${o}`);let h=s.match(/\s|\//),l=i.match(/\s|\//);if(h||l){let e=[`Unable to register library "${s}" with version "${i}":`];h&&e.push(`library name "${s}" contains illegal characters (whitespace or "/")`),h&&l&&e.push("and"),l&&e.push(`version name "${i}" contains illegal characters (whitespace or "/")`),ti.warn(e.join(" "));return}_registerComponent(new f.wA(`${s}-version`,()=>({library:s,version:i}),"VERSION"))}let tp="firebase-heartbeat-store",td=null;function getDbPromise(){return td||(td=(function(e,i,{blocked:o,upgrade:s,blocking:h,terminated:l}={}){let f=indexedDB.open(e,1),d=wrap_idb_value_wrap(f);return s&&f.addEventListener("upgradeneeded",e=>{s(wrap_idb_value_wrap(f.result),e.oldVersion,e.newVersion,wrap_idb_value_wrap(f.transaction),e)}),o&&f.addEventListener("blocked",e=>o(e.oldVersion,e.newVersion,e)),d.then(e=>{l&&e.addEventListener("close",()=>l()),h&&e.addEventListener("versionchange",e=>h(e.oldVersion,e.newVersion,e))}).catch(()=>{}),d})("firebase-heartbeat-database",0,{upgrade:(e,i)=>{if(0===i)try{e.createObjectStore(tp)}catch(e){console.warn(e)}}}).catch(e=>{throw tl.create("idb-open",{originalErrorMessage:e.message})})),td}async function readHeartbeatsFromIndexedDB(e){try{let i=await getDbPromise(),o=i.transaction(tp),s=await o.objectStore(tp).get(computeKey(e));return await o.done,s}catch(e){if(e instanceof g.ZR)ti.warn(e.message);else{let i=tl.create("idb-get",{originalErrorMessage:e?.message});ti.warn(i.message)}}}async function writeHeartbeatsToIndexedDB(e,i){try{let o=await getDbPromise(),s=o.transaction(tp,"readwrite"),h=s.objectStore(tp);await h.put(i,computeKey(e)),await s.done}catch(e){if(e instanceof g.ZR)ti.warn(e.message);else{let i=tl.create("idb-set",{originalErrorMessage:e?.message});ti.warn(i.message)}}}function computeKey(e){return`${e.name}!${e.options.appId}`}let HeartbeatServiceImpl=class HeartbeatServiceImpl{constructor(e){this.container=e,this._heartbeatsCache=null;let i=this.container.getProvider("app").getImmediate();this._storage=new HeartbeatStorageImpl(i),this._heartbeatsCachePromise=this._storage.read().then(e=>(this._heartbeatsCache=e,e))}async triggerHeartbeat(){try{let e=this.container.getProvider("platform-logger").getImmediate(),i=e.getPlatformInfoString(),o=getUTCDateString();if(this._heartbeatsCache?.heartbeats==null&&(this._heartbeatsCache=await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null)||this._heartbeatsCache.lastSentHeartbeatDate===o||this._heartbeatsCache.heartbeats.some(e=>e.date===o))return;if(this._heartbeatsCache.heartbeats.push({date:o,agent:i}),this._heartbeatsCache.heartbeats.length>30){let e=function(e){if(0===e.length)return -1;let i=0,o=e[0].date;for(let s=1;s<e.length;s++)e[s].date<o&&(o=e[s].date,i=s);return i}(this._heartbeatsCache.heartbeats);this._heartbeatsCache.heartbeats.splice(e,1)}return this._storage.overwrite(this._heartbeatsCache)}catch(e){ti.warn(e)}}async getHeartbeatsHeader(){try{if(null===this._heartbeatsCache&&await this._heartbeatsCachePromise,this._heartbeatsCache?.heartbeats==null||0===this._heartbeatsCache.heartbeats.length)return"";let e=getUTCDateString(),{heartbeatsToSend:i,unsentEntries:o}=function(e,i=1024){let o=[],s=e.slice();for(let h of e){let e=o.find(e=>e.agent===h.agent);if(e){if(e.dates.push(h.date),countBytes(o)>i){e.dates.pop();break}}else if(o.push({agent:h.agent,dates:[h.date]}),countBytes(o)>i){o.pop();break}s=s.slice(1)}return{heartbeatsToSend:o,unsentEntries:s}}(this._heartbeatsCache.heartbeats),s=(0,g.L)(JSON.stringify({version:2,heartbeats:i}));return this._heartbeatsCache.lastSentHeartbeatDate=e,o.length>0?(this._heartbeatsCache.heartbeats=o,await this._storage.overwrite(this._heartbeatsCache)):(this._heartbeatsCache.heartbeats=[],this._storage.overwrite(this._heartbeatsCache)),s}catch(e){return ti.warn(e),""}}};function getUTCDateString(){let e=new Date;return e.toISOString().substring(0,10)}let HeartbeatStorageImpl=class HeartbeatStorageImpl{constructor(e){this.app=e,this._canUseIndexedDBPromise=this.runIndexedDBEnvironmentCheck()}async runIndexedDBEnvironmentCheck(){return!!(0,g.hl)()&&(0,g.eu)().then(()=>!0).catch(()=>!1)}async read(){let e=await this._canUseIndexedDBPromise;if(!e)return{heartbeats:[]};{let e=await readHeartbeatsFromIndexedDB(this.app);return e?.heartbeats?e:{heartbeats:[]}}}async overwrite(e){let i=await this._canUseIndexedDBPromise;if(i){let i=await this.read();return writeHeartbeatsToIndexedDB(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??i.lastSentHeartbeatDate,heartbeats:e.heartbeats})}}async add(e){let i=await this._canUseIndexedDBPromise;if(i){let i=await this.read();return writeHeartbeatsToIndexedDB(this.app,{lastSentHeartbeatDate:e.lastSentHeartbeatDate??i.lastSentHeartbeatDate,heartbeats:[...i.heartbeats,...e.heartbeats]})}}};function countBytes(e){return(0,g.L)(JSON.stringify({version:2,heartbeats:e})).length}_registerComponent(new f.wA("platform-logger",e=>new PlatformLoggerServiceImpl(e),"PRIVATE")),_registerComponent(new f.wA("heartbeat",e=>new HeartbeatServiceImpl(e),"PRIVATE")),registerVersion(tr,tn,""),registerVersion(tr,tn,"esm2020"),registerVersion("fire-js","")},3576:function(e,i,o){"use strict";o.d(i,{H0:function(){return ComponentContainer},wA:function(){return Component}});var s=o(4534);let Component=class Component{constructor(e,i,o){this.name=e,this.instanceFactory=i,this.type=o,this.multipleInstances=!1,this.serviceProps={},this.instantiationMode="LAZY",this.onInstanceCreated=null}setInstantiationMode(e){return this.instantiationMode=e,this}setMultipleInstances(e){return this.multipleInstances=e,this}setServiceProps(e){return this.serviceProps=e,this}setInstanceCreatedCallback(e){return this.onInstanceCreated=e,this}};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let h="[DEFAULT]";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Provider=class Provider{constructor(e,i){this.name=e,this.container=i,this.component=null,this.instances=new Map,this.instancesDeferred=new Map,this.instancesOptions=new Map,this.onInitCallbacks=new Map}get(e){let i=this.normalizeInstanceIdentifier(e);if(!this.instancesDeferred.has(i)){let e=new s.BH;if(this.instancesDeferred.set(i,e),this.isInitialized(i)||this.shouldAutoInitialize())try{let o=this.getOrInitializeService({instanceIdentifier:i});o&&e.resolve(o)}catch(e){}}return this.instancesDeferred.get(i).promise}getImmediate(e){let i=this.normalizeInstanceIdentifier(e?.identifier),o=e?.optional??!1;if(this.isInitialized(i)||this.shouldAutoInitialize())try{return this.getOrInitializeService({instanceIdentifier:i})}catch(e){if(o)return null;throw e}else{if(o)return null;throw Error(`Service ${this.name} is not available`)}}getComponent(){return this.component}setComponent(e){if(e.name!==this.name)throw Error(`Mismatching Component ${e.name} for Provider ${this.name}.`);if(this.component)throw Error(`Component for ${this.name} has already been provided`);if(this.component=e,this.shouldAutoInitialize()){if("EAGER"===e.instantiationMode)try{this.getOrInitializeService({instanceIdentifier:h})}catch(e){}for(let[e,i]of this.instancesDeferred.entries()){let o=this.normalizeInstanceIdentifier(e);try{let e=this.getOrInitializeService({instanceIdentifier:o});i.resolve(e)}catch(e){}}}}clearInstance(e=h){this.instancesDeferred.delete(e),this.instancesOptions.delete(e),this.instances.delete(e)}async delete(){let e=Array.from(this.instances.values());await Promise.all([...e.filter(e=>"INTERNAL"in e).map(e=>e.INTERNAL.delete()),...e.filter(e=>"_delete"in e).map(e=>e._delete())])}isComponentSet(){return null!=this.component}isInitialized(e=h){return this.instances.has(e)}getOptions(e=h){return this.instancesOptions.get(e)||{}}initialize(e={}){let{options:i={}}=e,o=this.normalizeInstanceIdentifier(e.instanceIdentifier);if(this.isInitialized(o))throw Error(`${this.name}(${o}) has already been initialized`);if(!this.isComponentSet())throw Error(`Component ${this.name} has not been registered yet`);let s=this.getOrInitializeService({instanceIdentifier:o,options:i});for(let[e,i]of this.instancesDeferred.entries()){let h=this.normalizeInstanceIdentifier(e);o===h&&i.resolve(s)}return s}onInit(e,i){let o=this.normalizeInstanceIdentifier(i),s=this.onInitCallbacks.get(o)??new Set;s.add(e),this.onInitCallbacks.set(o,s);let h=this.instances.get(o);return h&&e(h,o),()=>{s.delete(e)}}invokeOnInitCallbacks(e,i){let o=this.onInitCallbacks.get(i);if(o)for(let s of o)try{s(e,i)}catch{}}getOrInitializeService({instanceIdentifier:e,options:i={}}){let o=this.instances.get(e);if(!o&&this.component&&(o=this.component.instanceFactory(this.container,{instanceIdentifier:e===h?void 0:e,options:i}),this.instances.set(e,o),this.instancesOptions.set(e,i),this.invokeOnInitCallbacks(o,e),this.component.onInstanceCreated))try{this.component.onInstanceCreated(this.container,e,o)}catch{}return o||null}normalizeInstanceIdentifier(e=h){return this.component?this.component.multipleInstances?e:h:e}shouldAutoInitialize(){return!!this.component&&"EXPLICIT"!==this.component.instantiationMode}};/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let ComponentContainer=class ComponentContainer{constructor(e){this.name=e,this.providers=new Map}addComponent(e){let i=this.getProvider(e.name);if(i.isComponentSet())throw Error(`Component ${e.name} has already been registered with ${this.name}`);i.setComponent(e)}addOrOverwriteComponent(e){let i=this.getProvider(e.name);i.isComponentSet()&&this.providers.delete(e.name),this.addComponent(e)}getProvider(e){if(this.providers.has(e))return this.providers.get(e);let i=new Provider(e,this);return this.providers.set(e,i),i}getProviders(){return Array.from(this.providers.values())}}},8650:function(e,i,o){"use strict";var s,h;o.d(i,{Yd:function(){return Logger},in:function(){return s}});/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let l=[];(h=s||(s={}))[h.DEBUG=0]="DEBUG",h[h.VERBOSE=1]="VERBOSE",h[h.INFO=2]="INFO",h[h.WARN=3]="WARN",h[h.ERROR=4]="ERROR",h[h.SILENT=5]="SILENT";let f={debug:s.DEBUG,verbose:s.VERBOSE,info:s.INFO,warn:s.WARN,error:s.ERROR,silent:s.SILENT},d=s.INFO,g={[s.DEBUG]:"log",[s.VERBOSE]:"log",[s.INFO]:"info",[s.WARN]:"warn",[s.ERROR]:"error"},defaultLogHandler=(e,i,...o)=>{if(i<e.logLevel)return;let s=new Date().toISOString(),h=g[i];if(h)console[h](`[${s}]  ${e.name}:`,...o);else throw Error(`Attempted to log a message with an invalid logType (value: ${i})`)};let Logger=class Logger{constructor(e){this.name=e,this._logLevel=d,this._logHandler=defaultLogHandler,this._userLogHandler=null,l.push(this)}get logLevel(){return this._logLevel}set logLevel(e){if(!(e in s))throw TypeError(`Invalid value "${e}" assigned to \`logLevel\``);this._logLevel=e}setLogLevel(e){this._logLevel="string"==typeof e?f[e]:e}get logHandler(){return this._logHandler}set logHandler(e){if("function"!=typeof e)throw TypeError("Value assigned to `logHandler` must be a function");this._logHandler=e}get userLogHandler(){return this._userLogHandler}set userLogHandler(e){this._userLogHandler=e}debug(...e){this._userLogHandler&&this._userLogHandler(this,s.DEBUG,...e),this._logHandler(this,s.DEBUG,...e)}log(...e){this._userLogHandler&&this._userLogHandler(this,s.VERBOSE,...e),this._logHandler(this,s.VERBOSE,...e)}info(...e){this._userLogHandler&&this._userLogHandler(this,s.INFO,...e),this._logHandler(this,s.INFO,...e)}warn(...e){this._userLogHandler&&this._userLogHandler(this,s.WARN,...e),this._logHandler(this,s.WARN,...e)}error(...e){this._userLogHandler&&this._userLogHandler(this,s.ERROR,...e),this._logHandler(this,s.ERROR,...e)}}},3172:function(e,i,o){"use strict";o.d(i,{V8:function(){return h},z8:function(){return s}});var s,h,l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},f={};(function(){function m(){this.blockSize=-1,this.blockSize=64,this.g=[,,,,],this.B=Array(this.blockSize),this.o=this.h=0,this.s()}function n(e,i,o){o||(o=0);var s=Array(16);if("string"==typeof i)for(var h=0;16>h;++h)s[h]=i.charCodeAt(o++)|i.charCodeAt(o++)<<8|i.charCodeAt(o++)<<16|i.charCodeAt(o++)<<24;else for(h=0;16>h;++h)s[h]=i[o++]|i[o++]<<8|i[o++]<<16|i[o++]<<24;i=e.g[0],o=e.g[1],h=e.g[2];var l=e.g[3],f=i+(l^o&(h^l))+s[0]+3614090360&4294967295;f=l+(h^(i=o+(f<<7&4294967295|f>>>25))&(o^h))+s[1]+3905402710&4294967295,f=h+(o^(l=i+(f<<12&4294967295|f>>>20))&(i^o))+s[2]+606105819&4294967295,f=o+(i^(h=l+(f<<17&4294967295|f>>>15))&(l^i))+s[3]+3250441966&4294967295,f=i+(l^(o=h+(f<<22&4294967295|f>>>10))&(h^l))+s[4]+4118548399&4294967295,f=l+(h^(i=o+(f<<7&4294967295|f>>>25))&(o^h))+s[5]+1200080426&4294967295,f=h+(o^(l=i+(f<<12&4294967295|f>>>20))&(i^o))+s[6]+2821735955&4294967295,f=o+(i^(h=l+(f<<17&4294967295|f>>>15))&(l^i))+s[7]+4249261313&4294967295,f=i+(l^(o=h+(f<<22&4294967295|f>>>10))&(h^l))+s[8]+1770035416&4294967295,f=l+(h^(i=o+(f<<7&4294967295|f>>>25))&(o^h))+s[9]+2336552879&4294967295,f=h+(o^(l=i+(f<<12&4294967295|f>>>20))&(i^o))+s[10]+4294925233&4294967295,f=o+(i^(h=l+(f<<17&4294967295|f>>>15))&(l^i))+s[11]+2304563134&4294967295,f=i+(l^(o=h+(f<<22&4294967295|f>>>10))&(h^l))+s[12]+1804603682&4294967295,f=l+(h^(i=o+(f<<7&4294967295|f>>>25))&(o^h))+s[13]+4254626195&4294967295,f=h+(o^(l=i+(f<<12&4294967295|f>>>20))&(i^o))+s[14]+2792965006&4294967295,f=o+(i^(h=l+(f<<17&4294967295|f>>>15))&(l^i))+s[15]+1236535329&4294967295,o=h+(f<<22&4294967295|f>>>10),f=i+(h^l&(o^h))+s[1]+4129170786&4294967295,i=o+(f<<5&4294967295|f>>>27),f=l+(o^h&(i^o))+s[6]+3225465664&4294967295,l=i+(f<<9&4294967295|f>>>23),f=h+(i^o&(l^i))+s[11]+643717713&4294967295,h=l+(f<<14&4294967295|f>>>18),f=o+(l^i&(h^l))+s[0]+3921069994&4294967295,o=h+(f<<20&4294967295|f>>>12),f=i+(h^l&(o^h))+s[5]+3593408605&4294967295,i=o+(f<<5&4294967295|f>>>27),f=l+(o^h&(i^o))+s[10]+38016083&4294967295,l=i+(f<<9&4294967295|f>>>23),f=h+(i^o&(l^i))+s[15]+3634488961&4294967295,h=l+(f<<14&4294967295|f>>>18),f=o+(l^i&(h^l))+s[4]+3889429448&4294967295,o=h+(f<<20&4294967295|f>>>12),f=i+(h^l&(o^h))+s[9]+568446438&4294967295,i=o+(f<<5&4294967295|f>>>27),f=l+(o^h&(i^o))+s[14]+3275163606&4294967295,l=i+(f<<9&4294967295|f>>>23),f=h+(i^o&(l^i))+s[3]+4107603335&4294967295,h=l+(f<<14&4294967295|f>>>18),f=o+(l^i&(h^l))+s[8]+1163531501&4294967295,o=h+(f<<20&4294967295|f>>>12),f=i+(h^l&(o^h))+s[13]+2850285829&4294967295,i=o+(f<<5&4294967295|f>>>27),f=l+(o^h&(i^o))+s[2]+4243563512&4294967295,l=i+(f<<9&4294967295|f>>>23),f=h+(i^o&(l^i))+s[7]+1735328473&4294967295,h=l+(f<<14&4294967295|f>>>18),f=o+(l^i&(h^l))+s[12]+2368359562&4294967295,f=i+((o=h+(f<<20&4294967295|f>>>12))^h^l)+s[5]+4294588738&4294967295,f=l+((i=o+(f<<4&4294967295|f>>>28))^o^h)+s[8]+2272392833&4294967295,f=h+((l=i+(f<<11&4294967295|f>>>21))^i^o)+s[11]+1839030562&4294967295,f=o+((h=l+(f<<16&4294967295|f>>>16))^l^i)+s[14]+4259657740&4294967295,f=i+((o=h+(f<<23&4294967295|f>>>9))^h^l)+s[1]+2763975236&4294967295,f=l+((i=o+(f<<4&4294967295|f>>>28))^o^h)+s[4]+1272893353&4294967295,f=h+((l=i+(f<<11&4294967295|f>>>21))^i^o)+s[7]+4139469664&4294967295,f=o+((h=l+(f<<16&4294967295|f>>>16))^l^i)+s[10]+3200236656&4294967295,f=i+((o=h+(f<<23&4294967295|f>>>9))^h^l)+s[13]+681279174&4294967295,f=l+((i=o+(f<<4&4294967295|f>>>28))^o^h)+s[0]+3936430074&4294967295,f=h+((l=i+(f<<11&4294967295|f>>>21))^i^o)+s[3]+3572445317&4294967295,f=o+((h=l+(f<<16&4294967295|f>>>16))^l^i)+s[6]+76029189&4294967295,f=i+((o=h+(f<<23&4294967295|f>>>9))^h^l)+s[9]+3654602809&4294967295,f=l+((i=o+(f<<4&4294967295|f>>>28))^o^h)+s[12]+3873151461&4294967295,f=h+((l=i+(f<<11&4294967295|f>>>21))^i^o)+s[15]+530742520&4294967295,f=o+((h=l+(f<<16&4294967295|f>>>16))^l^i)+s[2]+3299628645&4294967295,o=h+(f<<23&4294967295|f>>>9),f=i+(h^(o|~l))+s[0]+4096336452&4294967295,i=o+(f<<6&4294967295|f>>>26),f=l+(o^(i|~h))+s[7]+1126891415&4294967295,l=i+(f<<10&4294967295|f>>>22),f=h+(i^(l|~o))+s[14]+2878612391&4294967295,h=l+(f<<15&4294967295|f>>>17),f=o+(l^(h|~i))+s[5]+4237533241&4294967295,o=h+(f<<21&4294967295|f>>>11),f=i+(h^(o|~l))+s[12]+1700485571&4294967295,i=o+(f<<6&4294967295|f>>>26),f=l+(o^(i|~h))+s[3]+2399980690&4294967295,l=i+(f<<10&4294967295|f>>>22),f=h+(i^(l|~o))+s[10]+4293915773&4294967295,h=l+(f<<15&4294967295|f>>>17),f=o+(l^(h|~i))+s[1]+2240044497&4294967295,o=h+(f<<21&4294967295|f>>>11),f=i+(h^(o|~l))+s[8]+1873313359&4294967295,i=o+(f<<6&4294967295|f>>>26),f=l+(o^(i|~h))+s[15]+4264355552&4294967295,l=i+(f<<10&4294967295|f>>>22),f=h+(i^(l|~o))+s[6]+2734768916&4294967295,h=l+(f<<15&4294967295|f>>>17),f=o+(l^(h|~i))+s[13]+1309151649&4294967295,o=h+(f<<21&4294967295|f>>>11),f=i+(h^(o|~l))+s[4]+4149444226&4294967295,i=o+(f<<6&4294967295|f>>>26),f=l+(o^(i|~h))+s[11]+3174756917&4294967295,l=i+(f<<10&4294967295|f>>>22),f=h+(i^(l|~o))+s[2]+718787259&4294967295,h=l+(f<<15&4294967295|f>>>17),f=o+(l^(h|~i))+s[9]+3951481745&4294967295,e.g[0]=e.g[0]+i&4294967295,e.g[1]=e.g[1]+(h+(f<<21&4294967295|f>>>11))&4294967295,e.g[2]=e.g[2]+h&4294967295,e.g[3]=e.g[3]+l&4294967295}function t(e,i){this.h=i;for(var o=[],s=!0,h=e.length-1;0<=h;h--){var l=0|e[h];s&&l==i||(o[h]=l,s=!1)}this.g=o}!function(e,i){function c(){}c.prototype=i.prototype,e.D=i.prototype,e.prototype=new c,e.prototype.constructor=e,e.C=function(e,o,s){for(var h=Array(arguments.length-2),l=2;l<arguments.length;l++)h[l-2]=arguments[l];return i.prototype[o].apply(e,h)}}(m,function(){this.blockSize=-1}),m.prototype.s=function(){this.g[0]=1732584193,this.g[1]=4023233417,this.g[2]=2562383102,this.g[3]=271733878,this.o=this.h=0},m.prototype.u=function(e,i){void 0===i&&(i=e.length);for(var o=i-this.blockSize,s=this.B,h=this.h,l=0;l<i;){if(0==h)for(;l<=o;)n(this,e,l),l+=this.blockSize;if("string"==typeof e){for(;l<i;)if(s[h++]=e.charCodeAt(l++),h==this.blockSize){n(this,s),h=0;break}}else for(;l<i;)if(s[h++]=e[l++],h==this.blockSize){n(this,s),h=0;break}}this.h=h,this.o+=i},m.prototype.v=function(){var e=Array((56>this.h?this.blockSize:2*this.blockSize)-this.h);e[0]=128;for(var i=1;i<e.length-8;++i)e[i]=0;var o=8*this.o;for(i=e.length-8;i<e.length;++i)e[i]=255&o,o/=256;for(this.u(e),e=Array(16),i=o=0;4>i;++i)for(var s=0;32>s;s+=8)e[o++]=this.g[i]>>>s&255;return e};var e,i={};function u(e){return -128<=e&&128>e?Object.prototype.hasOwnProperty.call(i,e)?i[e]:i[e]=new t([0|e],0>e?-1:0):new t([0|e],0>e?-1:0)}function v(e){if(isNaN(e)||!isFinite(e))return o;if(0>e)return x(v(-e));for(var i=[],s=1,h=0;e>=s;h++)i[h]=e/s|0,s*=4294967296;return new t(i,0)}var o=u(0),l=u(1),d=u(16777216);function C(e){if(0!=e.h)return!1;for(var i=0;i<e.g.length;i++)if(0!=e.g[i])return!1;return!0}function B(e){return -1==e.h}function x(e){for(var i=e.g.length,o=[],s=0;s<i;s++)o[s]=~e.g[s];return new t(o,~e.h).add(l)}function F(e,i){return e.add(x(i))}function G(e,i){for(;(65535&e[i])!=e[i];)e[i+1]+=e[i]>>>16,e[i]&=65535,i++}function H(e,i){this.g=e,this.h=i}function D(e,i){if(C(i))throw Error("division by zero");if(C(e))return new H(o,o);if(B(e))return i=D(x(e),i),new H(x(i.g),x(i.h));if(B(i))return i=D(e,x(i)),new H(x(i.g),i.h);if(30<e.g.length){if(B(e)||B(i))throw Error("slowDivide_ only works with positive integers.");for(var s=l,h=i;0>=h.l(e);)s=I(s),h=I(h);var f=J(s,1),d=J(h,1);for(h=J(h,2),s=J(s,2);!C(h);){var g=d.add(h);0>=g.l(e)&&(f=f.add(s),d=g),h=J(h,1),s=J(s,1)}return i=F(e,f.j(i)),new H(f,i)}for(f=o;0<=e.l(i);){for(h=48>=(h=Math.ceil(Math.log(s=Math.max(1,Math.floor(e.m()/i.m())))/Math.LN2))?1:Math.pow(2,h-48),g=(d=v(s)).j(i);B(g)||0<g.l(e);)s-=h,g=(d=v(s)).j(i);C(d)&&(d=l),f=f.add(d),e=F(e,g)}return new H(f,e)}function I(e){for(var i=e.g.length+1,o=[],s=0;s<i;s++)o[s]=e.i(s)<<1|e.i(s-1)>>>31;return new t(o,e.h)}function J(e,i){var o=i>>5;i%=32;for(var s=e.g.length-o,h=[],l=0;l<s;l++)h[l]=0<i?e.i(l+o)>>>i|e.i(l+o+1)<<32-i:e.i(l+o);return new t(h,e.h)}(e=t.prototype).m=function(){if(B(this))return-x(this).m();for(var e=0,i=1,o=0;o<this.g.length;o++){var s=this.i(o);e+=(0<=s?s:4294967296+s)*i,i*=4294967296}return e},e.toString=function(e){if(2>(e=e||10)||36<e)throw Error("radix out of range: "+e);if(C(this))return"0";if(B(this))return"-"+x(this).toString(e);for(var i=v(Math.pow(e,6)),o=this,s="";;){var h=D(o,i).g,l=((0<(o=F(o,h.j(i))).g.length?o.g[0]:o.h)>>>0).toString(e);if(C(o=h))return l+s;for(;6>l.length;)l="0"+l;s=l+s}},e.i=function(e){return 0>e?0:e<this.g.length?this.g[e]:this.h},e.l=function(e){return B(e=F(this,e))?-1:C(e)?0:1},e.abs=function(){return B(this)?x(this):this},e.add=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0,h=0;h<=i;h++){var l=s+(65535&this.i(h))+(65535&e.i(h)),f=(l>>>16)+(this.i(h)>>>16)+(e.i(h)>>>16);s=f>>>16,l&=65535,f&=65535,o[h]=f<<16|l}return new t(o,-2147483648&o[o.length-1]?-1:0)},e.j=function(e){if(C(this)||C(e))return o;if(B(this))return B(e)?x(this).j(x(e)):x(x(this).j(e));if(B(e))return x(this.j(x(e)));if(0>this.l(d)&&0>e.l(d))return v(this.m()*e.m());for(var i=this.g.length+e.g.length,s=[],h=0;h<2*i;h++)s[h]=0;for(h=0;h<this.g.length;h++)for(var l=0;l<e.g.length;l++){var f=this.i(h)>>>16,g=65535&this.i(h),b=e.i(l)>>>16,w=65535&e.i(l);s[2*h+2*l]+=g*w,G(s,2*h+2*l),s[2*h+2*l+1]+=f*w,G(s,2*h+2*l+1),s[2*h+2*l+1]+=g*b,G(s,2*h+2*l+1),s[2*h+2*l+2]+=f*b,G(s,2*h+2*l+2)}for(h=0;h<i;h++)s[h]=s[2*h+1]<<16|s[2*h];for(h=i;h<2*i;h++)s[h]=0;return new t(s,0)},e.A=function(e){return D(this,e).h},e.and=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0;s<i;s++)o[s]=this.i(s)&e.i(s);return new t(o,this.h&e.h)},e.or=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0;s<i;s++)o[s]=this.i(s)|e.i(s);return new t(o,this.h|e.h)},e.xor=function(e){for(var i=Math.max(this.g.length,e.g.length),o=[],s=0;s<i;s++)o[s]=this.i(s)^e.i(s);return new t(o,this.h^e.h)},m.prototype.digest=m.prototype.v,m.prototype.reset=m.prototype.s,m.prototype.update=m.prototype.u,h=f.Md5=m,t.prototype.add=t.prototype.add,t.prototype.multiply=t.prototype.j,t.prototype.modulo=t.prototype.A,t.prototype.compare=t.prototype.l,t.prototype.toNumber=t.prototype.m,t.prototype.toString=t.prototype.toString,t.prototype.getBits=t.prototype.i,t.fromNumber=v,t.fromString=function y(e,i){if(0==e.length)throw Error("number format error: empty string");if(2>(i=i||10)||36<i)throw Error("radix out of range: "+i);if("-"==e.charAt(0))return x(y(e.substring(1),i));if(0<=e.indexOf("-"))throw Error('number format error: interior "-" character');for(var s=v(Math.pow(i,8)),h=o,l=0;l<e.length;l+=8){var f=Math.min(8,e.length-l),d=parseInt(e.substring(l,l+f),i);8>f?(f=v(Math.pow(i,f)),h=h.j(f).add(v(d))):h=(h=h.j(s)).add(v(d))}return h},s=f.Integer=t}).apply(void 0!==l?l:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},4203:function(e,i,o){"use strict";o.d(i,{FJ:function(){return b},JJ:function(){return s},UE:function(){return w},ii:function(){return h},jK:function(){return f},ju:function(){return g},kN:function(){return d},tw:function(){return l}});var s,h,l,f,d,g,b,w,_="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},k={};(function(){var e,i,o,O="function"==typeof Object.defineProperties?Object.defineProperty:function(e,i,o){return e==Array.prototype||e==Object.prototype||(e[i]=o.value),e},j=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof _&&_];for(var i=0;i<e.length;++i){var o=e[i];if(o&&o.Math==Math)return o}throw Error("Cannot find global object")}(this);!function(e,i){if(i)t:{var o=j;e=e.split(".");for(var s=0;s<e.length-1;s++){var h=e[s];if(!(h in o))break t;o=o[h]}(i=i(s=o[e=e[e.length-1]]))!=s&&null!=i&&O(o,e,{configurable:!0,writable:!0,value:i})}}("Array.prototype.values",function(e){return e||function(){var e,i,o,s;return e=this,e instanceof String&&(e+=""),i=0,o=!1,(s={next:function(){if(!o&&i<e.length)return{value:e[i++],done:!1};return o=!0,{done:!0,value:void 0}}})[Symbol.iterator]=function(){return s},s}});var $=$||{},tt=this||self;function ha(e){var i=typeof e;return"array"==(i="object"!=i?i:e?Array.isArray(e)?"array":i:"null")||"object"==i&&"number"==typeof e.length}function n(e){var i=typeof e;return"object"==i&&null!=e||"function"==i}function ia(e,i,o){return e.call.apply(e.bind,arguments)}function ja(e,i,o){if(!e)throw Error();if(2<arguments.length){var s=Array.prototype.slice.call(arguments,2);return function(){var o=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(o,s),e.apply(i,o)}}return function(){return e.apply(i,arguments)}}function p(e,i,o){return(p=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?ia:ja).apply(null,arguments)}function ka(e,i){var o=Array.prototype.slice.call(arguments,1);return function(){var i=o.slice();return i.push.apply(i,arguments),e.apply(this,i)}}function r(e,i){function c(){}c.prototype=i.prototype,e.aa=i.prototype,e.prototype=new c,e.prototype.constructor=e,e.Qb=function(e,o,s){for(var h=Array(arguments.length-2),l=2;l<arguments.length;l++)h[l-2]=arguments[l];return i.prototype[o].apply(e,h)}}function la(e){let i=e.length;if(0<i){let o=Array(i);for(let s=0;s<i;s++)o[s]=e[s];return o}return[]}function ma(e,i){for(let i=1;i<arguments.length;i++){let o=arguments[i];if(ha(o)){let i=e.length||0,s=o.length||0;e.length=i+s;for(let h=0;h<s;h++)e[i+h]=o[h]}else e.push(o)}}function t(e){return/^[\s\xa0]*$/.test(e)}function u(){var e=tt.navigator;return e&&(e=e.userAgent)?e:""}function oa(e){return oa[" "](e),e}oa[" "]=function(){};var te=-1!=u().indexOf("Gecko")&&!(-1!=u().toLowerCase().indexOf("webkit")&&-1==u().indexOf("Edge"))&&!(-1!=u().indexOf("Trident")||-1!=u().indexOf("MSIE"))&&-1==u().indexOf("Edge");function qa(e,i,o){for(let s in e)i.call(o,e[s],s,e)}function sa(e){let i={};for(let o in e)i[o]=e[o];return i}let tr="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function ua(e,i){let o,s;for(let i=1;i<arguments.length;i++){for(o in s=arguments[i])e[o]=s[o];for(let i=0;i<tr.length;i++)o=tr[i],Object.prototype.hasOwnProperty.call(s,o)&&(e[o]=s[o])}}var tn=new class{constructor(e,i){this.i=e,this.j=i,this.h=0,this.g=null}get(){let e;return 0<this.h?(this.h--,e=this.g,this.g=e.next,e.next=null):e=this.i(),e}}(()=>new Ca,e=>e.reset());let Ca=class Ca{constructor(){this.next=this.g=this.h=null}set(e,i){this.h=e,this.g=i,this.next=null}reset(){this.next=this.g=this.h=null}};let ti,to=!1,ts=new class{constructor(){this.h=this.g=null}add(e,i){let o=tn.get();o.set(e,i),this.h?this.h.next=o:this.g=o,this.h=o}},Ea=()=>{let e=tt.Promise.resolve(void 0);ti=()=>{e.then(Da)}};var Da=()=>{let e;for(var i;e=null,ts.g&&(e=ts.g,ts.g=ts.g.next,ts.g||(ts.h=null),e.next=null),i=e;){try{i.h.call(i.g)}catch(e){!function(e){tt.setTimeout(()=>{throw e},0)}(e)}tn.j(i),100>tn.h&&(tn.h++,i.next=tn.g,tn.g=i)}to=!1};function z(){this.s=this.s,this.C=this.C}function A(e,i){this.type=e,this.g=this.target=i,this.defaultPrevented=!1}z.prototype.s=!1,z.prototype.ma=function(){this.s||(this.s=!0,this.N())},z.prototype.N=function(){if(this.C)for(;this.C.length;)this.C.shift()()},A.prototype.h=function(){this.defaultPrevented=!0};var ta=function(){if(!tt.addEventListener||!Object.defineProperty)return!1;var e=!1,i=Object.defineProperty({},"passive",{get:function(){e=!0}});try{let c=()=>{};tt.addEventListener("test",c,i),tt.removeEventListener("test",c,i)}catch(e){}return e}();function C(e,i){if(A.call(this,e?e.type:""),this.relatedTarget=this.g=this.target=null,this.button=this.screenY=this.screenX=this.clientY=this.clientX=0,this.key="",this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1,this.state=null,this.pointerId=0,this.pointerType="",this.i=null,e){var o=this.type=e.type,s=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:null;if(this.target=e.target||e.srcElement,this.g=i,i=e.relatedTarget){if(te){t:{try{oa(i.nodeName);var h=!0;break t}catch(e){}h=!1}h||(i=null)}}else"mouseover"==o?i=e.fromElement:"mouseout"==o&&(i=e.toElement);this.relatedTarget=i,s?(this.clientX=void 0!==s.clientX?s.clientX:s.pageX,this.clientY=void 0!==s.clientY?s.clientY:s.pageY,this.screenX=s.screenX||0,this.screenY=s.screenY||0):(this.clientX=void 0!==e.clientX?e.clientX:e.pageX,this.clientY=void 0!==e.clientY?e.clientY:e.pageY,this.screenX=e.screenX||0,this.screenY=e.screenY||0),this.button=e.button,this.key=e.key||"",this.ctrlKey=e.ctrlKey,this.altKey=e.altKey,this.shiftKey=e.shiftKey,this.metaKey=e.metaKey,this.pointerId=e.pointerId||0,this.pointerType="string"==typeof e.pointerType?e.pointerType:th[e.pointerType]||"",this.state=e.state,this.i=e,e.defaultPrevented&&C.aa.h.call(this)}}r(C,A);var th={2:"touch",3:"pen",4:"mouse"};C.prototype.h=function(){C.aa.h.call(this);var e=this.i;e.preventDefault?e.preventDefault():e.returnValue=!1};var tu="closure_listenable_"+(1e6*Math.random()|0),tl=0;function Ia(e,i,o,s,h){this.listener=e,this.proxy=null,this.src=i,this.type=o,this.capture=!!s,this.ha=h,this.key=++tl,this.da=this.fa=!1}function Ja(e){e.da=!0,e.listener=null,e.proxy=null,e.src=null,e.ha=null}function Ka(e){this.src=e,this.g={},this.h=0}function Ma(e,i){var o=i.type;if(o in e.g){var s,h=e.g[o],l=Array.prototype.indexOf.call(h,i,void 0);(s=0<=l)&&Array.prototype.splice.call(h,l,1),s&&(Ja(i),0==e.g[o].length&&(delete e.g[o],e.h--))}}function La(e,i,o,s){for(var h=0;h<e.length;++h){var l=e[h];if(!l.da&&l.listener==i&&!!o==l.capture&&l.ha==s)return h}return -1}Ka.prototype.add=function(e,i,o,s,h){var l=e.toString();(e=this.g[l])||(e=this.g[l]=[],this.h++);var f=La(e,i,s,h);return -1<f?(i=e[f],o||(i.fa=!1)):((i=new Ia(i,this.src,l,!!s,h)).fa=o,e.push(i)),i};var tf="closure_lm_"+(1e6*Math.random()|0),tp={};function Ta(e,i,o,s,h,l){if(!i)throw Error("Invalid event type");var f=n(h)?!!h.capture:!!h,d=Ua(e);if(d||(e[tf]=d=new Ka(e)),(o=d.add(i,o,s,f,l)).proxy)return o;if(s=function a(e){return Xa.call(a.src,a.listener,e)},o.proxy=s,s.src=e,s.listener=o,e.addEventListener)ta||(h=f),void 0===h&&(h=!1),e.addEventListener(i.toString(),s,h);else if(e.attachEvent)e.attachEvent(Wa(i.toString()),s);else if(e.addListener&&e.removeListener)e.addListener(s);else throw Error("addEventListener and attachEvent are unavailable.");return o}function Za(e){if("number"!=typeof e&&e&&!e.da){var i=e.src;if(i&&i[tu])Ma(i.i,e);else{var o=e.type,s=e.proxy;i.removeEventListener?i.removeEventListener(o,s,e.capture):i.detachEvent?i.detachEvent(Wa(o),s):i.addListener&&i.removeListener&&i.removeListener(s),(o=Ua(i))?(Ma(o,e),0==o.h&&(o.src=null,i[tf]=null)):Ja(e)}}}function Wa(e){return e in tp?tp[e]:tp[e]="on"+e}function Xa(e,i){if(e.da)e=!0;else{i=new C(i,this);var o=e.listener,s=e.ha||e.src;e.fa&&Za(e),e=o.call(s,i)}return e}function Ua(e){return(e=e[tf])instanceof Ka?e:null}var td="__closure_events_fn_"+(1e9*Math.random()>>>0);function Sa(e){return"function"==typeof e?e:(e[td]||(e[td]=function(i){return e.handleEvent(i)}),e[td])}function E(){z.call(this),this.i=new Ka(this),this.M=this,this.F=null}function F(e,i){var o,s=e.F;if(s)for(o=[];s;s=s.F)o.push(s);if(e=e.M,s=i.type||i,"string"==typeof i)i=new A(i,e);else if(i instanceof A)i.target=i.target||e;else{var h=i;ua(i=new A(s,e),h)}if(h=!0,o)for(var l=o.length-1;0<=l;l--){var f=i.g=o[l];h=ab(f,s,!0,i)&&h}if(h=ab(f=i.g=e,s,!0,i)&&h,h=ab(f,s,!1,i)&&h,o)for(l=0;l<o.length;l++)h=ab(f=i.g=o[l],s,!1,i)&&h}function ab(e,i,o,s){if(!(i=e.i.g[String(i)]))return!0;i=i.concat();for(var h=!0,l=0;l<i.length;++l){var f=i[l];if(f&&!f.da&&f.capture==o){var d=f.listener,g=f.ha||f.src;f.fa&&Ma(e.i,f),h=!1!==d.call(g,s)&&h}}return h&&!s.defaultPrevented}function bb(e,i,o){if("function"==typeof e)o&&(e=p(e,o));else if(e&&"function"==typeof e.handleEvent)e=p(e.handleEvent,e);else throw Error("Invalid listener argument");return **********<Number(i)?-1:tt.setTimeout(e,i||0)}r(E,z),E.prototype[tu]=!0,E.prototype.removeEventListener=function(e,i,o,s){!function Ya(e,i,o,s,h){if(Array.isArray(i))for(var l=0;l<i.length;l++)Ya(e,i[l],o,s,h);else(s=n(s)?!!s.capture:!!s,o=Sa(o),e&&e[tu])?(e=e.i,(i=String(i).toString())in e.g&&-1<(o=La(l=e.g[i],o,s,h))&&(Ja(l[o]),Array.prototype.splice.call(l,o,1),0==l.length&&(delete e.g[i],e.h--))):e&&(e=Ua(e))&&(i=e.g[i.toString()],e=-1,i&&(e=La(i,o,s,h)),(o=-1<e?i[e]:null)&&Za(o))}(this,e,i,o,s)},E.prototype.N=function(){if(E.aa.N.call(this),this.i){var e,i=this.i;for(e in i.g){for(var o=i.g[e],s=0;s<o.length;s++)Ja(o[s]);delete i.g[e],i.h--}}this.F=null},E.prototype.K=function(e,i,o,s){return this.i.add(String(e),i,!1,o,s)},E.prototype.L=function(e,i,o,s){return this.i.add(String(e),i,!0,o,s)};let eb=class eb extends z{constructor(e,i){super(),this.m=e,this.l=i,this.h=null,this.i=!1,this.g=null}j(e){this.h=arguments,this.g?this.i=!0:function cb(e){e.g=bb(()=>{e.g=null,e.i&&(e.i=!1,cb(e))},e.l);let i=e.h;e.h=null,e.m.apply(null,i)}(this)}N(){super.N(),this.g&&(tt.clearTimeout(this.g),this.g=null,this.i=!1,this.h=null)}};function G(e){z.call(this),this.h=e,this.g={}}r(G,z);var tg=[];function gb(e){qa(e.g,function(e,i){this.g.hasOwnProperty(i)&&Za(e)},e),e.g={}}G.prototype.N=function(){G.aa.N.call(this),gb(this)},G.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented")};var tm=tt.JSON.stringify,ty=tt.JSON.parse,tv=class{stringify(e){return tt.JSON.stringify(e,void 0)}parse(e){return tt.JSON.parse(e,void 0)}};function kb(){}function lb(e){return e.h||(e.h=e.i())}function mb(){}kb.prototype.h=null;var tw={OPEN:"a",kb:"b",Ja:"c",wb:"d"};function nb(){A.call(this,"d")}function ob(){A.call(this,"c")}r(nb,A),r(ob,A);var t_={},tE=null;function qb(){return tE=tE||new E}function rb(e){A.call(this,t_.La,e)}function J(e){let i=qb();F(i,new rb(i))}function sb(e,i){A.call(this,t_.STAT_EVENT,e),this.stat=i}function K(e){let i=qb();F(i,new sb(i,e))}function tb(e,i){A.call(this,t_.Ma,e),this.size=i}function ub(e,i){if("function"!=typeof e)throw Error("Fn must not be null and must be a function");return tt.setTimeout(function(){e()},i)}function vb(){this.g=!0}function L(e,i,o,s){e.info(function(){return"XMLHTTP TEXT ("+i+"): "+function(e,i){if(!e.g)return i;if(!i)return null;try{var o=JSON.parse(i);if(o){for(e=0;e<o.length;e++)if(Array.isArray(o[e])){var s=o[e];if(!(2>s.length)){var h=s[1];if(Array.isArray(h)&&!(1>h.length)){var l=h[0];if("noop"!=l&&"stop"!=l&&"close"!=l)for(var f=1;f<h.length;f++)h[f]=""}}}}return tm(o)}catch(e){return i}}(e,o)+(s?" "+s:"")})}t_.La="serverreachability",r(rb,A),t_.STAT_EVENT="statevent",r(sb,A),t_.Ma="timingevent",r(tb,A),vb.prototype.xa=function(){this.g=!1},vb.prototype.info=function(){};var tT={NO_ERROR:0,gb:1,tb:2,sb:3,nb:4,rb:5,ub:6,Ia:7,TIMEOUT:8,xb:9},tC={lb:"complete",Hb:"success",Ja:"error",Ia:"abort",zb:"ready",Ab:"readystatechange",TIMEOUT:"timeout",vb:"incrementaldata",yb:"progress",ob:"downloadprogress",Pb:"uploadprogress"};function Db(){}function M(e,i,o,s){this.j=e,this.i=i,this.l=o,this.R=s||1,this.U=new G(this),this.I=45e3,this.H=null,this.o=!1,this.m=this.A=this.v=this.L=this.F=this.S=this.B=null,this.D=[],this.g=null,this.C=0,this.s=this.u=null,this.X=-1,this.J=!1,this.O=0,this.M=null,this.W=this.K=this.T=this.P=!1,this.h=new Eb}function Eb(){this.i=null,this.g="",this.h=!1}r(Db,kb),Db.prototype.g=function(){return new XMLHttpRequest},Db.prototype.i=function(){return{}},i=new Db;var tS={},tA={};function Hb(e,i,o){e.L=1,e.v=Ib(N(i)),e.m=o,e.P=!0,Jb(e,null)}function Jb(e,i){e.F=Date.now(),Kb(e),e.A=N(e.v);var o=e.A,s=e.R;Array.isArray(s)||(s=[String(s)]),Lb(o.i,"t",s),e.C=0,o=e.j.J,e.h=new Eb,e.g=Mb(e.j,o?i:null,!e.m),0<e.O&&(e.M=new eb(p(e.Y,e,e.g),e.O)),i=e.U,o=e.g,s=e.ca;var h="readystatechange";Array.isArray(h)||(h&&(tg[0]=h.toString()),h=tg);for(var l=0;l<h.length;l++){var f=function Qa(e,i,o,s,h){if(s&&s.once)return function Ra(e,i,o,s,h){if(Array.isArray(i)){for(var l=0;l<i.length;l++)Ra(e,i[l],o,s,h);return null}return o=Sa(o),e&&e[tu]?e.L(i,o,n(s)?!!s.capture:!!s,h):Ta(e,i,o,!0,s,h)}(e,i,o,s,h);if(Array.isArray(i)){for(var l=0;l<i.length;l++)Qa(e,i[l],o,s,h);return null}return o=Sa(o),e&&e[tu]?e.K(i,o,n(s)?!!s.capture:!!s,h):Ta(e,i,o,!1,s,h)}(o,h[l],s||i.handleEvent,!1,i.h||i);if(!f)break;i.g[f.key]=f}i=e.H?sa(e.H):{},e.m?(e.u||(e.u="POST"),i["Content-Type"]="application/x-www-form-urlencoded",e.g.ea(e.A,e.u,e.m,i)):(e.u="GET",e.g.ea(e.A,e.u,null,i)),J(),function(e,i,o,s,h,l){e.info(function(){if(e.g){if(l)for(var f="",d=l.split("&"),g=0;g<d.length;g++){var b=d[g].split("=");if(1<b.length){var w=b[0];b=b[1];var _=w.split("_");f=2<=_.length&&"type"==_[1]?f+(w+"=")+b+"&":f+(w+"=redacted&")}}else f=null}else f=l;return"XMLHTTP REQ ("+s+") [attempt "+h+"]: "+i+"\n"+o+"\n"+f})}(e.i,e.u,e.A,e.l,e.R,e.m)}function Pb(e){return!!e.g&&"GET"==e.u&&2!=e.L&&e.j.Ca}function Kb(e){e.S=Date.now()+e.I,Wb(e,e.I)}function Wb(e,i){if(null!=e.B)throw Error("WatchDog timer not null");e.B=ub(p(e.ba,e),i)}function Ob(e){e.B&&(tt.clearTimeout(e.B),e.B=null)}function Qb(e){0==e.j.G||e.J||Ub(e.j,e)}function Q(e){Ob(e);var i=e.M;i&&"function"==typeof i.ma&&i.ma(),e.M=null,gb(e.U),e.g&&(i=e.g,e.g=null,i.abort(),i.ma())}function Rb(e,i){try{var o=e.j;if(0!=o.G&&(o.g==e||Xb(o.h,e))){if(!e.K&&Xb(o.h,e)&&3==o.G){try{var s=o.Da.g.parse(i)}catch(e){s=null}if(Array.isArray(s)&&3==s.length){var h=s;if(0==h[0]){t:if(!o.u){if(o.g){if(o.g.F+3e3<e.F)Yb(o),Zb(o);else break t}$b(o),K(18)}}else o.za=h[1],0<o.za-o.T&&37500>h[2]&&o.F&&0==o.v&&!o.C&&(o.C=ub(p(o.Za,o),6e3));if(1>=ac(o.h)&&o.ca){try{o.ca()}catch(e){}o.ca=void 0}}else R(o,11)}else if((e.K||o.g==e)&&Yb(o),!t(i))for(h=o.Da.g.parse(i),i=0;i<h.length;i++){let d=h[i];if(o.T=d[0],d=d[1],2==o.G){if("c"==d[0]){o.K=d[1],o.ia=d[2];let i=d[3];null!=i&&(o.la=i,o.j.info("VER="+o.la));let h=d[4];null!=h&&(o.Aa=h,o.j.info("SVER="+o.Aa));let g=d[5];null!=g&&"number"==typeof g&&0<g&&(s=1.5*g,o.L=s,o.j.info("backChannelRequestTimeoutMs_="+s)),s=o;let b=e.g;if(b){let e=b.g?b.g.getResponseHeader("X-Client-Wire-Protocol"):null;if(e){var l=s.h;l.g||-1==e.indexOf("spdy")&&-1==e.indexOf("quic")&&-1==e.indexOf("h2")||(l.j=l.l,l.g=new Set,l.h&&(bc(l,l.h),l.h=null))}if(s.D){let e=b.g?b.g.getResponseHeader("X-HTTP-Session-Id"):null;e&&(s.ya=e,S(s.I,s.D,e))}}if(o.G=3,o.l&&o.l.ua(),o.ba&&(o.R=Date.now()-e.F,o.j.info("Handshake RTT: "+o.R+"ms")),(s=o).qa=cc(s,s.J?s.ia:null,s.W),e.K){dc(s.h,e);var f=s.L;f&&(e.I=f),e.B&&(Ob(e),Kb(e)),s.g=e}else ec(s);0<o.i.length&&fc(o)}else"stop"!=d[0]&&"close"!=d[0]||R(o,7)}else 3==o.G&&("stop"==d[0]||"close"==d[0]?"stop"==d[0]?R(o,7):gc(o):"noop"!=d[0]&&o.l&&o.l.ta(d),o.v=0)}}J(4)}catch(e){}}M.prototype.ca=function(e){e=e.target;let i=this.M;i&&3==P(e)?i.j():this.Y(e)},M.prototype.Y=function(e){try{if(e==this.g)t:{let _=P(this.g);var i=this.g.Ba();let k=this.g.Z();if(!(3>_)&&(3!=_||this.g&&(this.h.h||this.g.oa()||Nb(this.g)))){this.J||4!=_||7==i||(8==i||0>=k?J(3):J(2)),Ob(this);var o=this.g.Z();this.X=o;e:if(Pb(this)){var s=Nb(this.g);e="";var h=s.length,l=4==P(this.g);if(!this.h.i){if("undefined"==typeof TextDecoder){Q(this),Qb(this);var f="";break e}this.h.i=new tt.TextDecoder}for(i=0;i<h;i++)this.h.h=!0,e+=this.h.i.decode(s[i],{stream:!(l&&i==h-1)});s.length=0,this.h.g+=e,this.C=0,f=this.h.g}else f=this.g.oa();if(this.o=200==o,function(e,i,o,s,h,l,f){e.info(function(){return"XMLHTTP RESP ("+s+") [ attempt "+h+"]: "+i+"\n"+o+"\n"+l+" "+f})}(this.i,this.u,this.A,this.l,this.R,_,o),this.o){if(this.T&&!this.K){e:{if(this.g){var d,g=this.g;if((d=g.g?g.g.getResponseHeader("X-HTTP-Initial-Response"):null)&&!t(d)){var b=d;break e}}b=null}if(o=b)L(this.i,this.l,o,"Initial handshake response via X-HTTP-Initial-Response"),this.K=!0,Rb(this,o);else{this.o=!1,this.s=3,K(12),Q(this),Qb(this);break t}}if(this.P){let e;for(o=!0;!this.J&&this.C<f.length;)if((e=function(e,i){var o=e.C,s=i.indexOf("\n",o);return -1==s?tA:isNaN(o=Number(i.substring(o,s)))?tS:(s+=1)+o>i.length?tA:(i=i.slice(s,s+o),e.C=s+o,i)}(this,f))==tA){4==_&&(this.s=4,K(14),o=!1),L(this.i,this.l,null,"[Incomplete Response]");break}else if(e==tS){this.s=4,K(15),L(this.i,this.l,f,"[Invalid Chunk]"),o=!1;break}else L(this.i,this.l,e,null),Rb(this,e);if(Pb(this)&&0!=this.C&&(this.h.g=this.h.g.slice(this.C),this.C=0),4!=_||0!=f.length||this.h.h||(this.s=1,K(16),o=!1),this.o=this.o&&o,o){if(0<f.length&&!this.W){this.W=!0;var w=this.j;w.g==this&&w.ba&&!w.M&&(w.j.info("Great, no buffering proxy detected. Bytes received: "+f.length),Tb(w),w.M=!0,K(11))}}else L(this.i,this.l,f,"[Invalid Chunked Response]"),Q(this),Qb(this)}else L(this.i,this.l,f,null),Rb(this,f);4==_&&Q(this),this.o&&!this.J&&(4==_?Ub(this.j,this):(this.o=!1,Kb(this)))}else(function(e){let i={};e=(e.g&&2<=P(e)&&e.g.getAllResponseHeaders()||"").split("\r\n");for(let s=0;s<e.length;s++){if(t(e[s]))continue;var o=function(e){var i=1;e=e.split(":");let o=[];for(;0<i&&e.length;)o.push(e.shift()),i--;return e.length&&o.push(e.join(":")),o}(e[s]);let h=o[0];if("string"!=typeof(o=o[1]))continue;o=o.trim();let l=i[h]||[];i[h]=l,l.push(o)}!function(e,i){for(let o in e)i.call(void 0,e[o],o,e)}(i,function(e){return e.join(", ")})})(this.g),400==o&&0<f.indexOf("Unknown SID")?(this.s=3,K(12)):(this.s=0,K(13)),Q(this),Qb(this)}}}catch(e){}finally{}},M.prototype.cancel=function(){this.J=!0,Q(this)},M.prototype.ba=function(){this.B=null;let e=Date.now();0<=e-this.S?(function(e,i){e.info(function(){return"TIMEOUT: "+i})}(this.i,this.A),2!=this.L&&(J(),K(17)),Q(this),this.s=2,Qb(this)):Wb(this,this.S-e)};var tI=class{constructor(e,i){this.g=e,this.map=i}};function ic(e){this.l=e||10,e=tt.PerformanceNavigationTiming?0<(e=tt.performance.getEntriesByType("navigation")).length&&("hq"==e[0].nextHopProtocol||"h2"==e[0].nextHopProtocol):!!(tt.chrome&&tt.chrome.loadTimes&&tt.chrome.loadTimes()&&tt.chrome.loadTimes().wasFetchedViaSpdy),this.j=e?this.l:1,this.g=null,1<this.j&&(this.g=new Set),this.h=null,this.i=[]}function jc(e){return!!e.h||!!e.g&&e.g.size>=e.j}function ac(e){return e.h?1:e.g?e.g.size:0}function Xb(e,i){return e.h?e.h==i:!!e.g&&e.g.has(i)}function bc(e,i){e.g?e.g.add(i):e.h=i}function dc(e,i){e.h&&e.h==i?e.h=null:e.g&&e.g.has(i)&&e.g.delete(i)}function kc(e){if(null!=e.h)return e.i.concat(e.h.D);if(null!=e.g&&0!==e.g.size){let i=e.i;for(let o of e.g.values())i=i.concat(o.D);return i}return la(e.i)}function nc(e,i){if(e.forEach&&"function"==typeof e.forEach)e.forEach(i,void 0);else if(ha(e)||"string"==typeof e)Array.prototype.forEach.call(e,i,void 0);else for(var o=function(e){if(e.na&&"function"==typeof e.na)return e.na();if(!e.V||"function"!=typeof e.V){if("undefined"!=typeof Map&&e instanceof Map)return Array.from(e.keys());if(!("undefined"!=typeof Set&&e instanceof Set)){if(ha(e)||"string"==typeof e){var i=[];e=e.length;for(var o=0;o<e;o++)i.push(o);return i}for(let s in i=[],o=0,e)i[o++]=s;return i}}}(e),s=function(e){if(e.V&&"function"==typeof e.V)return e.V();if("undefined"!=typeof Map&&e instanceof Map||"undefined"!=typeof Set&&e instanceof Set)return Array.from(e.values());if("string"==typeof e)return e.split("");if(ha(e)){for(var i=[],o=e.length,s=0;s<o;s++)i.push(e[s]);return i}for(s in i=[],o=0,e)i[o++]=e[s];return i}(e),h=s.length,l=0;l<h;l++)i.call(void 0,s[l],o&&o[l],e)}ic.prototype.cancel=function(){if(this.i=kc(this),this.h)this.h.cancel(),this.h=null;else if(this.g&&0!==this.g.size){for(let e of this.g.values())e.cancel();this.g.clear()}};var tB=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");function T(e){if(this.g=this.o=this.j="",this.s=null,this.m=this.l="",this.h=!1,e instanceof T){this.h=e.h,qc(this,e.j),this.o=e.o,this.g=e.g,rc(this,e.s),this.l=e.l;var i=e.i,o=new sc;o.i=i.i,i.g&&(o.g=new Map(i.g),o.h=i.h),tc(this,o),this.m=e.m}else e&&(i=String(e).match(tB))?(this.h=!1,qc(this,i[1]||"",!0),this.o=uc(i[2]||""),this.g=uc(i[3]||"",!0),rc(this,i[4]),this.l=uc(i[5]||"",!0),tc(this,i[6]||"",!0),this.m=uc(i[7]||"")):(this.h=!1,this.i=new sc(null,this.h))}function N(e){return new T(e)}function qc(e,i,o){e.j=o?uc(i,!0):i,e.j&&(e.j=e.j.replace(/:$/,""))}function rc(e,i){if(i){if(isNaN(i=Number(i))||0>i)throw Error("Bad port number "+i);e.s=i}else e.s=null}function tc(e,i,o){var s,h;i instanceof sc?(e.i=i,s=e.i,(h=e.h)&&!s.j&&(U(s),s.i=null,s.g.forEach(function(e,i){var o=i.toLowerCase();i!=o&&(Dc(this,i),Lb(this,o,e))},s)),s.j=h):(o||(i=vc(i,tD)),e.i=new sc(i,e.h))}function S(e,i,o){e.i.set(i,o)}function Ib(e){return S(e,"zx",Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36)),e}function uc(e,i){return e?i?decodeURI(e.replace(/%25/g,"%2525")):decodeURIComponent(e):""}function vc(e,i,o){return"string"==typeof e?(e=encodeURI(e).replace(i,Cc),o&&(e=e.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),e):null}function Cc(e){return"%"+((e=e.charCodeAt(0))>>4&15).toString(16)+(15&e).toString(16)}T.prototype.toString=function(){var e=[],i=this.j;i&&e.push(vc(i,tk,!0),":");var o=this.g;return(o||"file"==i)&&(e.push("//"),(i=this.o)&&e.push(vc(i,tk,!0),"@"),e.push(encodeURIComponent(String(o)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),null!=(o=this.s)&&e.push(":",String(o))),(o=this.l)&&(this.g&&"/"!=o.charAt(0)&&e.push("/"),e.push(vc(o,"/"==o.charAt(0)?tR:tO,!0))),(o=this.i.toString())&&e.push("?",o),(o=this.m)&&e.push("#",vc(o,tx)),e.join("")};var tk=/[#\/\?@]/g,tO=/[#\?:]/g,tR=/[#\?]/g,tD=/[#\?@]/g,tx=/#/g;function sc(e,i){this.h=this.g=null,this.i=e||null,this.j=!!i}function U(e){e.g||(e.g=new Map,e.h=0,e.i&&function(e,i){if(e){e=e.split("&");for(var o=0;o<e.length;o++){var s=e[o].indexOf("="),h=null;if(0<=s){var l=e[o].substring(0,s);h=e[o].substring(s+1)}else l=e[o];i(l,h?decodeURIComponent(h.replace(/\+/g," ")):"")}}}(e.i,function(i,o){e.add(decodeURIComponent(i.replace(/\+/g," ")),o)}))}function Dc(e,i){U(e),i=V(e,i),e.g.has(i)&&(e.i=null,e.h-=e.g.get(i).length,e.g.delete(i))}function Ec(e,i){return U(e),i=V(e,i),e.g.has(i)}function Lb(e,i,o){Dc(e,i),0<o.length&&(e.i=null,e.g.set(V(e,i),la(o)),e.h+=o.length)}function V(e,i){return i=String(i),e.j&&(i=i.toLowerCase()),i}function W(e,i,o,s,h){try{h&&(h.onload=null,h.onerror=null,h.onabort=null,h.ontimeout=null),s(o)}catch(e){}}function Hc(){this.g=new tv}function Jc(e){this.l=e.Ub||null,this.j=e.eb||!1}function Kc(e,i){E.call(this),this.D=e,this.o=i,this.m=void 0,this.status=this.readyState=0,this.responseType=this.responseText=this.response=this.statusText="",this.onreadystatechange=null,this.u=new Headers,this.h=null,this.B="GET",this.A="",this.g=!1,this.v=this.j=this.l=null}function Nc(e){e.j.read().then(e.Pa.bind(e)).catch(e.ga.bind(e))}function Mc(e){e.readyState=4,e.l=null,e.j=null,e.v=null,Lc(e)}function Lc(e){e.onreadystatechange&&e.onreadystatechange.call(e)}function Oc(e){let i="";return qa(e,function(e,o){i+=o+":"+e+"\r\n"}),i}function Pc(e,i,o){t:{for(s in o){var s=!1;break t}s=!0}s||(o=Oc(o),"string"==typeof e?null!=o&&encodeURIComponent(String(o)):S(e,i,o))}function X(e){E.call(this),this.headers=new Map,this.o=e||null,this.h=!1,this.v=this.g=null,this.D="",this.m=0,this.l="",this.j=this.B=this.u=this.A=!1,this.I=null,this.H="",this.J=!1}(o=sc.prototype).add=function(e,i){U(this),this.i=null,e=V(this,e);var o=this.g.get(e);return o||this.g.set(e,o=[]),o.push(i),this.h+=1,this},o.forEach=function(e,i){U(this),this.g.forEach(function(o,s){o.forEach(function(o){e.call(i,o,s,this)},this)},this)},o.na=function(){U(this);let e=Array.from(this.g.values()),i=Array.from(this.g.keys()),o=[];for(let s=0;s<i.length;s++){let h=e[s];for(let e=0;e<h.length;e++)o.push(i[s])}return o},o.V=function(e){U(this);let i=[];if("string"==typeof e)Ec(this,e)&&(i=i.concat(this.g.get(V(this,e))));else{e=Array.from(this.g.values());for(let o=0;o<e.length;o++)i=i.concat(e[o])}return i},o.set=function(e,i){return U(this),this.i=null,Ec(this,e=V(this,e))&&(this.h-=this.g.get(e).length),this.g.set(e,[i]),this.h+=1,this},o.get=function(e,i){return e&&0<(e=this.V(e)).length?String(e[0]):i},o.toString=function(){if(this.i)return this.i;if(!this.g)return"";let e=[],i=Array.from(this.g.keys());for(var o=0;o<i.length;o++){var s=i[o];let l=encodeURIComponent(String(s)),f=this.V(s);for(s=0;s<f.length;s++){var h=l;""!==f[s]&&(h+="="+encodeURIComponent(String(f[s]))),e.push(h)}}return this.i=e.join("&")},r(Jc,kb),Jc.prototype.g=function(){return new Kc(this.l,this.j)},Jc.prototype.i=(e={},function(){return e}),r(Kc,E),(o=Kc.prototype).open=function(e,i){if(0!=this.readyState)throw this.abort(),Error("Error reopening a connection");this.B=e,this.A=i,this.readyState=1,Lc(this)},o.send=function(e){if(1!=this.readyState)throw this.abort(),Error("need to call open() first. ");this.g=!0;let i={headers:this.u,method:this.B,credentials:this.m,cache:void 0};e&&(i.body=e),(this.D||tt).fetch(new Request(this.A,i)).then(this.Sa.bind(this),this.ga.bind(this))},o.abort=function(){this.response=this.responseText="",this.u=new Headers,this.status=0,this.j&&this.j.cancel("Request was aborted.").catch(()=>{}),1<=this.readyState&&this.g&&4!=this.readyState&&(this.g=!1,Mc(this)),this.readyState=0},o.Sa=function(e){if(this.g&&(this.l=e,this.h||(this.status=this.l.status,this.statusText=this.l.statusText,this.h=e.headers,this.readyState=2,Lc(this)),this.g&&(this.readyState=3,Lc(this),this.g))){if("arraybuffer"===this.responseType)e.arrayBuffer().then(this.Qa.bind(this),this.ga.bind(this));else if(void 0!==tt.ReadableStream&&"body"in e){if(this.j=e.body.getReader(),this.o){if(this.responseType)throw Error('responseType must be empty for "streamBinaryChunks" mode responses.');this.response=[]}else this.response=this.responseText="",this.v=new TextDecoder;Nc(this)}else e.text().then(this.Ra.bind(this),this.ga.bind(this))}},o.Pa=function(e){if(this.g){if(this.o&&e.value)this.response.push(e.value);else if(!this.o){var i=e.value?e.value:new Uint8Array(0);(i=this.v.decode(i,{stream:!e.done}))&&(this.response=this.responseText+=i)}e.done?Mc(this):Lc(this),3==this.readyState&&Nc(this)}},o.Ra=function(e){this.g&&(this.response=this.responseText=e,Mc(this))},o.Qa=function(e){this.g&&(this.response=e,Mc(this))},o.ga=function(){this.g&&Mc(this)},o.setRequestHeader=function(e,i){this.u.append(e,i)},o.getResponseHeader=function(e){return this.h&&this.h.get(e.toLowerCase())||""},o.getAllResponseHeaders=function(){if(!this.h)return"";let e=[],i=this.h.entries();for(var o=i.next();!o.done;)e.push((o=o.value)[0]+": "+o[1]),o=i.next();return e.join("\r\n")},Object.defineProperty(Kc.prototype,"withCredentials",{get:function(){return"include"===this.m},set:function(e){this.m=e?"include":"same-origin"}}),r(X,E);var tL=/^https?$/i,tU=["POST","PUT"];function Sc(e,i){e.h=!1,e.g&&(e.j=!0,e.g.abort(),e.j=!1),e.l=i,e.m=5,Uc(e),Vc(e)}function Uc(e){e.A||(e.A=!0,F(e,"complete"),F(e,"error"))}function Wc(e){if(e.h&&void 0!==$&&(!e.v[1]||4!=P(e)||2!=e.Z())){if(e.u&&4==P(e))bb(e.Ea,0,e);else if(F(e,"readystatechange"),4==P(e)){e.h=!1;try{let f=e.Z();switch(f){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var i,o,s=!0;break;default:s=!1}if(!(i=s)){if(o=0===f){var h=String(e.D).match(tB)[1]||null;!h&&tt.self&&tt.self.location&&(h=tt.self.location.protocol.slice(0,-1)),o=!tL.test(h?h.toLowerCase():"")}i=o}if(i)F(e,"complete"),F(e,"success");else{e.m=6;try{var l=2<P(e)?e.g.statusText:""}catch(e){l=""}e.l=l+" ["+e.Z()+"]",Uc(e)}}finally{Vc(e)}}}}function Vc(e,i){if(e.g){Tc(e);let o=e.g,s=e.v[0]?()=>{}:null;e.g=null,e.v=null,i||F(e,"ready");try{o.onreadystatechange=s}catch(e){}}}function Tc(e){e.I&&(tt.clearTimeout(e.I),e.I=null)}function P(e){return e.g?e.g.readyState:0}function Nb(e){try{if(!e.g)return null;if("response"in e.g)return e.g.response;switch(e.H){case"":case"text":return e.g.responseText;case"arraybuffer":if("mozResponseArrayBuffer"in e.g)return e.g.mozResponseArrayBuffer}return null}catch(e){return null}}function Xc(e,i,o){return o&&o.internalChannelParams&&o.internalChannelParams[e]||i}function Yc(e){this.Aa=0,this.i=[],this.j=new vb,this.ia=this.qa=this.I=this.W=this.g=this.ya=this.D=this.H=this.m=this.S=this.o=null,this.Ya=this.U=0,this.Va=Xc("failFast",!1,e),this.F=this.C=this.u=this.s=this.l=null,this.X=!0,this.za=this.T=-1,this.Y=this.v=this.B=0,this.Ta=Xc("baseRetryDelayMs",5e3,e),this.cb=Xc("retryDelaySeedMs",1e4,e),this.Wa=Xc("forwardChannelMaxRetries",2,e),this.wa=Xc("forwardChannelRequestTimeoutMs",2e4,e),this.pa=e&&e.xmlHttpFactory||void 0,this.Xa=e&&e.Tb||void 0,this.Ca=e&&e.useFetchStreams||!1,this.L=void 0,this.J=e&&e.supportsCrossDomainXhr||!1,this.K="",this.h=new ic(e&&e.concurrentRequestLimit),this.Da=new Hc,this.P=e&&e.fastHandshake||!1,this.O=e&&e.encodeInitMessageHeaders||!1,this.P&&this.O&&(this.O=!1),this.Ua=e&&e.Rb||!1,e&&e.xa&&this.j.xa(),e&&e.forceLongPolling&&(this.X=!1),this.ba=!this.P&&this.X&&e&&e.detectBufferingProxy||!1,this.ja=void 0,e&&e.longPollingTimeout&&0<e.longPollingTimeout&&(this.ja=e.longPollingTimeout),this.ca=void 0,this.R=0,this.M=!1,this.ka=this.A=null}function gc(e){if(Zc(e),3==e.G){var i=e.U++,o=N(e.I);if(S(o,"SID",e.K),S(o,"RID",i),S(o,"TYPE","terminate"),$c(e,o),(i=new M(e,e.j,i)).L=2,i.v=Ib(N(o)),o=!1,tt.navigator&&tt.navigator.sendBeacon)try{o=tt.navigator.sendBeacon(i.v.toString(),"")}catch(e){}!o&&tt.Image&&((new Image).src=i.v,o=!0),o||(i.g=Mb(i.j,null),i.g.ea(i.v)),i.F=Date.now(),Kb(i)}ad(e)}function Zb(e){e.g&&(Tb(e),e.g.cancel(),e.g=null)}function Zc(e){Zb(e),e.u&&(tt.clearTimeout(e.u),e.u=null),Yb(e),e.h.cancel(),e.s&&("number"==typeof e.s&&tt.clearTimeout(e.s),e.s=null)}function fc(e){if(!jc(e.h)&&!e.s){e.s=!0;var i=e.Ga;ti||Ea(),to||(ti(),to=!0),ts.add(i,e),e.B=0}}function ed(e,i){var o;o=i?i.l:e.U++;let s=N(e.I);S(s,"SID",e.K),S(s,"RID",o),S(s,"AID",e.T),$c(e,s),e.m&&e.o&&Pc(s,e.m,e.o),o=new M(e,e.j,o,e.B+1),null===e.m&&(o.H=e.o),i&&(e.i=i.D.concat(e.i)),i=dd(e,o,1e3),o.I=Math.round(.5*e.wa)+Math.round(.5*e.wa*Math.random()),bc(e.h,o),Hb(o,s,i)}function $c(e,i){e.H&&qa(e.H,function(e,o){S(i,o,e)}),e.l&&nc({},function(e,o){S(i,o,e)})}function dd(e,i,o){o=Math.min(e.i.length,o);var s=e.l?p(e.l.Na,e.l,e):null;t:{var h=e.i;let i=-1;for(;;){let e=["count="+o];-1==i?0<o?(i=h[0].g,e.push("ofs="+i)):i=0:e.push("ofs="+i);let l=!0;for(let f=0;f<o;f++){let o=h[f].g,d=h[f].map;if(0>(o-=i))i=Math.max(0,h[f].g-100),l=!1;else try{!function(e,i,o){let s=o||"";try{nc(e,function(e,o){let h=e;n(e)&&(h=tm(e)),i.push(s+o+"="+encodeURIComponent(h))})}catch(e){throw i.push(s+"type="+encodeURIComponent("_badmap")),e}}(d,e,"req"+o+"_")}catch(e){s&&s(d)}}if(l){s=e.join("&");break t}}}return e=e.i.splice(0,o),i.D=e,s}function ec(e){if(!e.g&&!e.u){e.Y=1;var i=e.Fa;ti||Ea(),to||(ti(),to=!0),ts.add(i,e),e.v=0}}function $b(e){return!e.g&&!e.u&&!(3<=e.v)&&(e.Y++,e.u=ub(p(e.Fa,e),cd(e,e.v)),e.v++,!0)}function Tb(e){null!=e.A&&(tt.clearTimeout(e.A),e.A=null)}function fd(e){e.g=new M(e,e.j,"rpc",e.Y),null===e.m&&(e.g.H=e.o),e.g.O=0;var i=N(e.qa);S(i,"RID","rpc"),S(i,"SID",e.K),S(i,"AID",e.T),S(i,"CI",e.F?"0":"1"),!e.F&&e.ja&&S(i,"TO",e.ja),S(i,"TYPE","xmlhttp"),$c(e,i),e.m&&e.o&&Pc(i,e.m,e.o),e.L&&(e.g.I=e.L);var o=e.g;e=e.ia,o.L=1,o.v=Ib(N(i)),o.m=null,o.P=!0,Jb(o,e)}function Yb(e){null!=e.C&&(tt.clearTimeout(e.C),e.C=null)}function Ub(e,i){var o=null;if(e.g==i){Yb(e),Tb(e),e.g=null;var s=2}else{if(!Xb(e.h,i))return;o=i.D,dc(e.h,i),s=1}if(0!=e.G){if(i.o){if(1==s){o=i.m?i.m.length:0,i=Date.now()-i.F;var h,l=e.B;F(s=qb(),new tb(s,o)),fc(e)}else ec(e)}else if(3==(l=i.s)||0==l&&0<i.X||!(1==s&&(h=i,!(ac(e.h)>=e.h.j-(e.s?1:0))&&(e.s?(e.i=h.D.concat(e.i),!0):1!=e.G&&2!=e.G&&!(e.B>=(e.Va?0:e.Wa))&&(e.s=ub(p(e.Ga,e,h),cd(e,e.B)),e.B++,!0)))||2==s&&$b(e)))switch(o&&0<o.length&&((i=e.h).i=i.i.concat(o)),l){case 1:R(e,5);break;case 4:R(e,10);break;case 3:R(e,6);break;default:R(e,2)}}}function cd(e,i){let o=e.Ta+Math.floor(Math.random()*e.cb);return e.isActive()||(o*=2),o*i}function R(e,i){if(e.j.info("Error code "+i),2==i){var o=p(e.fb,e),s=e.Xa;let i=!s;s=new T(s||"//www.google.com/images/cleardot.gif"),tt.location&&"http"==tt.location.protocol||qc(s,"https"),Ib(s),i?function(e,i){let o=new vb;if(tt.Image){let s=new Image;s.onload=ka(W,o,"TestLoadImage: loaded",!0,i,s),s.onerror=ka(W,o,"TestLoadImage: error",!1,i,s),s.onabort=ka(W,o,"TestLoadImage: abort",!1,i,s),s.ontimeout=ka(W,o,"TestLoadImage: timeout",!1,i,s),tt.setTimeout(function(){s.ontimeout&&s.ontimeout()},1e4),s.src=e}else i(!1)}(s.toString(),o):function(e,i){let o=new vb,s=new AbortController,h=setTimeout(()=>{s.abort(),W(o,"TestPingServer: timeout",!1,i)},1e4);fetch(e,{signal:s.signal}).then(e=>{clearTimeout(h),e.ok?W(o,"TestPingServer: ok",!0,i):W(o,"TestPingServer: server error",!1,i)}).catch(()=>{clearTimeout(h),W(o,"TestPingServer: error",!1,i)})}(s.toString(),o)}else K(2);e.G=0,e.l&&e.l.sa(i),ad(e),Zc(e)}function ad(e){if(e.G=0,e.ka=[],e.l){let i=kc(e.h);(0!=i.length||0!=e.i.length)&&(ma(e.ka,i),ma(e.ka,e.i),e.h.i.length=0,la(e.i),e.i.length=0),e.l.ra()}}function cc(e,i,o){var s=o instanceof T?N(o):new T(o);if(""!=s.g)i&&(s.g=i+"."+s.g),rc(s,s.s);else{var h=tt.location;s=h.protocol,i=i?i+"."+h.hostname:h.hostname,h=+h.port;var l=new T(null);s&&qc(l,s),i&&(l.g=i),h&&rc(l,h),o&&(l.l=o),s=l}return o=e.D,i=e.ya,o&&i&&S(s,o,i),S(s,"VER",e.la),$c(e,s),s}function Mb(e,i,o){if(i&&!e.J)throw Error("Can't create secondary domain capable XhrIo object.");return(i=new X(e.Ca&&!e.pa?new Jc({eb:o}):e.pa)).Ha(e.J),i}function gd(){}function hd(){}function Y(e,i){E.call(this),this.g=new Yc(i),this.l=e,this.h=i&&i.messageUrlParams||null,e=i&&i.messageHeaders||null,i&&i.clientProtocolHeaderRequired&&(e?e["X-Client-Protocol"]="webchannel":e={"X-Client-Protocol":"webchannel"}),this.g.o=e,e=i&&i.initMessageHeaders||null,i&&i.messageContentType&&(e?e["X-WebChannel-Content-Type"]=i.messageContentType:e={"X-WebChannel-Content-Type":i.messageContentType}),i&&i.va&&(e?e["X-WebChannel-Client-Profile"]=i.va:e={"X-WebChannel-Client-Profile":i.va}),this.g.S=e,(e=i&&i.Sb)&&!t(e)&&(this.g.m=e),this.v=i&&i.supportsCrossDomainXhr||!1,this.u=i&&i.sendRawJson||!1,(i=i&&i.httpSessionIdParam)&&!t(i)&&(this.g.D=i,null!==(e=this.h)&&i in e&&i in(e=this.h)&&delete e[i]),this.j=new Z(this)}function id(e){nb.call(this),e.__headers__&&(this.headers=e.__headers__,this.statusCode=e.__status__,delete e.__headers__,delete e.__status__);var i=e.__sm__;if(i){t:{for(let o in i){e=o;break t}e=void 0}(this.i=e)&&(e=this.i,i=null!==i&&e in i?i[e]:void 0),this.data=i}else this.data=e}function jd(){ob.call(this),this.status=1}function Z(e){this.g=e}(o=X.prototype).Ha=function(e){this.J=e},o.ea=function(e,o,s,h){if(this.g)throw Error("[goog.net.XhrIo] Object is active with another request="+this.D+"; newUri="+e);o=o?o.toUpperCase():"GET",this.D=e,this.l="",this.m=0,this.A=!1,this.h=!0,this.g=this.o?this.o.g():i.g(),this.v=this.o?lb(this.o):lb(i),this.g.onreadystatechange=p(this.Ea,this);try{this.B=!0,this.g.open(o,String(e),!0),this.B=!1}catch(e){Sc(this,e);return}if(e=s||"",s=new Map(this.headers),h){if(Object.getPrototypeOf(h)===Object.prototype)for(var l in h)s.set(l,h[l]);else if("function"==typeof h.keys&&"function"==typeof h.get)for(let e of h.keys())s.set(e,h.get(e));else throw Error("Unknown input type for opt_headers: "+String(h))}for(let[i,f]of(h=Array.from(s.keys()).find(e=>"content-type"==e.toLowerCase()),l=tt.FormData&&e instanceof tt.FormData,!(0<=Array.prototype.indexOf.call(tU,o,void 0))||h||l||s.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8"),s))this.g.setRequestHeader(i,f);this.H&&(this.g.responseType=this.H),"withCredentials"in this.g&&this.g.withCredentials!==this.J&&(this.g.withCredentials=this.J);try{Tc(this),this.u=!0,this.g.send(e),this.u=!1}catch(e){Sc(this,e)}},o.abort=function(e){this.g&&this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1,this.m=e||7,F(this,"complete"),F(this,"abort"),Vc(this))},o.N=function(){this.g&&(this.h&&(this.h=!1,this.j=!0,this.g.abort(),this.j=!1),Vc(this,!0)),X.aa.N.call(this)},o.Ea=function(){this.s||(this.B||this.u||this.j?Wc(this):this.bb())},o.bb=function(){Wc(this)},o.isActive=function(){return!!this.g},o.Z=function(){try{return 2<P(this)?this.g.status:-1}catch(e){return -1}},o.oa=function(){try{return this.g?this.g.responseText:""}catch(e){return""}},o.Oa=function(e){if(this.g){var i=this.g.responseText;return e&&0==i.indexOf(e)&&(i=i.substring(e.length)),ty(i)}},o.Ba=function(){return this.m},o.Ka=function(){return"string"==typeof this.l?this.l:String(this.l)},(o=Yc.prototype).la=8,o.G=1,o.connect=function(e,i,o,s){K(0),this.W=e,this.H=i||{},o&&void 0!==s&&(this.H.OSID=o,this.H.OAID=s),this.F=this.X,this.I=cc(this,null,this.W),fc(this)},o.Ga=function(e){if(this.s){if(this.s=null,1==this.G){if(!e){this.U=Math.floor(1e5*Math.random()),e=this.U++;let h=new M(this,this.j,e),l=this.o;if(this.S&&(l?ua(l=sa(l),this.S):l=this.S),null!==this.m||this.O||(h.H=l,l=null),this.P)t:{for(var i=0,o=0;o<this.i.length;o++){e:{var s=this.i[o];if("__data__"in s.map&&"string"==typeof(s=s.map.__data__)){s=s.length;break e}s=void 0}if(void 0===s)break;if(4096<(i+=s)){i=o;break t}if(4096===i||o===this.i.length-1){i=o+1;break t}}i=1e3}else i=1e3;i=dd(this,h,i),S(o=N(this.I),"RID",e),S(o,"CVER",22),this.D&&S(o,"X-HTTP-Session-Id",this.D),$c(this,o),l&&(this.O?i="headers="+encodeURIComponent(String(Oc(l)))+"&"+i:this.m&&Pc(o,this.m,l)),bc(this.h,h),this.Ua&&S(o,"TYPE","init"),this.P?(S(o,"$req",i),S(o,"SID","null"),h.T=!0,Hb(h,o,null)):Hb(h,o,i),this.G=2}}else 3==this.G&&(e?ed(this,e):0==this.i.length||jc(this.h)||ed(this))}},o.Fa=function(){if(this.u=null,fd(this),this.ba&&!(this.M||null==this.g||0>=this.R)){var e=2*this.R;this.j.info("BP detection timer enabled: "+e),this.A=ub(p(this.ab,this),e)}},o.ab=function(){this.A&&(this.A=null,this.j.info("BP detection timeout reached."),this.j.info("Buffering proxy detected and switch to long-polling!"),this.F=!1,this.M=!0,K(10),Zb(this),fd(this))},o.Za=function(){null!=this.C&&(this.C=null,Zb(this),$b(this),K(19))},o.fb=function(e){e?(this.j.info("Successfully pinged google.com"),K(2)):(this.j.info("Failed to ping google.com"),K(1))},o.isActive=function(){return!!this.l&&this.l.isActive(this)},(o=gd.prototype).ua=function(){},o.ta=function(){},o.sa=function(){},o.ra=function(){},o.isActive=function(){return!0},o.Na=function(){},hd.prototype.g=function(e,i){return new Y(e,i)},r(Y,E),Y.prototype.m=function(){this.g.l=this.j,this.v&&(this.g.J=!0),this.g.connect(this.l,this.h||void 0)},Y.prototype.close=function(){gc(this.g)},Y.prototype.o=function(e){var i=this.g;if("string"==typeof e){var o={};o.__data__=e,e=o}else this.u&&((o={}).__data__=tm(e),e=o);i.i.push(new tI(i.Ya++,e)),3==i.G&&fc(i)},Y.prototype.N=function(){this.g.l=null,delete this.j,gc(this.g),delete this.g,Y.aa.N.call(this)},r(id,nb),r(jd,ob),r(Z,gd),Z.prototype.ua=function(){F(this.g,"a")},Z.prototype.ta=function(e){F(this.g,new id(e))},Z.prototype.sa=function(e){F(this.g,new jd)},Z.prototype.ra=function(){F(this.g,"b")},hd.prototype.createWebChannel=hd.prototype.g,Y.prototype.send=Y.prototype.o,Y.prototype.open=Y.prototype.m,Y.prototype.close=Y.prototype.close,w=k.createWebChannelTransport=function(){return new hd},b=k.getStatEventTarget=function(){return qb()},g=k.Event=t_,d=k.Stat={mb:0,pb:1,qb:2,Jb:3,Ob:4,Lb:5,Mb:6,Kb:7,Ib:8,Nb:9,PROXY:10,NOPROXY:11,Gb:12,Cb:13,Db:14,Bb:15,Eb:16,Fb:17,ib:18,hb:19,jb:20},tT.NO_ERROR=0,tT.TIMEOUT=8,tT.HTTP_ERROR=6,f=k.ErrorCode=tT,tC.COMPLETE="complete",l=k.EventType=tC,mb.EventType=tw,tw.OPEN="a",tw.CLOSE="b",tw.ERROR="c",tw.MESSAGE="d",E.prototype.listen=E.prototype.K,h=k.WebChannel=mb,k.FetchXmlHttpFactory=Jc,X.prototype.listenOnce=X.prototype.L,X.prototype.getLastError=X.prototype.Ka,X.prototype.getLastErrorCode=X.prototype.Ba,X.prototype.getStatus=X.prototype.Z,X.prototype.getResponseJson=X.prototype.Oa,X.prototype.getResponseText=X.prototype.oa,X.prototype.send=X.prototype.ea,X.prototype.setWithCredentials=X.prototype.Ha,s=k.XhrIo=X}).apply(void 0!==_?_:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},994:function(e,i,o){"use strict";o.d(i,{ZF:function(){return s.ZF}});var s=o(3304);/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */(0,s.KN)("firebase","12.0.0","app")},8081:function(e,i,o){"use strict";o.d(i,{w9:function(){return s.W},Xb:function(){return s.ab},v0:function(){return s.p},Aj:function(){return s.z},aF:function(){return s.a4},e5:function(){return s.ac},w7:function(){return s.D},gQ:function(){return s.an}});var s=o(9395);o(3304),o(4534),o(8650),o(3576)},4086:function(e,i,o){"use strict";o.d(i,{Xo:function(){return s.Xo},ad:function(){return s.ad},collection:function(){return s.hJ},doc:function(){return s.JU},getDoc:function(){return s.QT},getDocs:function(){return s.PL},oe:function(){return s.oe},pl:function(){return s.pl},query:function(){return s.IO},r7:function(){return s.r7},where:function(){return s.ar}});var s=o(4471)},3216:function(e,i,o){"use strict";o.d(i,{$C:function(){return getFunctions}});var s=o(3304),h=o(4534),l=o(3576);/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let f="functions";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let ContextProvider=class ContextProvider{constructor(e,i,o,h){this.app=e,this.auth=null,this.messaging=null,this.appCheck=null,this.serverAppAppCheckToken=null,(0,s.rh)(e)&&e.settings.appCheckToken&&(this.serverAppAppCheckToken=e.settings.appCheckToken),this.auth=i.getImmediate({optional:!0}),this.messaging=o.getImmediate({optional:!0}),this.auth||i.get().then(e=>this.auth=e,()=>{}),this.messaging||o.get().then(e=>this.messaging=e,()=>{}),this.appCheck||h?.get().then(e=>this.appCheck=e,()=>{})}async getAuthToken(){if(this.auth)try{let e=await this.auth.getToken();return e?.accessToken}catch(e){return}}async getMessagingToken(){if(this.messaging&&"Notification"in self&&"granted"===Notification.permission)try{return await this.messaging.getToken()}catch(e){return}}async getAppCheckToken(e){if(this.serverAppAppCheckToken)return this.serverAppAppCheckToken;if(this.appCheck){let i=e?await this.appCheck.getLimitedUseToken():await this.appCheck.getToken();return i.error?null:i.token}return null}async getContext(e){let i=await this.getAuthToken(),o=await this.getMessagingToken(),s=await this.getAppCheckToken(e);return{authToken:i,messagingToken:o,appCheckToken:s}}};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let d="us-central1";let FunctionsService=class FunctionsService{constructor(e,i,o,s,h=d,l=(...e)=>fetch(...e)){this.app=e,this.fetchImpl=l,this.emulatorOrigin=null,this.contextProvider=new ContextProvider(e,i,o,s),this.cancelAllRequests=new Promise(e=>{this.deleteService=()=>Promise.resolve(e())});try{let e=new URL(h);this.customDomain=e.origin+("/"===e.pathname?"":e.pathname),this.region=d}catch(e){this.customDomain=null,this.region=h}}_delete(){return this.deleteService()}_url(e){let i=this.app.options.projectId;if(null!==this.emulatorOrigin){let o=this.emulatorOrigin;return`${o}/${i}/${this.region}/${e}`}return null!==this.customDomain?`${this.customDomain}/${e}`:`https://${this.region}-${i}.cloudfunctions.net/${e}`}};let g="@firebase/functions",b="0.13.0";/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function getFunctions(e=(0,s.Mq)(),i=d){let o=(0,s.qX)((0,h.m9)(e),f),l=o.getImmediate({identifier:i}),g=(0,h.P0)("functions");return g&&function(e,i,o){!function(e,i,o){let s=(0,h.Xx)(i);e.emulatorOrigin=`http${s?"s":""}://${i}:${o}`,s&&((0,h.Uo)(e.emulatorOrigin),(0,h.dp)("Functions",!0))}((0,h.m9)(e),i,o)}(l,...g),l}(0,s.Xd)(new l.wA(f,(e,{instanceIdentifier:i})=>{let o=e.getProvider("app").getImmediate(),s=e.getProvider("auth-internal"),h=e.getProvider("messaging-internal"),l=e.getProvider("app-check-internal");return new FunctionsService(o,s,h,l,i)},"PUBLIC").setMultipleInstances(!0)),(0,s.KN)(g,b,void 0),(0,s.KN)(g,b,"esm2020")},5813:function(e,i,o){"use strict";o.d(i,{oq:function(){return deleteObject},Jt:function(){return getDownloadURL},cF:function(){return getStorage},aF:function(){return listAll},iH:function(){return ref},KV:function(){return uploadBytes}});var s,h,l,f,d=o(3304),g=o(4534),b=o(3576);/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let w="firebasestorage.googleapis.com",_="storageBucket";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let StorageError=class StorageError extends g.ZR{constructor(e,i,o=0){super(prependCode(e),`Firebase Storage: ${i} (${prependCode(e)})`),this.status_=o,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,StorageError.prototype)}get status(){return this.status_}set status(e){this.status_=e}_codeEquals(e){return prependCode(e)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(e){this.customData.serverResponse=e,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}};function prependCode(e){return"storage/"+e}function unknown(){return new StorageError(l.UNKNOWN,"An unknown error occurred, please check the error payload for server response.")}function invalidArgument(e){return new StorageError(l.INVALID_ARGUMENT,e)}function appDeleted(){return new StorageError(l.APP_DELETED,"The Firebase app was deleted.")}function invalidFormat(e,i){return new StorageError(l.INVALID_FORMAT,"String does not match format '"+e+"': "+i)}function internalError(e){throw new StorageError(l.INTERNAL_ERROR,"Internal error: "+e)}(s=l||(l={})).UNKNOWN="unknown",s.OBJECT_NOT_FOUND="object-not-found",s.BUCKET_NOT_FOUND="bucket-not-found",s.PROJECT_NOT_FOUND="project-not-found",s.QUOTA_EXCEEDED="quota-exceeded",s.UNAUTHENTICATED="unauthenticated",s.UNAUTHORIZED="unauthorized",s.UNAUTHORIZED_APP="unauthorized-app",s.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",s.INVALID_CHECKSUM="invalid-checksum",s.CANCELED="canceled",s.INVALID_EVENT_NAME="invalid-event-name",s.INVALID_URL="invalid-url",s.INVALID_DEFAULT_BUCKET="invalid-default-bucket",s.NO_DEFAULT_BUCKET="no-default-bucket",s.CANNOT_SLICE_BLOB="cannot-slice-blob",s.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",s.NO_DOWNLOAD_URL="no-download-url",s.INVALID_ARGUMENT="invalid-argument",s.INVALID_ARGUMENT_COUNT="invalid-argument-count",s.APP_DELETED="app-deleted",s.INVALID_ROOT_OPERATION="invalid-root-operation",s.INVALID_FORMAT="invalid-format",s.INTERNAL_ERROR="internal-error",s.UNSUPPORTED_ENVIRONMENT="unsupported-environment";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Location=class Location{constructor(e,i){this.bucket=e,this.path_=i}get path(){return this.path_}get isRoot(){return 0===this.path.length}fullServerUrl(){let e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o/"+e(this.path)}bucketOnlyServerUrl(){let e=encodeURIComponent;return"/b/"+e(this.bucket)+"/o"}static makeFromBucketSpec(e,i){let o;try{o=Location.makeFromUrl(e,i)}catch(i){return new Location(e,"")}if(""===o.path)return o;throw new StorageError(l.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+e+"'.")}static makeFromUrl(e,i){let o=null,s="([A-Za-z0-9.\\-_]+)",h=RegExp("^gs://"+s+"(/(.*))?$","i");function httpModify(e){e.path_=decodeURIComponent(e.path)}let f=i.replace(/[.]/g,"\\."),d=RegExp(`^https?://${f}/v[A-Za-z0-9_]+/b/${s}/o(/([^?#]*).*)?$`,"i"),g=RegExp(`^https?://${i===w?"(?:storage.googleapis.com|storage.cloud.google.com)":i}/${s}/([^?#]*)`,"i"),b=[{regex:h,indices:{bucket:1,path:3},postModify:function(e){"/"===e.path.charAt(e.path.length-1)&&(e.path_=e.path_.slice(0,-1))}},{regex:d,indices:{bucket:1,path:3},postModify:httpModify},{regex:g,indices:{bucket:1,path:2},postModify:httpModify}];for(let i=0;i<b.length;i++){let s=b[i],h=s.regex.exec(e);if(h){let e=h[s.indices.bucket],i=h[s.indices.path];i||(i=""),o=new Location(e,i),s.postModify(o);break}}if(null==o)throw new StorageError(l.INVALID_URL,"Invalid URL '"+e+"'.");return o}};let FailRequest=class FailRequest{constructor(e){this.promise_=Promise.reject(e)}getPromise(){return this.promise_}cancel(e=!1){}};function isString(e){return"string"==typeof e||e instanceof String}function isNativeBlob(e){return isNativeBlobDefined()&&e instanceof Blob}function isNativeBlobDefined(){return"undefined"!=typeof Blob}function validateNumber(e,i,o,s){if(s<i)throw invalidArgument(`Invalid value for '${e}'. Expected ${i} or greater.`);if(s>o)throw invalidArgument(`Invalid value for '${e}'. Expected ${o} or less.`)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function makeUrl(e,i,o){let s=i;return null==o&&(s=`https://${i}`),`${o}://${s}/v0${e}`}function makeQueryString(e){let i=encodeURIComponent,o="?";for(let s in e)if(e.hasOwnProperty(s)){let h=i(s)+"="+i(e[s]);o=o+h+"&"}return o.slice(0,-1)}(h=f||(f={}))[h.NO_ERROR=0]="NO_ERROR",h[h.NETWORK_ERROR=1]="NETWORK_ERROR",h[h.ABORT=2]="ABORT";/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let NetworkRequest=class NetworkRequest{constructor(e,i,o,s,h,l,f,d,g,b,w,_=!0,k=!1){this.url_=e,this.method_=i,this.headers_=o,this.body_=s,this.successCodes_=h,this.additionalRetryCodes_=l,this.callback_=f,this.errorCallback_=d,this.timeout_=g,this.progressCallback_=b,this.connectionFactory_=w,this.retry=_,this.isUsingEmulator=k,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((e,i)=>{this.resolve_=e,this.reject_=i,this.start_()})}start_(){let backoffDone=(e,i)=>{let o=this.resolve_,s=this.reject_,h=i.connection;if(i.wasSuccessCode)try{let e=this.callback_(h,h.getResponse());void 0!==e?o(e):o()}catch(e){s(e)}else if(null!==h){let e=unknown();e.serverResponse=h.getErrorText(),s(this.errorCallback_?this.errorCallback_(h,e):e)}else if(i.canceled){let e=this.appDelete_?appDeleted():new StorageError(l.CANCELED,"User canceled the upload/download.");s(e)}else{let e=new StorageError(l.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.");s(e)}};this.canceled_?backoffDone(!1,new RequestEndStatus(!1,null,!0)):this.backoffId_=/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function(e,i,o){let s=1,h=null,l=null,f=!1,d=0,g=!1;function triggerCallback(...e){g||(g=!0,i.apply(null,e))}function callWithDelay(i){h=setTimeout(()=>{h=null,e(responseHandler,2===d)},i)}function clearGlobalTimeout(){l&&clearTimeout(l)}function responseHandler(e,...i){let o;if(g){clearGlobalTimeout();return}if(e){clearGlobalTimeout(),triggerCallback.call(null,e,...i);return}let h=2===d||f;if(h){clearGlobalTimeout(),triggerCallback.call(null,e,...i);return}s<64&&(s*=2),1===d?(d=2,o=0):o=(s+Math.random())*1e3,callWithDelay(o)}let b=!1;function stop(e){!b&&(b=!0,clearGlobalTimeout(),!g&&(null!==h?(e||(d=2),clearTimeout(h),callWithDelay(0)):e||(d=1)))}return callWithDelay(0),l=setTimeout(()=>{f=!0,stop(!0)},o),stop}((e,i)=>{if(i){e(!1,new RequestEndStatus(!1,null,!0));return}let o=this.connectionFactory_();this.pendingConnection_=o;let progressListener=e=>{let i=e.loaded,o=e.lengthComputable?e.total:-1;null!==this.progressCallback_&&this.progressCallback_(i,o)};null!==this.progressCallback_&&o.addUploadProgressListener(progressListener),o.send(this.url_,this.method_,this.isUsingEmulator,this.body_,this.headers_).then(()=>{null!==this.progressCallback_&&o.removeUploadProgressListener(progressListener),this.pendingConnection_=null;let i=o.getErrorCode()===f.NO_ERROR,s=o.getStatus();if(!i||/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function(e,i){let o=e>=500&&e<600,s=-1!==[408,429].indexOf(e),h=-1!==i.indexOf(e);return o||s||h}(s,this.additionalRetryCodes_)&&this.retry){let i=o.getErrorCode()===f.ABORT;e(!1,new RequestEndStatus(!1,null,i));return}let h=-1!==this.successCodes_.indexOf(s);e(!0,new RequestEndStatus(h,o))})},backoffDone,this.timeout_)}getPromise(){return this.promise_}cancel(e){this.canceled_=!0,this.appDelete_=e||!1,null!==this.backoffId_&&(0,this.backoffId_)(!1),null!==this.pendingConnection_&&this.pendingConnection_.abort()}};let RequestEndStatus=class RequestEndStatus{constructor(e,i,o){this.wasSuccessCode=e,this.connection=i,this.canceled=!!o}};function getBlob$1(...e){let i="undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof WebKitBlobBuilder?WebKitBlobBuilder:void 0;if(void 0!==i){let o=new i;for(let i=0;i<e.length;i++)o.append(e[i]);return o.getBlob()}if(isNativeBlobDefined())return new Blob(e);throw new StorageError(l.UNSUPPORTED_ENVIRONMENT,"This browser doesn't seem to support creating Blobs")}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let k={RAW:"raw",BASE64:"base64",BASE64URL:"base64url",DATA_URL:"data_url"};let StringData=class StringData{constructor(e,i){this.data=e,this.contentType=i||null}};function utf8Bytes_(e){let i=[];for(let o=0;o<e.length;o++){let s=e.charCodeAt(o);if(s<=127)i.push(s);else if(s<=2047)i.push(192|s>>6,128|63&s);else if((64512&s)==55296){let h=o<e.length-1&&(64512&e.charCodeAt(o+1))==56320;if(h){let h=s,l=e.charCodeAt(++o);s=65536|(1023&h)<<10|1023&l,i.push(240|s>>18,128|s>>12&63,128|s>>6&63,128|63&s)}else i.push(239,191,189)}else(64512&s)==56320?i.push(239,191,189):i.push(224|s>>12,128|s>>6&63,128|63&s)}return new Uint8Array(i)}function base64Bytes_(e,i){let o;switch(e){case k.BASE64:{let o=-1!==i.indexOf("-"),s=-1!==i.indexOf("_");if(o||s)throw invalidFormat(e,"Invalid character '"+(o?"-":"_")+"' found: is it base64url encoded?");break}case k.BASE64URL:{let o=-1!==i.indexOf("+"),s=-1!==i.indexOf("/");if(o||s)throw invalidFormat(e,"Invalid character '"+(o?"+":"/")+"' found: is it base64 encoded?");i=i.replace(/-/g,"+").replace(/_/g,"/")}}try{o=/**
 * @license
 * Copyright 2021 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function(e){if("undefined"==typeof atob)throw new StorageError(l.UNSUPPORTED_ENVIRONMENT,"base-64 is missing. Make sure to install the required polyfills. See https://firebase.google.com/docs/web/environments-js-sdk#polyfills for more information.");return atob(e)}(i)}catch(i){if(i.message.includes("polyfill"))throw i;throw invalidFormat(e,"Invalid character found")}let s=new Uint8Array(o.length);for(let e=0;e<o.length;e++)s[e]=o.charCodeAt(e);return s}let DataURLParts=class DataURLParts{constructor(e){this.base64=!1,this.contentType=null;let i=e.match(/^data:([^,]+)?,/);if(null===i)throw invalidFormat(k.DATA_URL,"Must be formatted 'data:[<mediatype>][;base64],<data>");let o=i[1]||null;null!=o&&(this.base64=function(e,i){let o=e.length>=i.length;return!!o&&e.substring(e.length-i.length)===i}(o,";base64"),this.contentType=this.base64?o.substring(0,o.length-7):o),this.rest=e.substring(e.indexOf(",")+1)}};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let FbsBlob=class FbsBlob{constructor(e,i){let o=0,s="";isNativeBlob(e)?(this.data_=e,o=e.size,s=e.type):e instanceof ArrayBuffer?(i?this.data_=new Uint8Array(e):(this.data_=new Uint8Array(e.byteLength),this.data_.set(new Uint8Array(e))),o=this.data_.length):e instanceof Uint8Array&&(i?this.data_=e:(this.data_=new Uint8Array(e.length),this.data_.set(e)),o=e.length),this.size_=o,this.type_=s}size(){return this.size_}type(){return this.type_}slice(e,i){if(isNativeBlob(this.data_)){let o=this.data_,s=o.webkitSlice?o.webkitSlice(e,i):o.mozSlice?o.mozSlice(e,i):o.slice?o.slice(e,i):null;return null===s?null:new FbsBlob(s)}{let o=new Uint8Array(this.data_.buffer,e,i-e);return new FbsBlob(o,!0)}}static getBlob(...e){if(isNativeBlobDefined()){let i=e.map(e=>e instanceof FbsBlob?e.data_:e);return new FbsBlob(getBlob$1.apply(null,i))}{let i=e.map(e=>isString(e)?function(e,i){switch(e){case k.RAW:return new StringData(utf8Bytes_(i));case k.BASE64:case k.BASE64URL:return new StringData(base64Bytes_(e,i));case k.DATA_URL:return new StringData(function(e){let i=new DataURLParts(e);return i.base64?base64Bytes_(k.BASE64,i.rest):function(e){let i;try{i=decodeURIComponent(e)}catch(e){throw invalidFormat(k.DATA_URL,"Malformed data URL.")}return utf8Bytes_(i)}(i.rest)}(i),function(e){let i=new DataURLParts(e);return i.contentType}(i))}throw unknown()}(k.RAW,e).data:e.data_),o=0;i.forEach(e=>{o+=e.byteLength});let s=new Uint8Array(o),h=0;return i.forEach(e=>{for(let i=0;i<e.length;i++)s[h++]=e[i]}),new FbsBlob(s,!0)}}uploadData(){return this.data_}};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function jsonObjectOrNull(e){var i;let o;try{o=JSON.parse(e)}catch(e){return null}return"object"!=typeof(i=o)||Array.isArray(i)?null:o}function lastComponent(e){let i=e.lastIndexOf("/",e.length-2);return -1===i?e:e.slice(i+1)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function noXform_(e,i){return i}let Mapping=class Mapping{constructor(e,i,o,s){this.server=e,this.local=i||e,this.writable=!!o,this.xform=s||noXform_}};let O=null;function getMappings(){if(O)return O;let e=[];e.push(new Mapping("bucket")),e.push(new Mapping("generation")),e.push(new Mapping("metageneration")),e.push(new Mapping("name","fullPath",!0));let i=new Mapping("name");i.xform=function(e,i){return!isString(i)||i.length<2?i:lastComponent(i)},e.push(i);let o=new Mapping("size");return o.xform=function(e,i){return void 0!==i?Number(i):i},e.push(o),e.push(new Mapping("timeCreated")),e.push(new Mapping("updated")),e.push(new Mapping("md5Hash",null,!0)),e.push(new Mapping("cacheControl",null,!0)),e.push(new Mapping("contentDisposition",null,!0)),e.push(new Mapping("contentEncoding",null,!0)),e.push(new Mapping("contentLanguage",null,!0)),e.push(new Mapping("contentType",null,!0)),e.push(new Mapping("metadata","customMetadata",!0)),O=e}function fromResourceString(e,i,o){let s=jsonObjectOrNull(i);return null===s?null:function(e,i,o){let s={};s.type="file";let h=o.length;for(let e=0;e<h;e++){let h=o[e];s[h.local]=h.xform(s,i[h.server])}return Object.defineProperty(s,"ref",{get:function(){let i=s.bucket,o=s.fullPath,h=new Location(i,o);return e._makeStorageReference(h)}}),s}(e,s,o)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let j="prefixes",$="items";let RequestInfo=class RequestInfo{constructor(e,i,o,s){this.url=e,this.method=i,this.handler=o,this.timeout=s,this.urlParams={},this.headers={},this.body=null,this.errorHandler=null,this.progressCallback=null,this.successCodes=[200],this.additionalRetryCodes=[]}};/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function handlerCheck(e){if(!e)throw unknown()}function sharedErrorHandler(e){return function(i,o){var s,h;let f;return 401===i.getStatus()?f=i.getErrorText().includes("Firebase App Check token is invalid")?new StorageError(l.UNAUTHORIZED_APP,"This app does not have permission to access Firebase Storage on this project."):new StorageError(l.UNAUTHENTICATED,"User is not authenticated, please authenticate using Firebase Authentication and try again."):402===i.getStatus()?(s=e.bucket,f=new StorageError(l.QUOTA_EXCEEDED,"Quota for bucket '"+s+"' exceeded, please view quota on https://firebase.google.com/pricing/.")):403===i.getStatus()?(h=e.path,f=new StorageError(l.UNAUTHORIZED,"User does not have permission to access '"+h+"'.")):f=o,f.status=i.getStatus(),f.serverResponse=o.serverResponse,f}}function objectErrorHandler(e){let i=sharedErrorHandler(e);return function(o,s){let h=i(o,s);if(404===o.getStatus()){var f;f=e.path,h=new StorageError(l.OBJECT_NOT_FOUND,"Object '"+f+"' does not exist.")}return h.serverResponse=s.serverResponse,h}}let XhrConnection=class XhrConnection{constructor(){this.sent_=!1,this.xhr_=new XMLHttpRequest,this.initXhr(),this.errorCode_=f.NO_ERROR,this.sendPromise_=new Promise(e=>{this.xhr_.addEventListener("abort",()=>{this.errorCode_=f.ABORT,e()}),this.xhr_.addEventListener("error",()=>{this.errorCode_=f.NETWORK_ERROR,e()}),this.xhr_.addEventListener("load",()=>{e()})})}send(e,i,o,s,h){if(this.sent_)throw internalError("cannot .send() more than once");if((0,g.Xx)(e)&&o&&(this.xhr_.withCredentials=!0),this.sent_=!0,this.xhr_.open(i,e,!0),void 0!==h)for(let e in h)h.hasOwnProperty(e)&&this.xhr_.setRequestHeader(e,h[e].toString());return void 0!==s?this.xhr_.send(s):this.xhr_.send(),this.sendPromise_}getErrorCode(){if(!this.sent_)throw internalError("cannot .getErrorCode() before sending");return this.errorCode_}getStatus(){if(!this.sent_)throw internalError("cannot .getStatus() before sending");try{return this.xhr_.status}catch(e){return -1}}getResponse(){if(!this.sent_)throw internalError("cannot .getResponse() before sending");return this.xhr_.response}getErrorText(){if(!this.sent_)throw internalError("cannot .getErrorText() before sending");return this.xhr_.statusText}abort(){this.xhr_.abort()}getResponseHeader(e){return this.xhr_.getResponseHeader(e)}addUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.addEventListener("progress",e)}removeUploadProgressListener(e){null!=this.xhr_.upload&&this.xhr_.upload.removeEventListener("progress",e)}};let XhrTextConnection=class XhrTextConnection extends XhrConnection{initXhr(){this.xhr_.responseType="text"}};function newTextConnection(){return new XhrTextConnection}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */let Reference=class Reference{constructor(e,i){this._service=e,i instanceof Location?this._location=i:this._location=Location.makeFromUrl(i,e.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(e,i){return new Reference(e,i)}get root(){let e=new Location(this._location.bucket,"");return this._newRef(this._service,e)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return lastComponent(this._location.path)}get storage(){return this._service}get parent(){let e=/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function(e){if(0===e.length)return null;let i=e.lastIndexOf("/");if(-1===i)return"";let o=e.slice(0,i);return o}(this._location.path);if(null===e)return null;let i=new Location(this._location.bucket,e);return new Reference(this._service,i)}_throwIfRoot(e){if(""===this._location.path)throw new StorageError(l.INVALID_ROOT_OPERATION,"The operation '"+e+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}};async function listAllHelper(e,i,o){let s=await function(e,i){null!=i&&"number"==typeof i.maxResults&&validateNumber("options.maxResults",1,1e3,i.maxResults);let o=i||{},s=function(e,i,o,s,h){var l;let f={};i.isRoot?f.prefix="":f.prefix=i.path+"/",o&&o.length>0&&(f.delimiter=o),s&&(f.pageToken=s),h&&(f.maxResults=h);let d=i.bucketOnlyServerUrl(),g=makeUrl(d,e.host,e._protocol),b=e.maxOperationRetryTime,w=new RequestInfo(g,"GET",(l=i.bucket,function(i,o){let s=function(e,i,o){let s=jsonObjectOrNull(o);return null===s?null:function(e,i,o){let s={prefixes:[],items:[],nextPageToken:o.nextPageToken};if(o[j])for(let h of o[j]){let o=h.replace(/\/$/,""),l=e._makeStorageReference(new Location(i,o));s.prefixes.push(l)}if(o[$])for(let h of o[$]){let o=e._makeStorageReference(new Location(i,h.name));s.items.push(o)}return s}(e,i,s)}(e,l,o);return handlerCheck(null!==s),s}),b);return w.urlParams=f,w.errorHandler=sharedErrorHandler(i),w}(e.storage,e._location,"/",o.pageToken,o.maxResults);return e.storage.makeRequestWithTokens(s,newTextConnection)}(e,{pageToken:o});i.prefixes.push(...s.prefixes),i.items.push(...s.items),null!=s.nextPageToken&&await listAllHelper(e,i,s.nextPageToken)}function extractBucket(e,i){let o=i?.[_];return null==o?null:Location.makeFromBucketSpec(o,e)}let FirebaseStorageImpl=class FirebaseStorageImpl{constructor(e,i,o,s,h,l=!1){this.app=e,this._authProvider=i,this._appCheckProvider=o,this._url=s,this._firebaseVersion=h,this._isUsingEmulator=l,this._bucket=null,this._host=w,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=12e4,this._maxUploadRetryTime=6e5,this._requests=new Set,null!=s?this._bucket=Location.makeFromBucketSpec(s,this._host):this._bucket=extractBucket(this._host,this.app.options)}get host(){return this._host}set host(e){this._host=e,null!=this._url?this._bucket=Location.makeFromBucketSpec(this._url,e):this._bucket=extractBucket(e,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(e){validateNumber("time",0,Number.POSITIVE_INFINITY,e),this._maxUploadRetryTime=e}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(e){validateNumber("time",0,Number.POSITIVE_INFINITY,e),this._maxOperationRetryTime=e}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;let e=this._authProvider.getImmediate({optional:!0});if(e){let i=await e.getToken();if(null!==i)return i.accessToken}return null}async _getAppCheckToken(){if((0,d.rh)(this.app)&&this.app.settings.appCheckToken)return this.app.settings.appCheckToken;let e=this._appCheckProvider.getImmediate({optional:!0});if(e){let i=await e.getToken();return i.token}return null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(e=>e.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(e){return new Reference(this,e)}_makeRequest(e,i,o,s,h=!0){if(this._deleted)return new FailRequest(appDeleted());{let l=function(e,i,o,s,h,l,f=!0,d=!1){let g=makeQueryString(e.urlParams),b=e.url+g,w=Object.assign({},e.headers);return i&&(w["X-Firebase-GMPID"]=i),null!==o&&o.length>0&&(w.Authorization="Firebase "+o),w["X-Firebase-Storage-Version"]="webjs/"+(l??"AppManager"),null!==s&&(w["X-Firebase-AppCheck"]=s),new NetworkRequest(b,e.method,w,e.body,e.successCodes,e.additionalRetryCodes,e.handler,e.errorHandler,e.timeout,e.progressCallback,h,f,d)}(e,this._appId,o,s,i,this._firebaseVersion,h,this._isUsingEmulator);return this._requests.add(l),l.getPromise().then(()=>this._requests.delete(l),()=>this._requests.delete(l)),l}}async makeRequestWithTokens(e,i){let[o,s]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(e,i,o,s).getPromise()}};let tt="@firebase/storage",te="0.14.0",tr="storage";function uploadBytes(e,i,o){return function(e,i,o){e._throwIfRoot("uploadBytes");let s=function(e,i,o,s,h){let f=i.bucketOnlyServerUrl(),d={"X-Goog-Upload-Protocol":"multipart"},g=function(){let e="";for(let i=0;i<2;i++)e+=Math.random().toString().slice(2);return e}();d["Content-Type"]="multipart/related; boundary="+g;let b=function(e,i,o){let s=Object.assign({},o);return s.fullPath=e.path,s.size=i.size(),!s.contentType&&(s.contentType=i&&i.type()||"application/octet-stream"),s}(i,s,h),w=function(e,i){let o={},s=i.length;for(let h=0;h<s;h++){let s=i[h];s.writable&&(o[s.server]=e[s.local])}return JSON.stringify(o)}(b,o),_="--"+g+"\r\nContent-Type: application/json; charset=utf-8\r\n\r\n"+w+"\r\n--"+g+"\r\nContent-Type: "+b.contentType+"\r\n\r\n",k=FbsBlob.getBlob(_,s,"\r\n--"+g+"--");if(null===k)throw new StorageError(l.CANNOT_SLICE_BLOB,"Cannot slice blob for upload. Please retry the upload.");let O={name:b.fullPath},j=makeUrl(f,e.host,e._protocol),$=e.maxUploadRetryTime,tt=new RequestInfo(j,"POST",function(i,s){let h=fromResourceString(e,s,o);return handlerCheck(null!==h),h},$);return tt.urlParams=O,tt.headers=d,tt.body=k.uploadData(),tt.errorHandler=sharedErrorHandler(i),tt}(e.storage,e._location,getMappings(),new FbsBlob(i,!0),o);return e.storage.makeRequestWithTokens(s,newTextConnection).then(i=>({metadata:i,ref:e}))}(e=(0,g.m9)(e),i,o)}function listAll(e){return function(e){let i={prefixes:[],items:[]};return listAllHelper(e,i).then(()=>i)}(e=(0,g.m9)(e))}function getDownloadURL(e){return function(e){e._throwIfRoot("getDownloadURL");let i=function(e,i,o){let s=i.fullServerUrl(),h=makeUrl(s,e.host,e._protocol),l=e.maxOperationRetryTime,f=new RequestInfo(h,"GET",function(i,s){let h=fromResourceString(e,s,o);return handlerCheck(null!==h),function(e,i,o,s){let h=jsonObjectOrNull(i);if(null===h||!isString(h.downloadTokens))return null;let l=h.downloadTokens;if(0===l.length)return null;let f=encodeURIComponent,d=l.split(","),g=d.map(i=>{let h=e.bucket,l=e.fullPath,d="/b/"+f(h)+"/o/"+f(l),g=makeUrl(d,o,s),b=makeQueryString({alt:"media",token:i});return g+b});return g[0]}(h,s,e.host,e._protocol)},l);return f.errorHandler=objectErrorHandler(i),f}(e.storage,e._location,getMappings());return e.storage.makeRequestWithTokens(i,newTextConnection).then(e=>{if(null===e)throw new StorageError(l.NO_DOWNLOAD_URL,"The given file does not have any download URLs.");return e})}(e=(0,g.m9)(e))}function deleteObject(e){return function(e){e._throwIfRoot("deleteObject");let i=function(e,i){let o=i.fullServerUrl(),s=makeUrl(o,e.host,e._protocol),h=e.maxOperationRetryTime,l=new RequestInfo(s,"DELETE",function(e,i){},h);return l.successCodes=[200,204],l.errorHandler=objectErrorHandler(i),l}(e.storage,e._location);return e.storage.makeRequestWithTokens(i,newTextConnection)}(e=(0,g.m9)(e))}function ref(e,i){return function(e,i){if(!(i&&/^[A-Za-z]+:\/\//.test(i)))return function refFromPath(e,i){if(e instanceof FirebaseStorageImpl){if(null==e._bucket)throw new StorageError(l.NO_DEFAULT_BUCKET,"No default bucket found. Did you set the '"+_+"' property when initializing the app?");let o=new Reference(e,e._bucket);return null!=i?refFromPath(o,i):o}return void 0!==i?function(e,i){let o=function(e,i){let o=i.split("/").filter(e=>e.length>0).join("/");return 0===e.length?o:e+"/"+o}(e._location.path,i),s=new Location(e._location.bucket,o);return new Reference(e.storage,s)}(e,i):e}(e,i);if(e instanceof FirebaseStorageImpl)return new Reference(e,i);throw invalidArgument("To use ref(service, url), the first argument must be a Storage instance.")}(e=(0,g.m9)(e),i)}function getStorage(e=(0,d.Mq)(),i){e=(0,g.m9)(e);let o=(0,d.qX)(e,tr),s=o.getImmediate({identifier:i}),h=(0,g.P0)("storage");return h&&function(e,i,o,s={}){!function(e,i,o,s={}){e.host=`${i}:${o}`;let h=(0,g.Xx)(i);h&&((0,g.Uo)(`https://${e.host}/b`),(0,g.dp)("Storage",!0)),e._isUsingEmulator=!0,e._protocol=h?"https":"http";let{mockUserToken:l}=s;l&&(e._overrideAuthToken="string"==typeof l?l:(0,g.Sg)(l,e.app.options.projectId))}(e,i,o,s)}(s,...h),s}(0,d.Xd)(new b.wA(tr,function(e,{instanceIdentifier:i}){let o=e.getProvider("app").getImmediate(),s=e.getProvider("auth-internal"),h=e.getProvider("app-check-internal");return new FirebaseStorageImpl(o,s,h,i,d.Jn)},"PUBLIC").setMultipleInstances(!0)),(0,d.KN)(tt,te,""),(0,d.KN)(tt,te,"esm2020")}}]);