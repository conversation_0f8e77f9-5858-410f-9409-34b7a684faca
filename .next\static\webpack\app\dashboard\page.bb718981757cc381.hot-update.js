"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/layers.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Layers; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z\",\n      key: \"zw3jo\"\n    }\n  ],\n  [\n    \"path\",\n    {\n      d: \"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12\",\n      key: \"1wduqc\"\n    }\n  ],\n  [\n    \"path\",\n    {\n      d: \"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17\",\n      key: \"kqbvx6\"\n    }\n  ]\n];\nconst Layers = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"layers\", __iconNode);\n\n\n//# sourceMappingURL=layers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbGF5ZXJzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXREO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGdFQUFnQjs7QUFFVTtBQUN6QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2xheWVycy5qcz84ZDhhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgbHVjaWRlLXJlYWN0IHYwLjUyNS4wIC0gSVNDXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgSVNDIGxpY2Vuc2UuXG4gKiBTZWUgdGhlIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqL1xuXG5pbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uLmpzJztcblxuY29uc3QgX19pY29uTm9kZSA9IFtcbiAgW1xuICAgIFwicGF0aFwiLFxuICAgIHtcbiAgICAgIGQ6IFwiTTEyLjgzIDIuMThhMiAyIDAgMCAwLTEuNjYgMEwyLjYgNi4wOGExIDEgMCAwIDAgMCAxLjgzbDguNTggMy45MWEyIDIgMCAwIDAgMS42NiAwbDguNTgtMy45YTEgMSAwIDAgMCAwLTEuODN6XCIsXG4gICAgICBrZXk6IFwienczam9cIlxuICAgIH1cbiAgXSxcbiAgW1xuICAgIFwicGF0aFwiLFxuICAgIHtcbiAgICAgIGQ6IFwiTTIgMTJhMSAxIDAgMCAwIC41OC45MWw4LjYgMy45MWEyIDIgMCAwIDAgMS42NSAwbDguNTgtMy45QTEgMSAwIDAgMCAyMiAxMlwiLFxuICAgICAga2V5OiBcIjF3ZHVxY1wiXG4gICAgfVxuICBdLFxuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNMiAxN2ExIDEgMCAwIDAgLjU4LjkxbDguNiAzLjkxYTIgMiAwIDAgMCAxLjY1IDBsOC41OC0zLjlBMSAxIDAgMCAwIDIyIDE3XCIsXG4gICAgICBrZXk6IFwia3Fidng2XCJcbiAgICB9XG4gIF1cbl07XG5jb25zdCBMYXllcnMgPSBjcmVhdGVMdWNpZGVJY29uKFwibGF5ZXJzXCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBMYXllcnMgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bGF5ZXJzLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js":
/*!****************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/refresh-cw.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ RefreshCw; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\"path\", { d: \"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8\", key: \"v9h5vc\" }],\n  [\"path\", { d: \"M21 3v5h-5\", key: \"1q7to0\" }],\n  [\"path\", { d: \"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16\", key: \"3uifl3\" }],\n  [\"path\", { d: \"M8 16H3v5\", key: \"1cv678\" }]\n];\nconst RefreshCw = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"refresh-cw\", __iconNode);\n\n\n//# sourceMappingURL=refresh-cw.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvcmVmcmVzaC1jdy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RDtBQUNBLGFBQWEsd0VBQXdFO0FBQ3JGLGFBQWEsZ0NBQWdDO0FBQzdDLGFBQWEseUVBQXlFO0FBQ3RGLGFBQWEsK0JBQStCO0FBQzVDO0FBQ0Esa0JBQWtCLGdFQUFnQjs7QUFFVTtBQUM1QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3JlZnJlc2gtY3cuanM/ZjQzZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC41MjUuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IF9faWNvbk5vZGUgPSBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0zIDEyYTkgOSAwIDAgMSA5LTkgOS43NSA5Ljc1IDAgMCAxIDYuNzQgMi43NEwyMSA4XCIsIGtleTogXCJ2OWg1dmNcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTIxIDN2NWgtNVwiLCBrZXk6IFwiMXE3dG8wXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0yMSAxMmE5IDkgMCAwIDEtOSA5IDkuNzUgOS43NSAwIDAgMS02Ljc0LTIuNzRMMyAxNlwiLCBrZXk6IFwiM3VpZmwzXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk04IDE2SDN2NVwiLCBrZXk6IFwiMWN2Njc4XCIgfV1cbl07XG5jb25zdCBSZWZyZXNoQ3cgPSBjcmVhdGVMdWNpZGVJY29uKFwicmVmcmVzaC1jd1wiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgUmVmcmVzaEN3IGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXJlZnJlc2gtY3cuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/sparkles.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Sparkles; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z\",\n      key: \"4pj2yx\"\n    }\n  ],\n  [\"path\", { d: \"M20 3v4\", key: \"1olli1\" }],\n  [\"path\", { d: \"M22 5h-4\", key: \"1gvqau\" }],\n  [\"path\", { d: \"M4 17v2\", key: \"vumght\" }],\n  [\"path\", { d: \"M5 18H3\", key: \"zchphs\" }]\n];\nconst Sparkles = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"sparkles\", __iconNode);\n\n\n//# sourceMappingURL=sparkles.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc3BhcmtsZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsOEJBQThCO0FBQzNDLGFBQWEsNkJBQTZCO0FBQzFDLGFBQWEsNkJBQTZCO0FBQzFDO0FBQ0EsaUJBQWlCLGdFQUFnQjs7QUFFVTtBQUMzQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3NwYXJrbGVzLmpzPzk5NzIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNTI1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNOS45MzcgMTUuNUEyIDIgMCAwIDAgOC41IDE0LjA2M2wtNi4xMzUtMS41ODJhLjUuNSAwIDAgMSAwLS45NjJMOC41IDkuOTM2QTIgMiAwIDAgMCA5LjkzNyA4LjVsMS41ODItNi4xMzVhLjUuNSAwIDAgMSAuOTYzIDBMMTQuMDYzIDguNUEyIDIgMCAwIDAgMTUuNSA5LjkzN2w2LjEzNSAxLjU4MWEuNS41IDAgMCAxIDAgLjk2NEwxNS41IDE0LjA2M2EyIDIgMCAwIDAtMS40MzcgMS40MzdsLTEuNTgyIDYuMTM1YS41LjUgMCAwIDEtLjk2MyAwelwiLFxuICAgICAga2V5OiBcIjRwajJ5eFwiXG4gICAgfVxuICBdLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMjAgM3Y0XCIsIGtleTogXCIxb2xsaTFcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTIyIDVoLTRcIiwga2V5OiBcIjFndnFhdVwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNNCAxN3YyXCIsIGtleTogXCJ2dW1naHRcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTUgMThIM1wiLCBrZXk6IFwiemNocGhzXCIgfV1cbl07XG5jb25zdCBTcGFya2xlcyA9IGNyZWF0ZUx1Y2lkZUljb24oXCJzcGFya2xlc1wiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgU3BhcmtsZXMgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3BhcmtsZXMuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/star.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Star; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst __iconNode = [\n  [\n    \"path\",\n    {\n      d: \"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z\",\n      key: \"r04s7s\"\n    }\n  ]\n];\nconst Star = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"star\", __iconNode);\n\n\n//# sourceMappingURL=star.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc3Rhci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGdFQUFnQjs7QUFFVTtBQUN2QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3N0YXIuanM/MTRjZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC41MjUuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IF9faWNvbk5vZGUgPSBbXG4gIFtcbiAgICBcInBhdGhcIixcbiAgICB7XG4gICAgICBkOiBcIk0xMS41MjUgMi4yOTVhLjUzLjUzIDAgMCAxIC45NSAwbDIuMzEgNC42NzlhMi4xMjMgMi4xMjMgMCAwIDAgMS41OTUgMS4xNmw1LjE2Ni43NTZhLjUzLjUzIDAgMCAxIC4yOTQuOTA0bC0zLjczNiAzLjYzOGEyLjEyMyAyLjEyMyAwIDAgMC0uNjExIDEuODc4bC44ODIgNS4xNGEuNTMuNTMgMCAwIDEtLjc3MS41NmwtNC42MTgtMi40MjhhMi4xMjIgMi4xMjIgMCAwIDAtMS45NzMgMEw2LjM5NiAyMS4wMWEuNTMuNTMgMCAwIDEtLjc3LS41NmwuODgxLTUuMTM5YTIuMTIyIDIuMTIyIDAgMCAwLS42MTEtMS44NzlMMi4xNiA5Ljc5NWEuNTMuNTMgMCAwIDEgLjI5NC0uOTA2bDUuMTY1LS43NTVhMi4xMjIgMi4xMjIgMCAwIDAgMS41OTctMS4xNnpcIixcbiAgICAgIGtleTogXCJyMDRzN3NcIlxuICAgIH1cbiAgXVxuXTtcbmNvbnN0IFN0YXIgPSBjcmVhdGVMdWNpZGVJY29uKFwic3RhclwiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgU3RhciBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdGFyLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Layers,RefreshCw,Sparkles,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Layers,RefreshCw,Sparkles,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Layers,RefreshCw,Sparkles,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Layers,RefreshCw,Sparkles,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Layers,RefreshCw,Sparkles,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Layers,RefreshCw,Sparkles,Star,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Pequenas constantes de UX\nconst CACHE_DURATION = 5 * 60 * 1000;\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000;\nconst MODELS_PER_PAGE = 12;\nconst fadeIn = {\n    opacity: 1,\n    y: 0\n};\nconst fadeOut = {\n    opacity: 0,\n    y: 8\n};\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(MODELS_PER_PAGE);\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true\n    });\n    // --------------------- LÓGICA DE DADOS (mantida) ---------------------\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    setSelectedEndpoint(openRouterEndpoint || endpointsCache.endpoints[0]);\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        user,\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n            } else if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                if (selectedEndpoint.name === \"OpenRouter\") loadOpenRouterModels();\n                else if (selectedEndpoint.name === \"DeepSeek\") loadDeepSeekModels();\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        const q = query(collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\"), where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (!querySnapshot.empty) return querySnapshot.docs[0].data().username || querySnapshot.docs[0].id;\n        return \"unknown\";\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            setSelectedEndpoint(openRouterEndpoint || userEndpoints[0] || null);\n        } catch (error) {\n            setError(\"Erro ao carregar endpoints.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            setError(\"Erro ao carregar modelos OpenRouter.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            setError(\"Erro ao carregar modelos DeepSeek.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\") {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultIds = new Set(searchResults.map((result)=>result.model.id));\n            filtered = filtered.filter((model)=>searchResultIds.has(model.id));\n        } else if (selectedCategory) {\n            filtered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n        }\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint || favoriteToggling.has(model.id)) return;\n        setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n        const optimisticNewStatus = !model.isFavorite;\n        const updateUI = (status)=>{\n            setFavoriteModelIds((prev)=>{\n                const newSet = new Set(prev);\n                if (status) newSet.add(model.id);\n                else newSet.delete(model.id);\n                return newSet;\n            });\n            setModels((prev)=>prev.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: status\n                    } : m));\n        };\n        updateUI(optimisticNewStatus);\n        try {\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            if (actualNewStatus !== optimisticNewStatus) updateUI(actualNewStatus);\n        } catch (error) {\n            updateUI(!optimisticNewStatus);\n        } finally{\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        return _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002;\n    };\n    const handleSelectModel = (model)=>{\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleLoadMoreModels = ()=>setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    const handleRefreshModels = ()=>{\n        if (!selectedEndpoint) return;\n        const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n        setModelsCache((prev)=>{\n            const newCache = new Map(prev);\n            newCache.delete(cacheKey);\n            return newCache;\n        });\n        if (selectedEndpoint.name === \"OpenRouter\") loadOpenRouterModels();\n        else if (selectedEndpoint.name === \"DeepSeek\") loadDeepSeekModels();\n    };\n    // -------------------------------------------------------------------\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            className: \"fixed inset-0 z-50 flex items-center justify-center p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    className: \"absolute inset-0 bg-gradient-to-br from-black/60 via-black/50 to-transparent backdrop-blur-sm\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                    initial: {\n                        y: 20,\n                        opacity: 0,\n                        scale: 0.98\n                    },\n                    animate: {\n                        y: 0,\n                        opacity: 1,\n                        scale: 1\n                    },\n                    exit: {\n                        y: 12,\n                        opacity: 0,\n                        scale: 0.98\n                    },\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 300,\n                        damping: 30\n                    },\n                    className: \"relative w-full max-w-[1100px] h-[86vh] bg-gradient-to-br from-blue-950/90 to-slate-900/90 rounded-3xl shadow-[0_20px_60px_rgba(2,6,23,0.8)] border border-white/5 overflow-hidden flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 pointer-events-none bg-[linear-gradient(135deg,#0ea5a4_0%,#0284c7_40%,rgba(15,41,66,0.25)_100%)] mix-blend-overlay opacity-10\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"z-20 flex items-center justify-between px-6 py-4 border-b border-white/6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-11 h-11 rounded-lg bg-gradient-to-tr from-cyan-500 to-blue-600 flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-5 h-5 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Escolha um modelo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-slate-300/80\",\n                                                            children: \"R\\xe1pido — bonito — confi\\xe1vel\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden md:flex items-center gap-2 bg-white/3 rounded-full px-2 py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category: \"paid\"\n                                                            })),\n                                                    className: \"px-3 py-1 rounded-full text-sm transition \".concat(filters.category === \"paid\" ? \"bg-gradient-to-r from-blue-600 to-cyan-500 text-white shadow\" : \"text-slate-200/70 hover:bg-white/5\"),\n                                                    children: \"Pagos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category: \"free\"\n                                                            })),\n                                                    className: \"px-3 py-1 rounded-full text-sm transition \".concat(filters.category === \"free\" ? \"bg-gradient-to-r from-green-600 to-emerald-500 text-white shadow\" : \"text-slate-200/70 hover:bg-white/5\"),\n                                                    children: \"Gr\\xe1tis\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category: \"favorites\"\n                                                            })),\n                                                    className: \"px-3 py-1 rounded-full text-sm transition \".concat(filters.category === \"favorites\" ? \"bg-gradient-to-r from-amber-400 to-amber-300 text-white shadow\" : \"text-slate-200/70 hover:bg-white/5\"),\n                                                    children: \"Favoritos\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden sm:block w-[360px]\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                value: searchTerm,\n                                                onChange: setSearchTerm,\n                                                suggestions: suggestions,\n                                                isSearching: isSearching,\n                                                placeholder: \"Buscar por modelo, provedor ou tag...\",\n                                                showSuggestions: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 58\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            title: \"Atualizar\",\n                                            onClick: handleRefreshModels,\n                                            className: \"p-2 rounded-lg hover:bg-white/5 transition text-slate-200/80\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-5 h-5 \".concat(loading ? \"animate-spin\" : \"\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"p-2 rounded-lg bg-white/5 hover:bg-white/8 transition\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-5 h-5 text-slate-100\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 overflow-hidden\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                                    className: \"w-64 bg-gradient-to-b from-transparent to-black/20 p-5 border-r border-white/6 flex flex-col gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-xs text-slate-300 font-medium\",\n                                                    children: \"Endpoint\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                                    onChange: (e)=>{\n                                                        const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                                        setSelectedEndpoint(endpoint || null);\n                                                    },\n                                                    className: \"mt-2 w-full bg-transparent border border-white/6 rounded-lg px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-cyan-400\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Selecione\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: endpoint.id,\n                                                                className: \"bg-slate-900 text-slate-100\",\n                                                                children: endpoint.name\n                                                            }, endpoint.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 21\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"text-sm text-slate-200 font-semibold mb-3\",\n                                                    children: \"Categorias inteligentes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-2\",\n                                                    children: [\n                                                        \"paid\",\n                                                        \"free\",\n                                                        \"favorites\"\n                                                    ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setFilters((prev)=>({\n                                                                        ...prev,\n                                                                        category\n                                                                    })),\n                                                            className: \"text-sm text-left px-3 py-2 rounded-lg transition \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-500 text-white shadow\" : \"text-slate-300/80 hover:bg-white/5\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 rounded-lg flex items-center justify-center bg-white/4\",\n                                                                        children: category === \"paid\" ? \"\\uD83D\\uDCB0\" : category === \"free\" ? \"\\uD83C\\uDF81\" : \"⭐\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm font-medium capitalize\",\n                                                                                children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 337,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-xs text-slate-400\",\n                                                                                children: \"Filtros r\\xe1pidos\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 338,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                        lineNumber: 336,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 334,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        }, category, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-auto\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-slate-400 mb-2\",\n                                                    children: \"Inserir modelo customizado\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            value: customModelId,\n                                                            onChange: (e)=>setCustomModelId(e.target.value),\n                                                            placeholder: \"provedor/nome-do-modelo\",\n                                                            className: \"flex-1 px-3 py-2 rounded-lg bg-white/3 text-white text-sm focus:outline-none\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleUseCustomModel,\n                                                            className: \"px-3 py-2 bg-gradient-to-r from-blue-600 to-cyan-500 rounded-lg text-sm font-medium\",\n                                                            children: \"Usar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleRefreshModels,\n                                                            disabled: loading || !selectedEndpoint,\n                                                            className: \"flex-1 px-3 py-2 rounded-lg text-sm bg-white/5\",\n                                                            children: \"Atualizar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>{\n                                                                setFilters((prev)=>({\n                                                                        ...prev,\n                                                                        sortBy: \"newest\"\n                                                                    }));\n                                                            },\n                                                            className: \"px-3 py-2 rounded-lg text-sm bg-white/5\",\n                                                            children: \"Limpar\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"flex-1 p-6 overflow-y-auto\",\n                                    children: [\n                                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: Array.from({\n                                                length: 6\n                                            }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1\n                                                    },\n                                                    transition: {\n                                                        delay: i * 0.04\n                                                    },\n                                                    className: \"h-40 rounded-2xl bg-gradient-to-br from-white/3 to-white/2 border border-white/6 animate-pulse\"\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !loading && error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 rounded-2xl bg-red-900/40 text-red-200\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-8 text-center text-slate-300\",\n                                            children: \"Nenhum modelo encontrado — tente outro filtro ou termo de busca.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !loading && !error && filteredModels.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: filteredModels.slice(0, displayedModelsCount).map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                    model: model,\n                                                    isSelected: currentModel === model.id,\n                                                    onSelect: ()=>handleSelectModel(model),\n                                                    onToggleFavorite: ()=>handleToggleFavorite(model),\n                                                    isToggling: favoriteToggling.has(model.id),\n                                                    service: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                    searchTerm: searchTerm\n                                                }, model.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 flex justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleLoadMoreModels,\n                                                className: \"px-6 py-3 rounded-full bg-gradient-to-r from-blue-600 to-cyan-500 text-white\",\n                                                children: \"Carregar mais\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 389,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 308,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-6 py-4 border-t border-white/6 flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-slate-300\",\n                                    children: [\n                                        filteredModels.length,\n                                        \" modelos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"px-4 py-2 rounded-lg bg-white/5\",\n                                            children: \"Fechar\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-4 py-2 rounded-lg bg-gradient-to-r from-blue-600 to-cyan-500 text-white\",\n                                            children: \"Ajuda\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 400,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            isOpen: showExpensiveModelModal,\n                            model: pendingExpensiveModel,\n                            onConfirm: handleConfirmExpensiveModel,\n                            onCancel: ()=>setShowExpensiveModelModal(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n            lineNumber: 248,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"/bHmu8QxlPh63CHLKyEkFuO7EiY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service, searchTerm = \"\" } = param;\n    var _model_pricing, _model_pricing1;\n    const isExpensive = service.getTotalPrice ? service.getTotalPrice(model) > 0.00002 : false;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n        whileHover: {\n            scale: 1.02\n        },\n        initial: \"initial\",\n        animate: \"animate\",\n        exit: \"exit\",\n        className: \"relative p-5 rounded-2xl border border-white/6 bg-gradient-to-br \".concat(isSelected ? \"from-blue-800 to-cyan-800\" : \"from-white/3 to-white/2\", \" shadow-[0_10px_30px_rgba(2,6,23,0.6)\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-3 -left-3 bg-gradient-to-r from-indigo-500 to-violet-500 text-xs px-3 py-1 rounded-full text-white font-semibold flex items-center gap-2 shadow-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, undefined),\n                    model.provider || \"provider\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 428,\n                columnNumber: 7\n            }, undefined),\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-3 -right-3 rounded-full bg-amber-500 p-1 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4 text-white\",\n                    viewBox: \"0 0 24 24\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 8c-1.657 0-3 1.343-3 3v3h6v-3c0-1.657-1.343-3-3-3z\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 101\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 434,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-white font-semibold truncate text-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                    text: model.name,\n                                    highlight: searchTerm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 69\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-slate-300/80 font-mono truncate mt-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                    text: model.id,\n                                    highlight: searchTerm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 76\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-300/80 mt-3 line-clamp-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                    text: model.description,\n                                    highlight: searchTerm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 92\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 grid grid-cols-3 gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/3 rounded-lg p-3 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: service.formatContextLength ? service.formatContextLength(model.context_length) : model.context_length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/3 rounded-lg p-3 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: [\n                                                    service.formatPrice ? service.formatPrice(model.pricing.prompt) : (_model_pricing = model.pricing) === null || _model_pricing === void 0 ? void 0 : _model_pricing.prompt,\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/3 rounded-lg p-3 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-slate-300\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold text-white\",\n                                                children: [\n                                                    service.formatPrice ? service.formatPrice(model.pricing.completion) : (_model_pricing1 = model.pricing) === null || _model_pricing1 === void 0 ? void 0 : _model_pricing1.completion,\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-36 flex flex-col items-end gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"w-full px-4 py-2 rounded-lg text-sm font-medium transition \".concat(isSelected ? \"bg-white text-slate-900\" : \"bg-gradient-to-br from-blue-600 to-cyan-500 text-white hover:from-blue-500 hover:to-cyan-400\"),\n                                children: isSelected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"w-4 h-4 inline-block mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 266\n                                        }, undefined),\n                                        \"Selecionado\"\n                                    ]\n                                }, void 0, true) : \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2 rounded-lg transition \".concat(model.isFavorite ? \"bg-yellow-400/20 text-yellow-300\" : \"bg-white/5 text-slate-200 hover:bg-white/7\"),\n                                title: model.isFavorite ? \"Remover favorito\" : \"Adicionar favorito\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 27\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Layers_RefreshCw_Sparkles_Star_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 120\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 439,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 427,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\nconst DeepSeekModelCard = (param)=>/*#__PURE__*/ {\n    let { model, isSelected, onSelect } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n        whileHover: {\n            scale: 1.01\n        },\n        className: \"p-6 rounded-2xl border border-white/6 bg-gradient-to-br from-white/4 to-white/2\",\n        onClick: onSelect,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-white\",\n                                children: model.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-slate-300 mt-2\",\n                                children: model.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 476,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-right\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-300\",\n                                children: \"Contexto\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-semibold text-white\",\n                                children: _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatContextLength(model.context_length)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 475,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 grid grid-cols-3 gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/3 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-300/70 mb-1\",\n                                children: \"Input\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 62\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-semibold text-white\",\n                                children: [\n                                    _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.prompt),\n                                    \"/1M\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 486,\n                                columnNumber: 121\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 486,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/3 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-slate-300/70 mb-1\",\n                                children: \"Output\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 62\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm font-semibold text-white\",\n                                children: [\n                                    _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.completion),\n                                    \"/1M\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 487,\n                                columnNumber: 122\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-3 mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onSelect,\n                            className: \"w-full py-3 rounded-lg \".concat(isSelected ? \"bg-gradient-to-r from-blue-600 to-cyan-500 text-white\" : \"bg-white/5 text-white\"),\n                            children: isSelected ? \"Selecionado\" : \"Selecionar Modelo\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 40\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 485,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 474,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = DeepSeekModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n$RefreshReg$(_c2, \"DeepSeekModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2Rhc2hib2FyZC9Nb2RlbFNlbGVjdGlvbk1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDSztBQUMyQjtBQUNsQztBQUNiO0FBQ21EO0FBQ2xCO0FBQ0o7QUFDWTtBQUVmO0FBQ2dDO0FBQ047QUFDUjtBQVNoRiw0QkFBNEI7QUFDNUIsTUFBTXNCLGlCQUFpQixJQUFJLEtBQUs7QUFDaEMsTUFBTUMsMkJBQTJCLEtBQUssS0FBSztBQUMzQyxNQUFNQyxrQkFBa0I7QUFFeEIsTUFBTUMsU0FBUztJQUFFQyxTQUFTO0lBQUdDLEdBQUc7QUFBRTtBQUNsQyxNQUFNQyxVQUFVO0lBQUVGLFNBQVM7SUFBR0MsR0FBRztBQUFFO0FBRW5DLE1BQU1FLHNCQUEwRDtRQUFDLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxZQUFZLEVBQUVDLGFBQWEsRUFBRTs7SUFDL0csTUFBTSxFQUFFQyxJQUFJLEVBQUUsR0FBR3ZCLDhEQUFPQTtJQUN4QixNQUFNLENBQUN3QixXQUFXQyxhQUFhLEdBQUdsQywrQ0FBUUEsQ0FBZ0IsRUFBRTtJQUM1RCxNQUFNLENBQUNtQyxrQkFBa0JDLG9CQUFvQixHQUFHcEMsK0NBQVFBLENBQXFCO0lBQzdFLE1BQU0sQ0FBQ3FDLFFBQVFDLFVBQVUsR0FBR3RDLCtDQUFRQSxDQUFZLEVBQUU7SUFDbEQsTUFBTSxDQUFDdUMsa0JBQWtCQyxvQkFBb0IsR0FBR3hDLCtDQUFRQSxDQUFjLElBQUl5QztJQUMxRSxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBRzNDLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzRDLE9BQU9DLFNBQVMsR0FBRzdDLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUM4QyxzQkFBc0JDLHdCQUF3QixHQUFHL0MsK0NBQVFBLENBQUNzQjtJQUNqRSxNQUFNLENBQUMwQixlQUFlQyxpQkFBaUIsR0FBR2pELCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQ2tELHlCQUF5QkMsMkJBQTJCLEdBQUduRCwrQ0FBUUEsQ0FBQztJQUN2RSxNQUFNLENBQUNvRCx1QkFBdUJDLHlCQUF5QixHQUFHckQsK0NBQVFBLENBQWlCO0lBQ25GLE1BQU0sQ0FBQ3NELGtCQUFrQkMsb0JBQW9CLEdBQUd2RCwrQ0FBUUEsQ0FBZ0I7SUFDeEUsTUFBTSxDQUFDd0QsaUJBQWlCQyxtQkFBbUIsR0FBR3pELCtDQUFRQSxDQUFrQixFQUFFO0lBQzFFLE1BQU0sQ0FBQzBELGtCQUFrQkMsb0JBQW9CLEdBQUczRCwrQ0FBUUEsQ0FBYyxJQUFJeUM7SUFDMUUsTUFBTSxDQUFDbUIsb0JBQW9CQyxzQkFBc0IsR0FBRzdELCtDQUFRQSxDQUFnQjtJQUM1RSxNQUFNLENBQUM4RCxhQUFhQyxlQUFlLEdBQUcvRCwrQ0FBUUEsQ0FBd0QsSUFBSWdFO0lBQzFHLE1BQU0sQ0FBQ0MsZ0JBQWdCQyxrQkFBa0IsR0FBR2xFLCtDQUFRQSxDQUF5RDtJQUU3RyxNQUFNLENBQUNtRSxTQUFTQyxXQUFXLEdBQUdwRSwrQ0FBUUEsQ0FBZTtRQUNuRHFFLFVBQVU7UUFDVkMsUUFBUTtRQUNSQyxZQUFZO0lBQ2Q7SUFFQSxNQUFNLEVBQ0pBLFVBQVUsRUFDVkMsYUFBYSxFQUNiQyxhQUFhLEVBQ2JDLFdBQVcsRUFDWEMsV0FBVyxFQUNYQyxXQUFXLEVBQ1hDLFdBQVcsRUFDWixHQUFHOUQsMkVBQWlCQSxDQUFDc0IsUUFBUTtRQUFFeUMsWUFBWTtRQUFLQyxtQkFBbUI7UUFBT0MsY0FBYztRQUFNQyxnQkFBZ0I7UUFBS0MsWUFBWTtRQUFJQyxnQkFBZ0I7SUFBSztJQUV6Six3RUFBd0U7SUFDeEVwRixnREFBU0EsQ0FBQztRQUNSLElBQUlpQyxRQUFRSixRQUFRO1lBQ2xCLElBQUlxQyxrQkFBa0JtQixLQUFLQyxHQUFHLEtBQUtwQixlQUFlcUIsU0FBUyxHQUFHakUsMEJBQTBCO2dCQUN0RmEsYUFBYStCLGVBQWVoQyxTQUFTO2dCQUNyQyxJQUFJLENBQUNFLG9CQUFvQjhCLGVBQWVoQyxTQUFTLENBQUNzRCxNQUFNLEdBQUcsR0FBRztvQkFDNUQsTUFBTUMscUJBQXFCdkIsZUFBZWhDLFNBQVMsQ0FBQ3dELElBQUksQ0FBQ0MsQ0FBQUEsS0FBTUEsR0FBR0MsSUFBSSxLQUFLO29CQUMzRXZELG9CQUFvQm9ELHNCQUFzQnZCLGVBQWVoQyxTQUFTLENBQUMsRUFBRTtnQkFDdkU7WUFDRixPQUFPO2dCQUNMMkQ7WUFDRjtRQUNGO0lBQ0EsdURBQXVEO0lBQ3pELEdBQUc7UUFBQzVEO1FBQU1KO0tBQU87SUFFakI3QixnREFBU0EsQ0FBQztRQUNSLElBQUlvQyxrQkFBa0I7WUFDcEIsTUFBTTBELFdBQVcsR0FBMEIxRCxPQUF2QkEsaUJBQWlCMkQsRUFBRSxFQUFDLEtBQXlCLE9BQXRCM0QsaUJBQWlCd0QsSUFBSTtZQUNoRSxNQUFNSSxhQUFhakMsWUFBWWtDLEdBQUcsQ0FBQ0g7WUFDbkMsSUFBSUUsY0FBY1gsS0FBS0MsR0FBRyxLQUFLVSxXQUFXVCxTQUFTLEdBQUdsRSxnQkFBZ0I7Z0JBQ3BFa0IsVUFBVXlELFdBQVcxRCxNQUFNO2dCQUMzQndCLHNCQUFzQjFCLGlCQUFpQjJELEVBQUU7WUFDM0MsT0FBTyxJQUFJbEMsdUJBQXVCekIsaUJBQWlCMkQsRUFBRSxJQUFJLENBQUNDLFlBQVk7Z0JBQ3BFLElBQUk1RCxpQkFBaUJ3RCxJQUFJLEtBQUssY0FBY007cUJBQ3ZDLElBQUk5RCxpQkFBaUJ3RCxJQUFJLEtBQUssWUFBWU87WUFDakQ7UUFDRjtJQUNBLHVEQUF1RDtJQUN6RCxHQUFHO1FBQUMvRDtRQUFrQnlCO1FBQW9CRTtLQUFZO0lBRXRELE1BQU1xQywyQkFBMkI7UUFDL0IsSUFBSSxFQUFDbkUsaUJBQUFBLDJCQUFBQSxLQUFNb0UsS0FBSyxHQUFFLE9BQU87UUFDekIsTUFBTSxFQUFFQyxVQUFVLEVBQUVDLEtBQUssRUFBRUMsS0FBSyxFQUFFQyxPQUFPLEVBQUUsR0FBRyxNQUFNLDZMQUFPO1FBQzNELE1BQU1DLElBQUlILE1BQU1ELFdBQVczRiw2Q0FBRUEsRUFBRSxhQUFhNkYsTUFBTSxTQUFTLE1BQU12RSxLQUFLb0UsS0FBSztRQUMzRSxNQUFNTSxnQkFBZ0IsTUFBTUYsUUFBUUM7UUFDcEMsSUFBSSxDQUFDQyxjQUFjQyxLQUFLLEVBQUUsT0FBT0QsY0FBY0UsSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsSUFBSSxHQUFHQyxRQUFRLElBQUlKLGNBQWNFLElBQUksQ0FBQyxFQUFFLENBQUNkLEVBQUU7UUFDbEcsT0FBTztJQUNUO0lBRUEsTUFBTUYsZ0JBQWdCO1FBQ3BCLElBQUksQ0FBQzVELE1BQU07UUFDWFcsV0FBVztRQUNYRSxTQUFTO1FBQ1QsSUFBSTtZQUNGLE1BQU1pRSxXQUFXLE1BQU1YO1lBQ3ZCLE1BQU1ZLGdCQUFnQixNQUFNcEcsa0ZBQW1CQSxDQUFDbUc7WUFDaEQ1QyxrQkFBa0I7Z0JBQUVqQyxXQUFXOEU7Z0JBQWV6QixXQUFXRixLQUFLQyxHQUFHO1lBQUc7WUFDcEVuRCxhQUFhNkU7WUFDYixNQUFNdkIscUJBQXFCdUIsY0FBY3RCLElBQUksQ0FBQ0MsQ0FBQUEsS0FBTUEsR0FBR0MsSUFBSSxLQUFLO1lBQ2hFdkQsb0JBQW9Cb0Qsc0JBQXNCdUIsYUFBYSxDQUFDLEVBQUUsSUFBSTtRQUNoRSxFQUFFLE9BQU9uRSxPQUFPO1lBQ2RDLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXNELHVCQUF1QjtRQUMzQixJQUFJLENBQUM5RCxvQkFBb0IsQ0FBQ0gsTUFBTTtRQUNoQ1csV0FBVztRQUNYRSxTQUFTO1FBQ1QsSUFBSTtZQUNGLE1BQU1tRSxtQkFBbUIsTUFBTXBHLDhFQUFpQkEsQ0FBQ3FHLFdBQVc7WUFDNUQsTUFBTUgsV0FBVyxNQUFNWDtZQUN2QixNQUFNZSxjQUFjLE1BQU1wRyxzRkFBcUJBLENBQUNxRyxtQkFBbUIsQ0FBQ0wsVUFBVTNFLGlCQUFpQjJELEVBQUU7WUFDakcsTUFBTXNCLHNCQUFzQkosaUJBQWlCSyxHQUFHLENBQUNDLENBQUFBLFFBQVU7b0JBQUUsR0FBR0EsS0FBSztvQkFBRUMsWUFBWUwsWUFBWU0sR0FBRyxDQUFDRixNQUFNeEIsRUFBRTtnQkFBRTtZQUM3RyxNQUFNRCxXQUFXLEdBQTBCMUQsT0FBdkJBLGlCQUFpQjJELEVBQUUsRUFBQyxLQUF5QixPQUF0QjNELGlCQUFpQndELElBQUk7WUFDaEU1QixlQUFlMEQsQ0FBQUEsT0FBUSxJQUFJekQsSUFBSXlELE1BQU1DLEdBQUcsQ0FBQzdCLFVBQVU7b0JBQUV4RCxRQUFRK0U7b0JBQXFCOUIsV0FBV0YsS0FBS0MsR0FBRztnQkFBRztZQUN4Ry9DLFVBQVU4RTtZQUNWNUUsb0JBQW9CMEU7WUFDcEJyRCxzQkFBc0IxQixpQkFBaUIyRCxFQUFFO1FBQzNDLEVBQUUsT0FBT2xELE9BQU87WUFDZEMsU0FBUztRQUNYLFNBQVU7WUFDUkYsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNdUQscUJBQXFCO1FBQ3pCLElBQUksQ0FBQy9ELG9CQUFvQixDQUFDSCxNQUFNO1FBQ2hDVyxXQUFXO1FBQ1hFLFNBQVM7UUFDVCxJQUFJO1lBQ0YsTUFBTThFLGlCQUFpQixNQUFNOUcsMEVBQWVBLENBQUNvRyxXQUFXO1lBQ3hELE1BQU1wQixXQUFXLEdBQTBCMUQsT0FBdkJBLGlCQUFpQjJELEVBQUUsRUFBQyxLQUF5QixPQUF0QjNELGlCQUFpQndELElBQUk7WUFDaEU1QixlQUFlMEQsQ0FBQUEsT0FBUSxJQUFJekQsSUFBSXlELE1BQU1DLEdBQUcsQ0FBQzdCLFVBQVU7b0JBQUV4RCxRQUFRc0Y7b0JBQWdCckMsV0FBV0YsS0FBS0MsR0FBRztnQkFBRztZQUNuRy9DLFVBQVVxRjtZQUNWOUQsc0JBQXNCMUIsaUJBQWlCMkQsRUFBRTtRQUMzQyxFQUFFLE9BQU9sRCxPQUFPO1lBQ2RDLFNBQVM7UUFDWCxTQUFVO1lBQ1JGLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTWlGLG9CQUFvQjtRQUN4QixJQUFJQyxXQUFXO2VBQUl4RjtTQUFPO1FBQzFCLElBQUlGLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCd0QsSUFBSSxNQUFLLGNBQWM7WUFDM0NrQyxXQUFXakgsOEVBQWlCQSxDQUFDa0gsZ0JBQWdCLENBQUNELFVBQVUxRCxRQUFRRSxRQUFRO1FBQzFFO1FBQ0EsSUFBSU8sZUFBZUwsV0FBV3dELElBQUksSUFBSTtZQUNwQyxNQUFNQyxrQkFBa0IsSUFBSXZGLElBQUlnQyxjQUFjNEMsR0FBRyxDQUFDWSxDQUFBQSxTQUFVQSxPQUFPWCxLQUFLLENBQUN4QixFQUFFO1lBQzNFK0IsV0FBV0EsU0FBU0ssTUFBTSxDQUFDWixDQUFBQSxRQUFTVSxnQkFBZ0JSLEdBQUcsQ0FBQ0YsTUFBTXhCLEVBQUU7UUFDbEUsT0FBTyxJQUFJeEMsa0JBQWtCO1lBQzNCdUUsV0FBVzdHLHdGQUFzQkEsQ0FBQ21ILG1CQUFtQixDQUFDTixVQUFVdkU7UUFDbEU7UUFDQSxJQUFJLENBQUNzQixlQUFlLENBQUNMLFdBQVd3RCxJQUFJLElBQUk7WUFDdEMsTUFBTUssVUFBVWpHLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCd0QsSUFBSSxNQUFLLGFBQWE5RSwwRUFBZUEsR0FBR0QsOEVBQWlCQTtZQUMzRmlILFdBQVdPLFFBQVFDLFVBQVUsQ0FBQ1IsVUFBVTFELFFBQVFHLE1BQU07UUFDeEQ7UUFDQSxPQUFPdUQ7SUFDVDtJQUVBLE1BQU1TLGlCQUFpQlY7SUFFdkIsTUFBTVcsdUJBQXVCLE9BQU9qQjtRQUNsQyxJQUFJLENBQUN0RixRQUFRLENBQUNHLG9CQUFvQnVCLGlCQUFpQjhELEdBQUcsQ0FBQ0YsTUFBTXhCLEVBQUUsR0FBRztRQUNsRW5DLG9CQUFvQjhELENBQUFBLE9BQVEsSUFBSWhGLElBQUlnRixNQUFNZSxHQUFHLENBQUNsQixNQUFNeEIsRUFBRTtRQUN0RCxNQUFNMkMsc0JBQXNCLENBQUNuQixNQUFNQyxVQUFVO1FBQzdDLE1BQU1tQixXQUFXLENBQUNDO1lBQ2hCbkcsb0JBQW9CaUYsQ0FBQUE7Z0JBQ2xCLE1BQU1tQixTQUFTLElBQUluRyxJQUFJZ0Y7Z0JBQ3ZCLElBQUlrQixRQUFRQyxPQUFPSixHQUFHLENBQUNsQixNQUFNeEIsRUFBRTtxQkFBUThDLE9BQU9DLE1BQU0sQ0FBQ3ZCLE1BQU14QixFQUFFO2dCQUM3RCxPQUFPOEM7WUFDVDtZQUNBdEcsVUFBVW1GLENBQUFBLE9BQVFBLEtBQUtKLEdBQUcsQ0FBQ3lCLENBQUFBLElBQUtBLEVBQUVoRCxFQUFFLEtBQUt3QixNQUFNeEIsRUFBRSxHQUFHO3dCQUFFLEdBQUdnRCxDQUFDO3dCQUFFdkIsWUFBWW9CO29CQUFPLElBQUlHO1FBQ3JGO1FBQ0FKLFNBQVNEO1FBQ1QsSUFBSTtZQUNGLE1BQU0zQixXQUFXLE1BQU1YO1lBQ3ZCLE1BQU00QyxrQkFBa0IsTUFBTWpJLHNGQUFxQkEsQ0FBQ2tJLGNBQWMsQ0FBQ2xDLFVBQVUzRSxpQkFBaUIyRCxFQUFFLEVBQUV3QixNQUFNeEIsRUFBRSxFQUFFd0IsTUFBTTNCLElBQUk7WUFDdEgsSUFBSW9ELG9CQUFvQk4scUJBQXFCQyxTQUFTSztRQUN4RCxFQUFFLE9BQU9uRyxPQUFPO1lBQ2Q4RixTQUFTLENBQUNEO1FBQ1osU0FBVTtZQUNSOUUsb0JBQW9COEQsQ0FBQUE7Z0JBQVUsTUFBTW1CLFNBQVMsSUFBSW5HLElBQUlnRjtnQkFBT21CLE9BQU9DLE1BQU0sQ0FBQ3ZCLE1BQU14QixFQUFFO2dCQUFHLE9BQU84QztZQUFRO1FBQ3RHO0lBQ0Y7SUFFQSxNQUFNSyxtQkFBbUIsQ0FBQzNCO1FBQ3hCLElBQUluRixDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQndELElBQUksTUFBSyxjQUFjLE9BQU87UUFDcEQsT0FBTy9FLDhFQUFpQkEsQ0FBQ3NJLGFBQWEsQ0FBQzVCLFNBQVM7SUFDbEQ7SUFFQSxNQUFNNkIsb0JBQW9CLENBQUM3QjtRQUN6QixJQUFJMkIsaUJBQWlCM0IsUUFBUTtZQUMzQmpFLHlCQUF5QmlFO1lBQ3pCbkUsMkJBQTJCO1FBQzdCLE9BQU87WUFDTHBCLGNBQWN1RixNQUFNeEIsRUFBRTtZQUN0QmpFO1FBQ0Y7SUFDRjtJQUVBLE1BQU11SCw4QkFBOEI7UUFDbEMsSUFBSWhHLHVCQUF1QjtZQUN6QnJCLGNBQWNxQixzQkFBc0IwQyxFQUFFO1lBQ3RDM0MsMkJBQTJCO1lBQzNCRSx5QkFBeUI7WUFDekJ4QjtRQUNGO0lBQ0Y7SUFFQSxNQUFNd0gsdUJBQXVCLElBQU10Ryx3QkFBd0IwRSxDQUFBQSxPQUFRQSxPQUFPbkc7SUFDMUUsTUFBTWdJLHVCQUF1QjtRQUFRLElBQUl0RyxjQUFjK0UsSUFBSSxJQUFJO1lBQUVoRyxjQUFjaUIsY0FBYytFLElBQUk7WUFBS2xHO1FBQVc7SUFBRTtJQUNuSCxNQUFNMEgsc0JBQXNCO1FBQzFCLElBQUksQ0FBQ3BILGtCQUFrQjtRQUN2QixNQUFNMEQsV0FBVyxHQUEwQjFELE9BQXZCQSxpQkFBaUIyRCxFQUFFLEVBQUMsS0FBeUIsT0FBdEIzRCxpQkFBaUJ3RCxJQUFJO1FBQ2hFNUIsZUFBZTBELENBQUFBO1lBQVUsTUFBTStCLFdBQVcsSUFBSXhGLElBQUl5RDtZQUFPK0IsU0FBU1gsTUFBTSxDQUFDaEQ7WUFBVyxPQUFPMkQ7UUFBVTtRQUNyRyxJQUFJckgsaUJBQWlCd0QsSUFBSSxLQUFLLGNBQWNNO2FBQ3ZDLElBQUk5RCxpQkFBaUJ3RCxJQUFJLEtBQUssWUFBWU87SUFDakQ7SUFFQSxzRUFBc0U7SUFFdEUsSUFBSSxDQUFDdEUsUUFBUSxPQUFPO0lBRXBCLHFCQUNFLDhEQUFDMUIsMkRBQWVBO2tCQUNkLDRFQUFDRCxrREFBTUEsQ0FBQ3dKLEdBQUc7WUFDVEMsU0FBUztnQkFBRWxJLFNBQVM7WUFBRTtZQUN0Qm1JLFNBQVM7Z0JBQUVuSSxTQUFTO1lBQUU7WUFDdEJvSSxNQUFNO2dCQUFFcEksU0FBUztZQUFFO1lBQ25CcUksV0FBVTs7OEJBR1YsOERBQUM1SixrREFBTUEsQ0FBQ3dKLEdBQUc7b0JBQ1RDLFNBQVM7d0JBQUVsSSxTQUFTO29CQUFFO29CQUN0Qm1JLFNBQVM7d0JBQUVuSSxTQUFTO29CQUFFO29CQUN0Qm9JLE1BQU07d0JBQUVwSSxTQUFTO29CQUFFO29CQUNuQnFJLFdBQVU7b0JBQ1ZDLFNBQVNqSTs7Ozs7OzhCQUlYLDhEQUFDNUIsa0RBQU1BLENBQUN3SixHQUFHO29CQUNUQyxTQUFTO3dCQUFFakksR0FBRzt3QkFBSUQsU0FBUzt3QkFBR3VJLE9BQU87b0JBQUs7b0JBQzFDSixTQUFTO3dCQUFFbEksR0FBRzt3QkFBR0QsU0FBUzt3QkFBR3VJLE9BQU87b0JBQUU7b0JBQ3RDSCxNQUFNO3dCQUFFbkksR0FBRzt3QkFBSUQsU0FBUzt3QkFBR3VJLE9BQU87b0JBQUs7b0JBQ3ZDQyxZQUFZO3dCQUFFQyxNQUFNO3dCQUFVQyxXQUFXO3dCQUFLQyxTQUFTO29CQUFHO29CQUMxRE4sV0FBVTs7c0NBR1YsOERBQUNKOzRCQUFJSSxXQUFVOzs7Ozs7c0NBR2YsOERBQUNKOzRCQUFJSSxXQUFVOzs4Q0FDYiw4REFBQ0o7b0NBQUlJLFdBQVU7O3NEQUNiLDhEQUFDSjs0Q0FBSUksV0FBVTs7OERBQ2IsOERBQUNKO29EQUFJSSxXQUFVOzhEQUNiLDRFQUFDckosbUhBQVFBO3dEQUFDcUosV0FBVTs7Ozs7Ozs7Ozs7OERBRXRCLDhEQUFDSjs7c0VBQ0MsOERBQUNXOzREQUFHUCxXQUFVO3NFQUFtQzs7Ozs7O3NFQUNqRCw4REFBQ1E7NERBQUVSLFdBQVU7c0VBQTRCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBSTdDLDhEQUFDSjs0Q0FBSUksV0FBVTs7OERBQ2IsOERBQUNTO29EQUFPUixTQUFTLElBQU0xRixXQUFXcUQsQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFcEQsVUFBVTs0REFBTztvREFBS3dGLFdBQVcsNkNBQWlMLE9BQXBJMUYsUUFBUUUsUUFBUSxLQUFLLFNBQVMsaUVBQWlFOzhEQUF3Qzs7Ozs7OzhEQUMzUSw4REFBQ2lHO29EQUFPUixTQUFTLElBQU0xRixXQUFXcUQsQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFcEQsVUFBVTs0REFBTztvREFBS3dGLFdBQVcsNkNBQXFMLE9BQXhJMUYsUUFBUUUsUUFBUSxLQUFLLFNBQVMscUVBQXFFOzhEQUF3Qzs7Ozs7OzhEQUMvUSw4REFBQ2lHO29EQUFPUixTQUFTLElBQU0xRixXQUFXcUQsQ0FBQUEsT0FBUztnRUFBRSxHQUFHQSxJQUFJO2dFQUFFcEQsVUFBVTs0REFBWTtvREFBS3dGLFdBQVcsNkNBQXdMLE9BQTNJMUYsUUFBUUUsUUFBUSxLQUFLLGNBQWMsbUVBQW1FOzhEQUF3Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUkzUiw4REFBQ29GO29DQUFJSSxXQUFVOztzREFDYiw4REFBQ0o7NENBQUlJLFdBQVU7c0RBQTRCLDRFQUFDNUksd0VBQW1CQTtnREFBQ3NKLE9BQU9oRztnREFBWWlHLFVBQVVoRztnREFBZUUsYUFBYUE7Z0RBQWFDLGFBQWFBO2dEQUFhOEYsYUFBWTtnREFBd0NDLGVBQWU7Ozs7Ozs7Ozs7O3NEQUVuTyw4REFBQ0o7NENBQU9LLE9BQU07NENBQVliLFNBQVNQOzRDQUFxQk0sV0FBVTtzREFDaEUsNEVBQUN4SixtSEFBU0E7Z0RBQUN3SixXQUFXLFdBQXlDLE9BQTlCbkgsVUFBVSxpQkFBaUI7Ozs7Ozs7Ozs7O3NEQUc5RCw4REFBQzRIOzRDQUFPUixTQUFTakk7NENBQVNnSSxXQUFVO3NEQUNsQyw0RUFBQzFKLG1IQUFDQTtnREFBQzBKLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1uQiw4REFBQ0o7NEJBQUlJLFdBQVU7OzhDQUViLDhEQUFDZTtvQ0FBTWYsV0FBVTs7c0RBQ2YsOERBQUNKOzs4REFDQyw4REFBQ29CO29EQUFNaEIsV0FBVTs4REFBcUM7Ozs7Ozs4REFDdEQsOERBQUNpQjtvREFDQ1AsT0FBT3BJLENBQUFBLDZCQUFBQSx1Q0FBQUEsaUJBQWtCMkQsRUFBRSxLQUFJO29EQUMvQjBFLFVBQVUsQ0FBQ087d0RBQ1QsTUFBTUMsV0FBVy9JLFVBQVV3RCxJQUFJLENBQUNDLENBQUFBLEtBQU1BLEdBQUdJLEVBQUUsS0FBS2lGLEVBQUVFLE1BQU0sQ0FBQ1YsS0FBSzt3REFDOURuSSxvQkFBb0I0SSxZQUFZO29EQUNsQztvREFDQW5CLFdBQVU7O3NFQUVWLDhEQUFDcUI7NERBQU9YLE9BQU07c0VBQUc7Ozs7Ozt3REFDaEJ0SSxVQUFVb0YsR0FBRyxDQUFDMkQsQ0FBQUEseUJBQ2IsOERBQUNFO2dFQUF5QlgsT0FBT1MsU0FBU2xGLEVBQUU7Z0VBQUUrRCxXQUFVOzBFQUErQm1CLFNBQVNyRixJQUFJOytEQUF2RnFGLFNBQVNsRixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FLN0IzRCxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQndELElBQUksTUFBSyw4QkFDMUIsOERBQUM4RDs7OERBQ0MsOERBQUMwQjtvREFBR3RCLFdBQVU7OERBQTRDOzs7Ozs7OERBQzFELDhEQUFDSjtvREFBSUksV0FBVTs4REFDWjt3REFBRTt3REFBUTt3REFBUTtxREFBWSxDQUFxQnhDLEdBQUcsQ0FBQ2hELENBQUFBLHlCQUN0RCw4REFBQ2lHOzREQUFzQlIsU0FBUyxJQUFNMUYsV0FBV3FELENBQUFBLE9BQVM7d0VBQUUsR0FBR0EsSUFBSTt3RUFBRXBEO29FQUFTOzREQUFLd0YsV0FBVyxxREFBMkwsT0FBdEkxRixRQUFRRSxRQUFRLEtBQUtBLFdBQVcsaUVBQWlFO3NFQUNsUCw0RUFBQ29GO2dFQUFJSSxXQUFVOztrRkFDYiw4REFBQ0o7d0VBQUlJLFdBQVU7a0ZBQWtFeEYsYUFBYSxTQUFTLGlCQUFPQSxhQUFhLFNBQVMsaUJBQU87Ozs7OztrRkFDM0ksOERBQUNvRjs7MEZBQ0MsOERBQUNBO2dGQUFJSSxXQUFVOzBGQUFrQ3hGLGFBQWEsU0FBUyxVQUFVQSxhQUFhLFNBQVMsY0FBVzs7Ozs7OzBGQUNsSCw4REFBQ29GO2dGQUFJSSxXQUFVOzBGQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7OzJEQUxqQ3hGOzs7Ozs7Ozs7Ozs7Ozs7O3NEQWNyQiw4REFBQ29GOzRDQUFJSSxXQUFVOzs4REFDYiw4REFBQ0o7b0RBQUlJLFdBQVU7OERBQThCOzs7Ozs7OERBQzdDLDhEQUFDSjtvREFBSUksV0FBVTs7c0VBQ2IsOERBQUN1Qjs0REFBTWIsT0FBT3ZIOzREQUFld0gsVUFBVSxDQUFDTyxJQUFNOUgsaUJBQWlCOEgsRUFBRUUsTUFBTSxDQUFDVixLQUFLOzREQUFHRSxhQUFZOzREQUEwQlosV0FBVTs7Ozs7O3NFQUNoSSw4REFBQ1M7NERBQU9SLFNBQVNSOzREQUFzQk8sV0FBVTtzRUFBc0Y7Ozs7Ozs7Ozs7Ozs4REFHekksOERBQUNKO29EQUFJSSxXQUFVOztzRUFDYiw4REFBQ1M7NERBQU9SLFNBQVNQOzREQUFxQjhCLFVBQVUzSSxXQUFXLENBQUNQOzREQUFrQjBILFdBQVU7c0VBQWlEOzs7Ozs7c0VBQ3pJLDhEQUFDUzs0REFBT1IsU0FBUztnRUFBUTFGLFdBQVdxRCxDQUFBQSxPQUFTO3dFQUFFLEdBQUdBLElBQUk7d0VBQUVuRCxRQUFRO29FQUFTOzREQUFLOzREQUFHdUYsV0FBVTtzRUFBMEM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNM0ksOERBQUN5QjtvQ0FBS3pCLFdBQVU7O3dDQUNibkgseUJBQ0MsOERBQUMrRzs0Q0FBSUksV0FBVTtzREFDWjBCLE1BQU1DLElBQUksQ0FBQztnREFBRWpHLFFBQVE7NENBQUUsR0FBRzhCLEdBQUcsQ0FBQyxDQUFDb0UsR0FBR0Msa0JBQ2pDLDhEQUFDekwsa0RBQU1BLENBQUN3SixHQUFHO29EQUFTQyxTQUFTO3dEQUFFbEksU0FBUztvREFBRTtvREFBR21JLFNBQVM7d0RBQUVuSSxTQUFTO29EQUFFO29EQUFHd0ksWUFBWTt3REFBRTJCLE9BQU9ELElBQUk7b0RBQUs7b0RBQUc3QixXQUFVO21EQUFoRzZCOzs7Ozs7Ozs7O3dDQUt0QixDQUFDaEosV0FBV0UsdUJBQ1gsOERBQUM2Rzs0Q0FBSUksV0FBVTtzREFBOENqSDs7Ozs7O3dDQUc5RCxDQUFDRixXQUFXLENBQUNFLFNBQVMwRixlQUFlL0MsTUFBTSxLQUFLLG1CQUMvQyw4REFBQ2tFOzRDQUFJSSxXQUFVO3NEQUFpQzs7Ozs7O3dDQUdqRCxDQUFDbkgsV0FBVyxDQUFDRSxTQUFTMEYsZUFBZS9DLE1BQU0sR0FBRyxtQkFDN0MsOERBQUNrRTs0Q0FBSUksV0FBVTtzREFDWnZCLGVBQWVzRCxLQUFLLENBQUMsR0FBRzlJLHNCQUFzQnVFLEdBQUcsQ0FBQ0MsQ0FBQUEsc0JBQ2pELDhEQUFDdUU7b0RBQXlCdkUsT0FBT0E7b0RBQU93RSxZQUFZaEssaUJBQWlCd0YsTUFBTXhCLEVBQUU7b0RBQUVpRyxVQUFVLElBQU01QyxrQkFBa0I3QjtvREFBUTBFLGtCQUFrQixJQUFNekQscUJBQXFCakI7b0RBQVEyRSxZQUFZdkksaUJBQWlCOEQsR0FBRyxDQUFDRixNQUFNeEIsRUFBRTtvREFBR3NDLFNBQVNqRyxDQUFBQSw2QkFBQUEsdUNBQUFBLGlCQUFrQndELElBQUksTUFBSyxhQUFhOUUsMEVBQWVBLEdBQUdELDhFQUFpQkE7b0RBQUUyRCxZQUFZQTttREFBNVMrQyxNQUFNeEIsRUFBRTs7Ozs7Ozs7Ozt3Q0FLN0IsQ0FBQ3BELFdBQVcsQ0FBQ0UsU0FBUzBGLGVBQWUvQyxNQUFNLEdBQUd6QyxzQ0FDN0MsOERBQUMyRzs0Q0FBSUksV0FBVTtzREFDYiw0RUFBQ1M7Z0RBQU9SLFNBQVNUO2dEQUFzQlEsV0FBVTswREFBK0U7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU94SSw4REFBQ0o7NEJBQUlJLFdBQVU7OzhDQUNiLDhEQUFDSjtvQ0FBSUksV0FBVTs7d0NBQTBCdkIsZUFBZS9DLE1BQU07d0NBQUM7Ozs7Ozs7OENBQy9ELDhEQUFDa0U7b0NBQUlJLFdBQVU7O3NEQUNiLDhEQUFDUzs0Q0FBT1IsU0FBU2pJOzRDQUFTZ0ksV0FBVTtzREFBa0M7Ozs7OztzREFDdEUsOERBQUNTOzRDQUFPVCxXQUFVO3NEQUE2RTs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUluRyw4REFBQzFJLHlFQUErQkE7NEJBQUNTLFFBQVFzQjs0QkFBeUJvRSxPQUFPbEU7NEJBQXVCOEksV0FBVzlDOzRCQUE2QitDLFVBQVUsSUFBTWhKLDJCQUEyQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLN0w7R0F4WE14Qjs7UUFDYWxCLDBEQUFPQTtRQWdDcEJNLHVFQUFpQkE7OztLQWpDakJZO0FBc1lOLE1BQU1rSyxZQUFzQztRQUFDLEVBQUV2RSxLQUFLLEVBQUV3RSxVQUFVLEVBQUVDLFFBQVEsRUFBRUMsZ0JBQWdCLEVBQUVDLGFBQWEsS0FBSyxFQUFFN0QsT0FBTyxFQUFFN0QsYUFBYSxFQUFFLEVBQUU7UUE2QlQrQyxnQkFJSUE7SUFoQ3JJLE1BQU04RSxjQUFjaEUsUUFBUWMsYUFBYSxHQUFHZCxRQUFRYyxhQUFhLENBQUM1QixTQUFTLFVBQVU7SUFFckYscUJBQ0UsOERBQUNySCxrREFBTUEsQ0FBQ3dKLEdBQUc7UUFBQzRDLFlBQVk7WUFBRXRDLE9BQU87UUFBSztRQUFHTCxTQUFRO1FBQVVDLFNBQVE7UUFBVUMsTUFBSztRQUFPQyxXQUFXLG9FQUF5SSxPQUFyRWlDLGFBQWEsOEJBQThCLDJCQUEwQjs7MEJBQzNPLDhEQUFDckM7Z0JBQUlJLFdBQVU7O2tDQUNiLDhEQUFDdkosbUhBQU1BO3dCQUFDdUosV0FBVTs7Ozs7O29CQUNqQnZDLE1BQU1nRixRQUFRLElBQUk7Ozs7Ozs7WUFHcEJGLDZCQUNDLDhEQUFDM0M7Z0JBQUlJLFdBQVU7MEJBQ2IsNEVBQUMwQztvQkFBSTFDLFdBQVU7b0JBQXFCMkMsU0FBUTtvQkFBWUMsTUFBSztvQkFBT0MsUUFBTzs4QkFBZSw0RUFBQ0M7d0JBQUtDLGVBQWM7d0JBQVFDLGdCQUFlO3dCQUFRQyxhQUFhO3dCQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzBCQUluSyw4REFBQ3REO2dCQUFJSSxXQUFVOztrQ0FDYiw4REFBQ0o7d0JBQUlJLFdBQVU7OzBDQUNiLDhEQUFDc0I7Z0NBQUd0QixXQUFVOzBDQUE0Qyw0RUFBQzNJLDZFQUFlQTtvQ0FBQzhMLE1BQU0xRixNQUFNM0IsSUFBSTtvQ0FBRXNILFdBQVcxSTs7Ozs7Ozs7Ozs7MENBQ3hHLDhEQUFDOEY7Z0NBQUVSLFdBQVU7MENBQW9ELDRFQUFDM0ksNkVBQWVBO29DQUFDOEwsTUFBTTFGLE1BQU14QixFQUFFO29DQUFFbUgsV0FBVzFJOzs7Ozs7Ozs7Ozs0QkFDNUcrQyxNQUFNNEYsV0FBVyxrQkFBSSw4REFBQzdDO2dDQUFFUixXQUFVOzBDQUE4Qyw0RUFBQzNJLDZFQUFlQTtvQ0FBQzhMLE1BQU0xRixNQUFNNEYsV0FBVztvQ0FBRUQsV0FBVzFJOzs7Ozs7Ozs7OzswQ0FFdEksOERBQUNrRjtnQ0FBSUksV0FBVTs7a0RBQ2IsOERBQUNKO3dDQUFJSSxXQUFVOzswREFDYiw4REFBQ0o7Z0RBQUlJLFdBQVU7MERBQXlCOzs7Ozs7MERBQ3hDLDhEQUFDSjtnREFBSUksV0FBVTswREFBb0N6QixRQUFRK0UsbUJBQW1CLEdBQUcvRSxRQUFRK0UsbUJBQW1CLENBQUM3RixNQUFNOEYsY0FBYyxJQUFJOUYsTUFBTThGLGNBQWM7Ozs7Ozs7Ozs7OztrREFFM0osOERBQUMzRDt3Q0FBSUksV0FBVTs7MERBQ2IsOERBQUNKO2dEQUFJSSxXQUFVOzBEQUF5Qjs7Ozs7OzBEQUN4Qyw4REFBQ0o7Z0RBQUlJLFdBQVU7O29EQUFvQ3pCLFFBQVFpRixXQUFXLEdBQUdqRixRQUFRaUYsV0FBVyxDQUFDL0YsTUFBTWdHLE9BQU8sQ0FBQ0MsTUFBTSxLQUFJakcsaUJBQUFBLE1BQU1nRyxPQUFPLGNBQWJoRyxxQ0FBQUEsZUFBZWlHLE1BQU07b0RBQUM7Ozs7Ozs7Ozs7Ozs7a0RBRTdJLDhEQUFDOUQ7d0NBQUlJLFdBQVU7OzBEQUNiLDhEQUFDSjtnREFBSUksV0FBVTswREFBeUI7Ozs7OzswREFDeEMsOERBQUNKO2dEQUFJSSxXQUFVOztvREFBb0N6QixRQUFRaUYsV0FBVyxHQUFHakYsUUFBUWlGLFdBQVcsQ0FBQy9GLE1BQU1nRyxPQUFPLENBQUNFLFVBQVUsS0FBSWxHLGtCQUFBQSxNQUFNZ0csT0FBTyxjQUFiaEcsc0NBQUFBLGdCQUFla0csVUFBVTtvREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLekosOERBQUMvRDt3QkFBSUksV0FBVTs7MENBQ2IsOERBQUNTO2dDQUFPUixTQUFTaUM7Z0NBQVVsQyxXQUFXLDhEQUFzTSxPQUF4SWlDLGFBQWEsNEJBQTRCOzBDQUFtR0EsMkJBQWE7O3NEQUFFLDhEQUFDdkwsbUhBQUtBOzRDQUFDc0osV0FBVTs7Ozs7O3dDQUE2Qjs7bURBQWlCOzs7Ozs7MENBRTlULDhEQUFDUztnQ0FBT1IsU0FBU2tDO2dDQUFrQlgsVUFBVVk7Z0NBQVlwQyxXQUFXLDZCQUFrSSxPQUFyR3ZDLE1BQU1DLFVBQVUsR0FBRyxxQ0FBcUM7Z0NBQWdEb0QsT0FBT3JELE1BQU1DLFVBQVUsR0FBRyxxQkFBcUI7MENBQ3JQMEUsMkJBQWEsOERBQUN4QztvQ0FBSUksV0FBVTs7Ozs7OERBQThFLDhEQUFDekosbUhBQUlBO29DQUFDeUosV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNdkk7TUFoRE1nQztBQWtETixNQUFNNEIsb0JBQThGO1FBQUMsRUFBRW5HLEtBQUssRUFBRXdFLFVBQVUsRUFBRUMsUUFBUSxFQUFFO1dBQ2xJLDhEQUFDOUwsa0RBQU1BLENBQUN3SixHQUFHO1FBQUM0QyxZQUFZO1lBQUV0QyxPQUFPO1FBQUs7UUFBR0YsV0FBWTtRQUFrRkMsU0FBU2lDOzswQkFDOUksOERBQUN0QztnQkFBSUksV0FBVTs7a0NBQ2IsOERBQUNKOzswQ0FDQyw4REFBQ1c7Z0NBQUdQLFdBQVU7MENBQW9DdkMsTUFBTTNCLElBQUk7Ozs7OzswQ0FDNUQsOERBQUMwRTtnQ0FBRVIsV0FBVTswQ0FBK0J2QyxNQUFNNEYsV0FBVzs7Ozs7Ozs7Ozs7O2tDQUUvRCw4REFBQ3pEO3dCQUFJSSxXQUFVOzswQ0FDYiw4REFBQ0o7Z0NBQUlJLFdBQVU7MENBQXlCOzs7Ozs7MENBQ3hDLDhEQUFDSjtnQ0FBSUksV0FBVTswQ0FBb0NoSiwwRUFBZUEsQ0FBQ3NNLG1CQUFtQixDQUFDN0YsTUFBTThGLGNBQWM7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFHL0csOERBQUMzRDtnQkFBSUksV0FBVTs7a0NBQ2IsOERBQUNKO3dCQUFJSSxXQUFVOzswQ0FBd0MsOERBQUNKO2dDQUFJSSxXQUFVOzBDQUFpQzs7Ozs7OzBDQUFXLDhEQUFDSjtnQ0FBSUksV0FBVTs7b0NBQW9DaEosMEVBQWVBLENBQUN3TSxXQUFXLENBQUMvRixNQUFNZ0csT0FBTyxDQUFDQyxNQUFNO29DQUFFOzs7Ozs7Ozs7Ozs7O2tDQUN2Tiw4REFBQzlEO3dCQUFJSSxXQUFVOzswQ0FBd0MsOERBQUNKO2dDQUFJSSxXQUFVOzBDQUFpQzs7Ozs7OzBDQUFZLDhEQUFDSjtnQ0FBSUksV0FBVTs7b0NBQW9DaEosMEVBQWVBLENBQUN3TSxXQUFXLENBQUMvRixNQUFNZ0csT0FBTyxDQUFDRSxVQUFVO29DQUFFOzs7Ozs7Ozs7Ozs7O2tDQUM1Tiw4REFBQy9EO3dCQUFJSSxXQUFVO2tDQUFrQiw0RUFBQ1M7NEJBQU9SLFNBQVNpQzs0QkFBVWxDLFdBQVcsMEJBQXlILE9BQS9GaUMsYUFBYSwwREFBMEQ7c0NBQTRCQSxhQUFhLGdCQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFek47TUFqQlIyQjtBQW9CTiwrREFBZTlMLG1CQUFtQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvTW9kZWxTZWxlY3Rpb25Nb2RhbC50c3g/YWFkNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgbW90aW9uLCBBbmltYXRlUHJlc2VuY2UgfSBmcm9tICdmcmFtZXItbW90aW9uJztcbmltcG9ydCB7IFgsIFNlYXJjaCwgU3RhciwgUmVmcmVzaEN3LCBMYXllcnMsIENoZWNrLCBTcGFya2xlcyB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBkYiB9IGZyb20gJ0AvbGliL2ZpcmViYXNlJztcbmltcG9ydCB7IGdldFVzZXJBUElFbmRwb2ludHMsIHR5cGUgQVBJRW5kcG9pbnQgfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9zZXR0aW5nc1NlcnZpY2UnO1xuaW1wb3J0IHsgb3BlblJvdXRlclNlcnZpY2UgfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9vcGVuUm91dGVyU2VydmljZSc7XG5pbXBvcnQgeyBkZWVwU2Vla1NlcnZpY2UgfSBmcm9tICdAL2xpYi9zZXJ2aWNlcy9kZWVwU2Vla1NlcnZpY2UnO1xuaW1wb3J0IHsgbW9kZWxGYXZvcml0ZXNTZXJ2aWNlIH0gZnJvbSAnQC9saWIvc2VydmljZXMvbW9kZWxGYXZvcml0ZXNTZXJ2aWNlJztcbmltcG9ydCB7IEFJTW9kZWwsIE1vZGVsQ2F0ZWdvcnksIE1vZGVsU29ydEJ5LCBNb2RlbEZpbHRlcnMgfSBmcm9tICdAL2xpYi90eXBlcy9jaGF0JztcbmltcG9ydCB7IHVzZUFkdmFuY2VkU2VhcmNoIH0gZnJvbSAnQC9ob29rcy91c2VBZHZhbmNlZFNlYXJjaCc7XG5pbXBvcnQgeyBhZHZhbmNlZEZpbHRlcnNTZXJ2aWNlLCBTbWFydENhdGVnb3J5IH0gZnJvbSAnQC9saWIvc2VydmljZXMvYWR2YW5jZWRGaWx0ZXJzU2VydmljZSc7XG5pbXBvcnQgQWR2YW5jZWRTZWFyY2hJbnB1dCwgeyBIaWdobGlnaHRlZFRleHQgfSBmcm9tICdAL2NvbXBvbmVudHMvQWR2YW5jZWRTZWFyY2hJbnB1dCc7XG5pbXBvcnQgRXhwZW5zaXZlTW9kZWxDb25maXJtYXRpb25Nb2RhbCBmcm9tICcuL0V4cGVuc2l2ZU1vZGVsQ29uZmlybWF0aW9uTW9kYWwnO1xuXG5pbnRlcmZhY2UgTW9kZWxTZWxlY3Rpb25Nb2RhbFByb3BzIHtcbiAgaXNPcGVuOiBib29sZWFuO1xuICBvbkNsb3NlOiAoKSA9PiB2b2lkO1xuICBjdXJyZW50TW9kZWw6IHN0cmluZztcbiAgb25Nb2RlbFNlbGVjdDogKG1vZGVsSWQ6IHN0cmluZykgPT4gdm9pZDtcbn1cblxuLy8gUGVxdWVuYXMgY29uc3RhbnRlcyBkZSBVWFxuY29uc3QgQ0FDSEVfRFVSQVRJT04gPSA1ICogNjAgKiAxMDAwO1xuY29uc3QgRU5EUE9JTlRTX0NBQ0hFX0RVUkFUSU9OID0gMTAgKiA2MCAqIDEwMDA7XG5jb25zdCBNT0RFTFNfUEVSX1BBR0UgPSAxMjtcblxuY29uc3QgZmFkZUluID0geyBvcGFjaXR5OiAxLCB5OiAwIH07XG5jb25zdCBmYWRlT3V0ID0geyBvcGFjaXR5OiAwLCB5OiA4IH07XG5cbmNvbnN0IE1vZGVsU2VsZWN0aW9uTW9kYWw6IFJlYWN0LkZDPE1vZGVsU2VsZWN0aW9uTW9kYWxQcm9wcz4gPSAoeyBpc09wZW4sIG9uQ2xvc2UsIGN1cnJlbnRNb2RlbCwgb25Nb2RlbFNlbGVjdCB9KSA9PiB7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbZW5kcG9pbnRzLCBzZXRFbmRwb2ludHNdID0gdXNlU3RhdGU8QVBJRW5kcG9pbnRbXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWRFbmRwb2ludCwgc2V0U2VsZWN0ZWRFbmRwb2ludF0gPSB1c2VTdGF0ZTxBUElFbmRwb2ludCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbW9kZWxzLCBzZXRNb2RlbHNdID0gdXNlU3RhdGU8QUlNb2RlbFtdPihbXSk7XG4gIGNvbnN0IFtmYXZvcml0ZU1vZGVsSWRzLCBzZXRGYXZvcml0ZU1vZGVsSWRzXSA9IHVzZVN0YXRlPFNldDxzdHJpbmc+PihuZXcgU2V0KCkpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtkaXNwbGF5ZWRNb2RlbHNDb3VudCwgc2V0RGlzcGxheWVkTW9kZWxzQ291bnRdID0gdXNlU3RhdGUoTU9ERUxTX1BFUl9QQUdFKTtcbiAgY29uc3QgW2N1c3RvbU1vZGVsSWQsIHNldEN1c3RvbU1vZGVsSWRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbc2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWwsIHNldFNob3dFeHBlbnNpdmVNb2RlbE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3BlbmRpbmdFeHBlbnNpdmVNb2RlbCwgc2V0UGVuZGluZ0V4cGVuc2l2ZU1vZGVsXSA9IHVzZVN0YXRlPEFJTW9kZWwgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzbWFydENhdGVnb3JpZXMsIHNldFNtYXJ0Q2F0ZWdvcmllc10gPSB1c2VTdGF0ZTxTbWFydENhdGVnb3J5W10+KFtdKTtcbiAgY29uc3QgW2Zhdm9yaXRlVG9nZ2xpbmcsIHNldEZhdm9yaXRlVG9nZ2xpbmddID0gdXNlU3RhdGU8U2V0PHN0cmluZz4+KG5ldyBTZXQoKSk7XG4gIGNvbnN0IFtsYXN0TG9hZGVkRW5kcG9pbnQsIHNldExhc3RMb2FkZWRFbmRwb2ludF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW21vZGVsc0NhY2hlLCBzZXRNb2RlbHNDYWNoZV0gPSB1c2VTdGF0ZTxNYXA8c3RyaW5nLCB7IG1vZGVsczogQUlNb2RlbFtdOyB0aW1lc3RhbXA6IG51bWJlciB9Pj4obmV3IE1hcCgpKTtcbiAgY29uc3QgW2VuZHBvaW50c0NhY2hlLCBzZXRFbmRwb2ludHNDYWNoZV0gPSB1c2VTdGF0ZTx7IGVuZHBvaW50czogQVBJRW5kcG9pbnRbXTsgdGltZXN0YW1wOiBudW1iZXIgfSB8IG51bGw+KG51bGwpO1xuXG4gIGNvbnN0IFtmaWx0ZXJzLCBzZXRGaWx0ZXJzXSA9IHVzZVN0YXRlPE1vZGVsRmlsdGVycz4oe1xuICAgIGNhdGVnb3J5OiAncGFpZCcsXG4gICAgc29ydEJ5OiAnbmV3ZXN0JyxcbiAgICBzZWFyY2hUZXJtOiAnJ1xuICB9KTtcblxuICBjb25zdCB7XG4gICAgc2VhcmNoVGVybSxcbiAgICBzZXRTZWFyY2hUZXJtLFxuICAgIHNlYXJjaFJlc3VsdHMsXG4gICAgc3VnZ2VzdGlvbnMsXG4gICAgaXNTZWFyY2hpbmcsXG4gICAgaGFzU2VhcmNoZWQsXG4gICAgY2xlYXJTZWFyY2hcbiAgfSA9IHVzZUFkdmFuY2VkU2VhcmNoKG1vZGVscywgeyBkZWJvdW5jZU1zOiAzMDAsIGVuYWJsZVN1Z2dlc3Rpb25zOiBmYWxzZSwgY2FjaGVSZXN1bHRzOiB0cnVlLCBmdXp6eVRocmVzaG9sZDogMC42LCBtYXhSZXN1bHRzOiA1MCwgYm9vc3RGYXZvcml0ZXM6IHRydWUgfSk7XG5cbiAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tIEzDk0dJQ0EgREUgREFET1MgKG1hbnRpZGEpIC0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICh1c2VyICYmIGlzT3Blbikge1xuICAgICAgaWYgKGVuZHBvaW50c0NhY2hlICYmIERhdGUubm93KCkgLSBlbmRwb2ludHNDYWNoZS50aW1lc3RhbXAgPCBFTkRQT0lOVFNfQ0FDSEVfRFVSQVRJT04pIHtcbiAgICAgICAgc2V0RW5kcG9pbnRzKGVuZHBvaW50c0NhY2hlLmVuZHBvaW50cyk7XG4gICAgICAgIGlmICghc2VsZWN0ZWRFbmRwb2ludCAmJiBlbmRwb2ludHNDYWNoZS5lbmRwb2ludHMubGVuZ3RoID4gMCkge1xuICAgICAgICAgIGNvbnN0IG9wZW5Sb3V0ZXJFbmRwb2ludCA9IGVuZHBvaW50c0NhY2hlLmVuZHBvaW50cy5maW5kKGVwID0+IGVwLm5hbWUgPT09ICdPcGVuUm91dGVyJyk7XG4gICAgICAgICAgc2V0U2VsZWN0ZWRFbmRwb2ludChvcGVuUm91dGVyRW5kcG9pbnQgfHwgZW5kcG9pbnRzQ2FjaGUuZW5kcG9pbnRzWzBdKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgbG9hZEVuZHBvaW50cygpO1xuICAgICAgfVxuICAgIH1cbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gIH0sIFt1c2VyLCBpc09wZW5dKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChzZWxlY3RlZEVuZHBvaW50KSB7XG4gICAgICBjb25zdCBjYWNoZUtleSA9IGAke3NlbGVjdGVkRW5kcG9pbnQuaWR9XyR7c2VsZWN0ZWRFbmRwb2ludC5uYW1lfWA7XG4gICAgICBjb25zdCBjYWNoZWREYXRhID0gbW9kZWxzQ2FjaGUuZ2V0KGNhY2hlS2V5KTtcbiAgICAgIGlmIChjYWNoZWREYXRhICYmIERhdGUubm93KCkgLSBjYWNoZWREYXRhLnRpbWVzdGFtcCA8IENBQ0hFX0RVUkFUSU9OKSB7XG4gICAgICAgIHNldE1vZGVscyhjYWNoZWREYXRhLm1vZGVscyk7XG4gICAgICAgIHNldExhc3RMb2FkZWRFbmRwb2ludChzZWxlY3RlZEVuZHBvaW50LmlkKTtcbiAgICAgIH0gZWxzZSBpZiAobGFzdExvYWRlZEVuZHBvaW50ICE9PSBzZWxlY3RlZEVuZHBvaW50LmlkIHx8ICFjYWNoZWREYXRhKSB7XG4gICAgICAgIGlmIChzZWxlY3RlZEVuZHBvaW50Lm5hbWUgPT09ICdPcGVuUm91dGVyJykgbG9hZE9wZW5Sb3V0ZXJNb2RlbHMoKTtcbiAgICAgICAgZWxzZSBpZiAoc2VsZWN0ZWRFbmRwb2ludC5uYW1lID09PSAnRGVlcFNlZWsnKSBsb2FkRGVlcFNlZWtNb2RlbHMoKTtcbiAgICAgIH1cbiAgICB9XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWhvb2tzL2V4aGF1c3RpdmUtZGVwc1xuICB9LCBbc2VsZWN0ZWRFbmRwb2ludCwgbGFzdExvYWRlZEVuZHBvaW50LCBtb2RlbHNDYWNoZV0pO1xuXG4gIGNvbnN0IGdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSA9IGFzeW5jICgpOiBQcm9taXNlPHN0cmluZz4gPT4ge1xuICAgIGlmICghdXNlcj8uZW1haWwpIHJldHVybiAndW5rbm93bic7XG4gICAgY29uc3QgeyBjb2xsZWN0aW9uLCBxdWVyeSwgd2hlcmUsIGdldERvY3MgfSA9IGF3YWl0IGltcG9ydCgnZmlyZWJhc2UvZmlyZXN0b3JlJyk7XG4gICAgY29uc3QgcSA9IHF1ZXJ5KGNvbGxlY3Rpb24oZGIsICd1c3VhcmlvcycpLCB3aGVyZSgnZW1haWwnLCAnPT0nLCB1c2VyLmVtYWlsKSk7XG4gICAgY29uc3QgcXVlcnlTbmFwc2hvdCA9IGF3YWl0IGdldERvY3MocSk7XG4gICAgaWYgKCFxdWVyeVNuYXBzaG90LmVtcHR5KSByZXR1cm4gcXVlcnlTbmFwc2hvdC5kb2NzWzBdLmRhdGEoKS51c2VybmFtZSB8fCBxdWVyeVNuYXBzaG90LmRvY3NbMF0uaWQ7XG4gICAgcmV0dXJuICd1bmtub3duJztcbiAgfTtcblxuICBjb25zdCBsb2FkRW5kcG9pbnRzID0gYXN5bmMgKCkgPT4ge1xuICAgIGlmICghdXNlcikgcmV0dXJuO1xuICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IobnVsbCk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCB1c2VyRW5kcG9pbnRzID0gYXdhaXQgZ2V0VXNlckFQSUVuZHBvaW50cyh1c2VybmFtZSk7XG4gICAgICBzZXRFbmRwb2ludHNDYWNoZSh7IGVuZHBvaW50czogdXNlckVuZHBvaW50cywgdGltZXN0YW1wOiBEYXRlLm5vdygpIH0pO1xuICAgICAgc2V0RW5kcG9pbnRzKHVzZXJFbmRwb2ludHMpO1xuICAgICAgY29uc3Qgb3BlblJvdXRlckVuZHBvaW50ID0gdXNlckVuZHBvaW50cy5maW5kKGVwID0+IGVwLm5hbWUgPT09ICdPcGVuUm91dGVyJyk7XG4gICAgICBzZXRTZWxlY3RlZEVuZHBvaW50KG9wZW5Sb3V0ZXJFbmRwb2ludCB8fCB1c2VyRW5kcG9pbnRzWzBdIHx8IG51bGwpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXRFcnJvcignRXJybyBhbyBjYXJyZWdhciBlbmRwb2ludHMuJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2FkT3BlblJvdXRlck1vZGVscyA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkRW5kcG9pbnQgfHwgIXVzZXIpIHJldHVybjtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBvcGVuUm91dGVyTW9kZWxzID0gYXdhaXQgb3BlblJvdXRlclNlcnZpY2UuZmV0Y2hNb2RlbHMoKTtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBmYXZvcml0ZUlkcyA9IGF3YWl0IG1vZGVsRmF2b3JpdGVzU2VydmljZS5nZXRGYXZvcml0ZU1vZGVsSWRzKHVzZXJuYW1lLCBzZWxlY3RlZEVuZHBvaW50LmlkKTtcbiAgICAgIGNvbnN0IG1vZGVsc1dpdGhGYXZvcml0ZXMgPSBvcGVuUm91dGVyTW9kZWxzLm1hcChtb2RlbCA9PiAoeyAuLi5tb2RlbCwgaXNGYXZvcml0ZTogZmF2b3JpdGVJZHMuaGFzKG1vZGVsLmlkKSB9KSk7XG4gICAgICBjb25zdCBjYWNoZUtleSA9IGAke3NlbGVjdGVkRW5kcG9pbnQuaWR9XyR7c2VsZWN0ZWRFbmRwb2ludC5uYW1lfWA7XG4gICAgICBzZXRNb2RlbHNDYWNoZShwcmV2ID0+IG5ldyBNYXAocHJldikuc2V0KGNhY2hlS2V5LCB7IG1vZGVsczogbW9kZWxzV2l0aEZhdm9yaXRlcywgdGltZXN0YW1wOiBEYXRlLm5vdygpIH0pKTtcbiAgICAgIHNldE1vZGVscyhtb2RlbHNXaXRoRmF2b3JpdGVzKTtcbiAgICAgIHNldEZhdm9yaXRlTW9kZWxJZHMoZmF2b3JpdGVJZHMpO1xuICAgICAgc2V0TGFzdExvYWRlZEVuZHBvaW50KHNlbGVjdGVkRW5kcG9pbnQuaWQpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBzZXRFcnJvcignRXJybyBhbyBjYXJyZWdhciBtb2RlbG9zIE9wZW5Sb3V0ZXIuJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBsb2FkRGVlcFNlZWtNb2RlbHMgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEVuZHBvaW50IHx8ICF1c2VyKSByZXR1cm47XG4gICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcbiAgICB0cnkge1xuICAgICAgY29uc3QgZGVlcFNlZWtNb2RlbHMgPSBhd2FpdCBkZWVwU2Vla1NlcnZpY2UuZmV0Y2hNb2RlbHMoKTtcbiAgICAgIGNvbnN0IGNhY2hlS2V5ID0gYCR7c2VsZWN0ZWRFbmRwb2ludC5pZH1fJHtzZWxlY3RlZEVuZHBvaW50Lm5hbWV9YDtcbiAgICAgIHNldE1vZGVsc0NhY2hlKHByZXYgPT4gbmV3IE1hcChwcmV2KS5zZXQoY2FjaGVLZXksIHsgbW9kZWxzOiBkZWVwU2Vla01vZGVscywgdGltZXN0YW1wOiBEYXRlLm5vdygpIH0pKTtcbiAgICAgIHNldE1vZGVscyhkZWVwU2Vla01vZGVscyk7XG4gICAgICBzZXRMYXN0TG9hZGVkRW5kcG9pbnQoc2VsZWN0ZWRFbmRwb2ludC5pZCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHNldEVycm9yKCdFcnJvIGFvIGNhcnJlZ2FyIG1vZGVsb3MgRGVlcFNlZWsuJyk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRGaWx0ZXJlZE1vZGVscyA9ICgpID0+IHtcbiAgICBsZXQgZmlsdGVyZWQgPSBbLi4ubW9kZWxzXTtcbiAgICBpZiAoc2VsZWN0ZWRFbmRwb2ludD8ubmFtZSA9PT0gJ09wZW5Sb3V0ZXInKSB7XG4gICAgICBmaWx0ZXJlZCA9IG9wZW5Sb3V0ZXJTZXJ2aWNlLmZpbHRlckJ5Q2F0ZWdvcnkoZmlsdGVyZWQsIGZpbHRlcnMuY2F0ZWdvcnkpO1xuICAgIH1cbiAgICBpZiAoaGFzU2VhcmNoZWQgJiYgc2VhcmNoVGVybS50cmltKCkpIHtcbiAgICAgIGNvbnN0IHNlYXJjaFJlc3VsdElkcyA9IG5ldyBTZXQoc2VhcmNoUmVzdWx0cy5tYXAocmVzdWx0ID0+IHJlc3VsdC5tb2RlbC5pZCkpO1xuICAgICAgZmlsdGVyZWQgPSBmaWx0ZXJlZC5maWx0ZXIobW9kZWwgPT4gc2VhcmNoUmVzdWx0SWRzLmhhcyhtb2RlbC5pZCkpO1xuICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRDYXRlZ29yeSkge1xuICAgICAgZmlsdGVyZWQgPSBhZHZhbmNlZEZpbHRlcnNTZXJ2aWNlLmdldE1vZGVsc0J5Q2F0ZWdvcnkoZmlsdGVyZWQsIHNlbGVjdGVkQ2F0ZWdvcnkpO1xuICAgIH1cbiAgICBpZiAoIWhhc1NlYXJjaGVkIHx8ICFzZWFyY2hUZXJtLnRyaW0oKSkge1xuICAgICAgY29uc3Qgc2VydmljZSA9IHNlbGVjdGVkRW5kcG9pbnQ/Lm5hbWUgPT09ICdEZWVwU2VlaycgPyBkZWVwU2Vla1NlcnZpY2UgOiBvcGVuUm91dGVyU2VydmljZTtcbiAgICAgIGZpbHRlcmVkID0gc2VydmljZS5zb3J0TW9kZWxzKGZpbHRlcmVkLCBmaWx0ZXJzLnNvcnRCeSk7XG4gICAgfVxuICAgIHJldHVybiBmaWx0ZXJlZDtcbiAgfTtcblxuICBjb25zdCBmaWx0ZXJlZE1vZGVscyA9IGdldEZpbHRlcmVkTW9kZWxzKCk7XG5cbiAgY29uc3QgaGFuZGxlVG9nZ2xlRmF2b3JpdGUgPSBhc3luYyAobW9kZWw6IEFJTW9kZWwpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIXNlbGVjdGVkRW5kcG9pbnQgfHwgZmF2b3JpdGVUb2dnbGluZy5oYXMobW9kZWwuaWQpKSByZXR1cm47XG4gICAgc2V0RmF2b3JpdGVUb2dnbGluZyhwcmV2ID0+IG5ldyBTZXQocHJldikuYWRkKG1vZGVsLmlkKSk7XG4gICAgY29uc3Qgb3B0aW1pc3RpY05ld1N0YXR1cyA9ICFtb2RlbC5pc0Zhdm9yaXRlO1xuICAgIGNvbnN0IHVwZGF0ZVVJID0gKHN0YXR1czogYm9vbGVhbikgPT4ge1xuICAgICAgc2V0RmF2b3JpdGVNb2RlbElkcyhwcmV2ID0+IHtcbiAgICAgICAgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTtcbiAgICAgICAgaWYgKHN0YXR1cykgbmV3U2V0LmFkZChtb2RlbC5pZCk7IGVsc2UgbmV3U2V0LmRlbGV0ZShtb2RlbC5pZCk7XG4gICAgICAgIHJldHVybiBuZXdTZXQ7XG4gICAgICB9KTtcbiAgICAgIHNldE1vZGVscyhwcmV2ID0+IHByZXYubWFwKG0gPT4gbS5pZCA9PT0gbW9kZWwuaWQgPyB7IC4uLm0sIGlzRmF2b3JpdGU6IHN0YXR1cyB9IDogbSkpO1xuICAgIH07XG4gICAgdXBkYXRlVUkob3B0aW1pc3RpY05ld1N0YXR1cyk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHVzZXJuYW1lID0gYXdhaXQgZ2V0VXNlcm5hbWVGcm9tRmlyZXN0b3JlKCk7XG4gICAgICBjb25zdCBhY3R1YWxOZXdTdGF0dXMgPSBhd2FpdCBtb2RlbEZhdm9yaXRlc1NlcnZpY2UudG9nZ2xlRmF2b3JpdGUodXNlcm5hbWUsIHNlbGVjdGVkRW5kcG9pbnQuaWQsIG1vZGVsLmlkLCBtb2RlbC5uYW1lKTtcbiAgICAgIGlmIChhY3R1YWxOZXdTdGF0dXMgIT09IG9wdGltaXN0aWNOZXdTdGF0dXMpIHVwZGF0ZVVJKGFjdHVhbE5ld1N0YXR1cyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHVwZGF0ZVVJKCFvcHRpbWlzdGljTmV3U3RhdHVzKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0RmF2b3JpdGVUb2dnbGluZyhwcmV2ID0+IHsgY29uc3QgbmV3U2V0ID0gbmV3IFNldChwcmV2KTsgbmV3U2V0LmRlbGV0ZShtb2RlbC5pZCk7IHJldHVybiBuZXdTZXQ7IH0pO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBpc0V4cGVuc2l2ZU1vZGVsID0gKG1vZGVsOiBBSU1vZGVsKTogYm9vbGVhbiA9PiB7XG4gICAgaWYgKHNlbGVjdGVkRW5kcG9pbnQ/Lm5hbWUgIT09ICdPcGVuUm91dGVyJykgcmV0dXJuIGZhbHNlO1xuICAgIHJldHVybiBvcGVuUm91dGVyU2VydmljZS5nZXRUb3RhbFByaWNlKG1vZGVsKSA+IDAuMDAwMDI7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2VsZWN0TW9kZWwgPSAobW9kZWw6IEFJTW9kZWwpID0+IHtcbiAgICBpZiAoaXNFeHBlbnNpdmVNb2RlbChtb2RlbCkpIHtcbiAgICAgIHNldFBlbmRpbmdFeHBlbnNpdmVNb2RlbChtb2RlbCk7XG4gICAgICBzZXRTaG93RXhwZW5zaXZlTW9kZWxNb2RhbCh0cnVlKTtcbiAgICB9IGVsc2Uge1xuICAgICAgb25Nb2RlbFNlbGVjdChtb2RlbC5pZCk7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNvbmZpcm1FeHBlbnNpdmVNb2RlbCA9ICgpID0+IHtcbiAgICBpZiAocGVuZGluZ0V4cGVuc2l2ZU1vZGVsKSB7XG4gICAgICBvbk1vZGVsU2VsZWN0KHBlbmRpbmdFeHBlbnNpdmVNb2RlbC5pZCk7XG4gICAgICBzZXRTaG93RXhwZW5zaXZlTW9kZWxNb2RhbChmYWxzZSk7XG4gICAgICBzZXRQZW5kaW5nRXhwZW5zaXZlTW9kZWwobnVsbCk7XG4gICAgICBvbkNsb3NlKCk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUxvYWRNb3JlTW9kZWxzID0gKCkgPT4gc2V0RGlzcGxheWVkTW9kZWxzQ291bnQocHJldiA9PiBwcmV2ICsgTU9ERUxTX1BFUl9QQUdFKTtcbiAgY29uc3QgaGFuZGxlVXNlQ3VzdG9tTW9kZWwgPSAoKSA9PiB7IGlmIChjdXN0b21Nb2RlbElkLnRyaW0oKSkgeyBvbk1vZGVsU2VsZWN0KGN1c3RvbU1vZGVsSWQudHJpbSgpKTsgb25DbG9zZSgpOyB9IH07XG4gIGNvbnN0IGhhbmRsZVJlZnJlc2hNb2RlbHMgPSAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZEVuZHBvaW50KSByZXR1cm47XG4gICAgY29uc3QgY2FjaGVLZXkgPSBgJHtzZWxlY3RlZEVuZHBvaW50LmlkfV8ke3NlbGVjdGVkRW5kcG9pbnQubmFtZX1gO1xuICAgIHNldE1vZGVsc0NhY2hlKHByZXYgPT4geyBjb25zdCBuZXdDYWNoZSA9IG5ldyBNYXAocHJldik7IG5ld0NhY2hlLmRlbGV0ZShjYWNoZUtleSk7IHJldHVybiBuZXdDYWNoZTsgfSk7XG4gICAgaWYgKHNlbGVjdGVkRW5kcG9pbnQubmFtZSA9PT0gJ09wZW5Sb3V0ZXInKSBsb2FkT3BlblJvdXRlck1vZGVscygpO1xuICAgIGVsc2UgaWYgKHNlbGVjdGVkRW5kcG9pbnQubmFtZSA9PT0gJ0RlZXBTZWVrJykgbG9hZERlZXBTZWVrTW9kZWxzKCk7XG4gIH07XG5cbiAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG4gIGlmICghaXNPcGVuKSByZXR1cm4gbnVsbDtcblxuICByZXR1cm4gKFxuICAgIDxBbmltYXRlUHJlc2VuY2U+XG4gICAgICA8bW90aW9uLmRpdlxuICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAgfX1cbiAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxIH19XG4gICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIHotNTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcC02XCJcbiAgICAgID5cbiAgICAgICAgey8qIEJhY2tkcm9wICovfVxuICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgIGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSB9fVxuICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibGFjay82MCB2aWEtYmxhY2svNTAgdG8tdHJhbnNwYXJlbnQgYmFja2Ryb3AtYmx1ci1zbVwiXG4gICAgICAgICAgb25DbGljaz17b25DbG9zZX1cbiAgICAgICAgLz5cblxuICAgICAgICB7LyogTW9kYWwgQ2FyZCAqL31cbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IHk6IDIwLCBvcGFjaXR5OiAwLCBzY2FsZTogMC45OCB9fVxuICAgICAgICAgIGFuaW1hdGU9e3sgeTogMCwgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cbiAgICAgICAgICBleGl0PXt7IHk6IDEyLCBvcGFjaXR5OiAwLCBzY2FsZTogMC45OCB9fVxuICAgICAgICAgIHRyYW5zaXRpb249e3sgdHlwZTogJ3NwcmluZycsIHN0aWZmbmVzczogMzAwLCBkYW1waW5nOiAzMCB9fVxuICAgICAgICAgIGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbCBtYXgtdy1bMTEwMHB4XSBoLVs4NnZoXSBiZy1ncmFkaWVudC10by1iciBmcm9tLWJsdWUtOTUwLzkwIHRvLXNsYXRlLTkwMC85MCByb3VuZGVkLTN4bCBzaGFkb3ctWzBfMjBweF82MHB4X3JnYmEoMiw2LDIzLDAuOCldIGJvcmRlciBib3JkZXItd2hpdGUvNSBvdmVyZmxvdy1oaWRkZW4gZmxleCBmbGV4LWNvbFwiXG4gICAgICAgID5cbiAgICAgICAgICB7LyogRGVjb3JhdGl2ZSBncmFkaWVudCAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgcG9pbnRlci1ldmVudHMtbm9uZSBiZy1bbGluZWFyLWdyYWRpZW50KDEzNWRlZywjMGVhNWE0XzAlLCMwMjg0YzdfNDAlLHJnYmEoMTUsNDEsNjYsMC4yNSlfMTAwJSldIG1peC1ibGVuZC1vdmVybGF5IG9wYWNpdHktMTBcIiAvPlxuXG4gICAgICAgICAgey8qIEhFQURFUiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInotMjAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHB4LTYgcHktNCBib3JkZXItYiBib3JkZXItd2hpdGUvNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNFwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTExIGgtMTEgcm91bmRlZC1sZyBiZy1ncmFkaWVudC10by10ciBmcm9tLWN5YW4tNTAwIHRvLWJsdWUtNjAwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPFNwYXJrbGVzIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPkVzY29saGEgdW0gbW9kZWxvPC9oMz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zbGF0ZS0zMDAvODBcIj5Sw6FwaWRvIOKAlCBib25pdG8g4oCUIGNvbmZpw6F2ZWw8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmZsZXggaXRlbXMtY2VudGVyIGdhcC0yIGJnLXdoaXRlLzMgcm91bmRlZC1mdWxsIHB4LTIgcHktMVwiPlxuICAgICAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17KCkgPT4gc2V0RmlsdGVycyhwcmV2ID0+ICh7IC4uLnByZXYsIGNhdGVnb3J5OiAncGFpZCcgfSkpfSBjbGFzc05hbWU9e2BweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQtc20gdHJhbnNpdGlvbiAke2ZpbHRlcnMuY2F0ZWdvcnkgPT09ICdwYWlkJyA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tY3lhbi01MDAgdGV4dC13aGl0ZSBzaGFkb3cnIDogJ3RleHQtc2xhdGUtMjAwLzcwIGhvdmVyOmJnLXdoaXRlLzUnfWB9PlBhZ29zPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgY2F0ZWdvcnk6ICdmcmVlJyB9KSl9IGNsYXNzTmFtZT17YHB4LTMgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC1zbSB0cmFuc2l0aW9uICR7ZmlsdGVycy5jYXRlZ29yeSA9PT0gJ2ZyZWUnID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmVlbi02MDAgdG8tZW1lcmFsZC01MDAgdGV4dC13aGl0ZSBzaGFkb3cnIDogJ3RleHQtc2xhdGUtMjAwLzcwIGhvdmVyOmJnLXdoaXRlLzUnfWB9Pkdyw6F0aXM8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9eygpID0+IHNldEZpbHRlcnMocHJldiA9PiAoeyAuLi5wcmV2LCBjYXRlZ29yeTogJ2Zhdm9yaXRlcycgfSkpfSBjbGFzc05hbWU9e2BweC0zIHB5LTEgcm91bmRlZC1mdWxsIHRleHQtc20gdHJhbnNpdGlvbiAke2ZpbHRlcnMuY2F0ZWdvcnkgPT09ICdmYXZvcml0ZXMnID8gJ2JnLWdyYWRpZW50LXRvLXIgZnJvbS1hbWJlci00MDAgdG8tYW1iZXItMzAwIHRleHQtd2hpdGUgc2hhZG93JyA6ICd0ZXh0LXNsYXRlLTIwMC83MCBob3ZlcjpiZy13aGl0ZS81J31gfT5GYXZvcml0b3M8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhpZGRlbiBzbTpibG9jayB3LVszNjBweF1cIj48QWR2YW5jZWRTZWFyY2hJbnB1dCB2YWx1ZT17c2VhcmNoVGVybX0gb25DaGFuZ2U9e3NldFNlYXJjaFRlcm19IHN1Z2dlc3Rpb25zPXtzdWdnZXN0aW9uc30gaXNTZWFyY2hpbmc9e2lzU2VhcmNoaW5nfSBwbGFjZWhvbGRlcj1cIkJ1c2NhciBwb3IgbW9kZWxvLCBwcm92ZWRvciBvdSB0YWcuLi5cIiBzaG93U3VnZ2VzdGlvbnMgLz48L2Rpdj5cblxuICAgICAgICAgICAgICA8YnV0dG9uIHRpdGxlPVwiQXR1YWxpemFyXCIgb25DbGljaz17aGFuZGxlUmVmcmVzaE1vZGVsc30gY2xhc3NOYW1lPVwicC0yIHJvdW5kZWQtbGcgaG92ZXI6Ymctd2hpdGUvNSB0cmFuc2l0aW9uIHRleHQtc2xhdGUtMjAwLzgwXCI+XG4gICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9e2B3LTUgaC01ICR7bG9hZGluZyA/ICdhbmltYXRlLXNwaW4nIDogJyd9YH0gLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtvbkNsb3NlfSBjbGFzc05hbWU9XCJwLTIgcm91bmRlZC1sZyBiZy13aGl0ZS81IGhvdmVyOmJnLXdoaXRlLzggdHJhbnNpdGlvblwiPlxuICAgICAgICAgICAgICAgIDxYIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1zbGF0ZS0xMDBcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIEJPRFkgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtMSBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICAgIHsvKiBTSURFQkFSICovfVxuICAgICAgICAgICAgPGFzaWRlIGNsYXNzTmFtZT1cInctNjQgYmctZ3JhZGllbnQtdG8tYiBmcm9tLXRyYW5zcGFyZW50IHRvLWJsYWNrLzIwIHAtNSBib3JkZXItciBib3JkZXItd2hpdGUvNiBmbGV4IGZsZXgtY29sIGdhcC02XCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zbGF0ZS0zMDAgZm9udC1tZWRpdW1cIj5FbmRwb2ludDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlbGVjdGVkRW5kcG9pbnQ/LmlkIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGVuZHBvaW50ID0gZW5kcG9pbnRzLmZpbmQoZXAgPT4gZXAuaWQgPT09IGUudGFyZ2V0LnZhbHVlKTtcbiAgICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRFbmRwb2ludChlbmRwb2ludCB8fCBudWxsKTtcbiAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yIHctZnVsbCBiZy10cmFuc3BhcmVudCBib3JkZXIgYm9yZGVyLXdoaXRlLzYgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC1zbSB0ZXh0LXdoaXRlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1jeWFuLTQwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIlwiPlNlbGVjaW9uZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgICAge2VuZHBvaW50cy5tYXAoZW5kcG9pbnQgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIGtleT17ZW5kcG9pbnQuaWR9IHZhbHVlPXtlbmRwb2ludC5pZH0gY2xhc3NOYW1lPVwiYmctc2xhdGUtOTAwIHRleHQtc2xhdGUtMTAwXCI+e2VuZHBvaW50Lm5hbWV9PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAge3NlbGVjdGVkRW5kcG9pbnQ/Lm5hbWUgPT09ICdPcGVuUm91dGVyJyAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtMjAwIGZvbnQtc2VtaWJvbGQgbWItM1wiPkNhdGVnb3JpYXMgaW50ZWxpZ2VudGVzPC9oND5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICB7KFsncGFpZCcsICdmcmVlJywgJ2Zhdm9yaXRlcyddIGFzIE1vZGVsQ2F0ZWdvcnlbXSkubWFwKGNhdGVnb3J5ID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIGtleT17Y2F0ZWdvcnl9IG9uQ2xpY2s9eygpID0+IHNldEZpbHRlcnMocHJldiA9PiAoeyAuLi5wcmV2LCBjYXRlZ29yeSB9KSl9IGNsYXNzTmFtZT17YHRleHQtc20gdGV4dC1sZWZ0IHB4LTMgcHktMiByb3VuZGVkLWxnIHRyYW5zaXRpb24gJHtmaWx0ZXJzLmNhdGVnb3J5ID09PSBjYXRlZ29yeSA/ICdiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tY3lhbi01MDAgdGV4dC13aGl0ZSBzaGFkb3cnIDogJ3RleHQtc2xhdGUtMzAwLzgwIGhvdmVyOmJnLXdoaXRlLzUnfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggcm91bmRlZC1sZyBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBiZy13aGl0ZS80XCI+e2NhdGVnb3J5ID09PSAncGFpZCcgPyAn8J+SsCcgOiBjYXRlZ29yeSA9PT0gJ2ZyZWUnID8gJ/CfjoEnIDogJ+KtkCd9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGNhcGl0YWxpemVcIj57Y2F0ZWdvcnkgPT09ICdwYWlkJyA/ICdQYWdvcycgOiBjYXRlZ29yeSA9PT0gJ2ZyZWUnID8gJ0dyw6F0aXMnIDogJ0Zhdm9yaXRvcyd9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtNDAwXCI+RmlsdHJvcyByw6FwaWRvczwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtYXV0b1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTQwMCBtYi0yXCI+SW5zZXJpciBtb2RlbG8gY3VzdG9taXphZG88L2Rpdj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dCB2YWx1ZT17Y3VzdG9tTW9kZWxJZH0gb25DaGFuZ2U9eyhlKSA9PiBzZXRDdXN0b21Nb2RlbElkKGUudGFyZ2V0LnZhbHVlKX0gcGxhY2Vob2xkZXI9XCJwcm92ZWRvci9ub21lLWRvLW1vZGVsb1wiIGNsYXNzTmFtZT1cImZsZXgtMSBweC0zIHB5LTIgcm91bmRlZC1sZyBiZy13aGl0ZS8zIHRleHQtd2hpdGUgdGV4dC1zbSBmb2N1czpvdXRsaW5lLW5vbmVcIiAvPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVVc2VDdXN0b21Nb2RlbH0gY2xhc3NOYW1lPVwicHgtMyBweS0yIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1jeWFuLTUwMCByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW1cIj5Vc2FyPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTQgZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVSZWZyZXNoTW9kZWxzfSBkaXNhYmxlZD17bG9hZGluZyB8fCAhc2VsZWN0ZWRFbmRwb2ludH0gY2xhc3NOYW1lPVwiZmxleC0xIHB4LTMgcHktMiByb3VuZGVkLWxnIHRleHQtc20gYmctd2hpdGUvNVwiPkF0dWFsaXphcjwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXsoKSA9PiB7IHNldEZpbHRlcnMocHJldiA9PiAoeyAuLi5wcmV2LCBzb3J0Qnk6ICduZXdlc3QnIH0pKTsgfX0gY2xhc3NOYW1lPVwicHgtMyBweS0yIHJvdW5kZWQtbGcgdGV4dC1zbSBiZy13aGl0ZS81XCI+TGltcGFyPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9hc2lkZT5cblxuICAgICAgICAgICAgey8qIE1BSU4gQ09OVEVOVCAqL31cbiAgICAgICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBwLTYgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgIHtsb2FkaW5nICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTZcIj5cbiAgICAgICAgICAgICAgICAgIHtBcnJheS5mcm9tKHsgbGVuZ3RoOiA2IH0pLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICA8bW90aW9uLmRpdiBrZXk9e2l9IGluaXRpYWw9e3sgb3BhY2l0eTogMCB9fSBhbmltYXRlPXt7IG9wYWNpdHk6IDEgfX0gdHJhbnNpdGlvbj17eyBkZWxheTogaSAqIDAuMDQgfX0gY2xhc3NOYW1lPVwiaC00MCByb3VuZGVkLTJ4bCBiZy1ncmFkaWVudC10by1iciBmcm9tLXdoaXRlLzMgdG8td2hpdGUvMiBib3JkZXIgYm9yZGVyLXdoaXRlLzYgYW5pbWF0ZS1wdWxzZVwiIC8+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAgICB7IWxvYWRpbmcgJiYgZXJyb3IgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IHJvdW5kZWQtMnhsIGJnLXJlZC05MDAvNDAgdGV4dC1yZWQtMjAwXCI+e2Vycm9yfTwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHshbG9hZGluZyAmJiAhZXJyb3IgJiYgZmlsdGVyZWRNb2RlbHMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtOCB0ZXh0LWNlbnRlciB0ZXh0LXNsYXRlLTMwMFwiPk5lbmh1bSBtb2RlbG8gZW5jb250cmFkbyDigJQgdGVudGUgb3V0cm8gZmlsdHJvIG91IHRlcm1vIGRlIGJ1c2NhLjwvZGl2PlxuICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgIHshbG9hZGluZyAmJiAhZXJyb3IgJiYgZmlsdGVyZWRNb2RlbHMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIHNtOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy0zIGdhcC02XCI+XG4gICAgICAgICAgICAgICAgICB7ZmlsdGVyZWRNb2RlbHMuc2xpY2UoMCwgZGlzcGxheWVkTW9kZWxzQ291bnQpLm1hcChtb2RlbCA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxNb2RlbENhcmQga2V5PXttb2RlbC5pZH0gbW9kZWw9e21vZGVsfSBpc1NlbGVjdGVkPXtjdXJyZW50TW9kZWwgPT09IG1vZGVsLmlkfSBvblNlbGVjdD17KCkgPT4gaGFuZGxlU2VsZWN0TW9kZWwobW9kZWwpfSBvblRvZ2dsZUZhdm9yaXRlPXsoKSA9PiBoYW5kbGVUb2dnbGVGYXZvcml0ZShtb2RlbCl9IGlzVG9nZ2xpbmc9e2Zhdm9yaXRlVG9nZ2xpbmcuaGFzKG1vZGVsLmlkKX0gc2VydmljZT17c2VsZWN0ZWRFbmRwb2ludD8ubmFtZSA9PT0gJ0RlZXBTZWVrJyA/IGRlZXBTZWVrU2VydmljZSA6IG9wZW5Sb3V0ZXJTZXJ2aWNlfSBzZWFyY2hUZXJtPXtzZWFyY2hUZXJtfSAvPlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgeyFsb2FkaW5nICYmICFlcnJvciAmJiBmaWx0ZXJlZE1vZGVscy5sZW5ndGggPiBkaXNwbGF5ZWRNb2RlbHNDb3VudCAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IGZsZXgganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlTG9hZE1vcmVNb2RlbHN9IGNsYXNzTmFtZT1cInB4LTYgcHktMyByb3VuZGVkLWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWN5YW4tNTAwIHRleHQtd2hpdGVcIj5DYXJyZWdhciBtYWlzPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L21haW4+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRk9PVEVSICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS00IGJvcmRlci10IGJvcmRlci13aGl0ZS82IGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtMzAwXCI+e2ZpbHRlcmVkTW9kZWxzLmxlbmd0aH0gbW9kZWxvczwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e29uQ2xvc2V9IGNsYXNzTmFtZT1cInB4LTQgcHktMiByb3VuZGVkLWxnIGJnLXdoaXRlLzVcIj5GZWNoYXI8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJweC00IHB5LTIgcm91bmRlZC1sZyBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS02MDAgdG8tY3lhbi01MDAgdGV4dC13aGl0ZVwiPkFqdWRhPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxFeHBlbnNpdmVNb2RlbENvbmZpcm1hdGlvbk1vZGFsIGlzT3Blbj17c2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWx9IG1vZGVsPXtwZW5kaW5nRXhwZW5zaXZlTW9kZWx9IG9uQ29uZmlybT17aGFuZGxlQ29uZmlybUV4cGVuc2l2ZU1vZGVsfSBvbkNhbmNlbD17KCkgPT4gc2V0U2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWwoZmFsc2UpfSAvPlxuICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICA8L21vdGlvbi5kaXY+XG4gICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gICk7XG59O1xuXG4vLyA9PT09PT09PT09PT09PT09PT09IENPTVBPTkVOVEVTIEZJTEhPUyBSRUVTVElMSVpBRE9TID09PT09PT09PT09PT09PT09PT1cblxuaW50ZXJmYWNlIE1vZGVsQ2FyZFByb3BzIHtcbiAgbW9kZWw6IEFJTW9kZWw7XG4gIGlzU2VsZWN0ZWQ6IGJvb2xlYW47XG4gIG9uU2VsZWN0OiAoKSA9PiB2b2lkO1xuICBvblRvZ2dsZUZhdm9yaXRlOiAoKSA9PiB2b2lkO1xuICBpc1RvZ2dsaW5nPzogYm9vbGVhbjtcbiAgc2VydmljZTogdHlwZW9mIG9wZW5Sb3V0ZXJTZXJ2aWNlIHwgdHlwZW9mIGRlZXBTZWVrU2VydmljZTtcbiAgc2VhcmNoVGVybT86IHN0cmluZztcbn1cblxuY29uc3QgTW9kZWxDYXJkOiBSZWFjdC5GQzxNb2RlbENhcmRQcm9wcz4gPSAoeyBtb2RlbCwgaXNTZWxlY3RlZCwgb25TZWxlY3QsIG9uVG9nZ2xlRmF2b3JpdGUsIGlzVG9nZ2xpbmcgPSBmYWxzZSwgc2VydmljZSwgc2VhcmNoVGVybSA9ICcnIH0pID0+IHtcbiAgY29uc3QgaXNFeHBlbnNpdmUgPSBzZXJ2aWNlLmdldFRvdGFsUHJpY2UgPyBzZXJ2aWNlLmdldFRvdGFsUHJpY2UobW9kZWwpID4gMC4wMDAwMiA6IGZhbHNlO1xuXG4gIHJldHVybiAoXG4gICAgPG1vdGlvbi5kaXYgd2hpbGVIb3Zlcj17eyBzY2FsZTogMS4wMiB9fSBpbml0aWFsPVwiaW5pdGlhbFwiIGFuaW1hdGU9XCJhbmltYXRlXCIgZXhpdD1cImV4aXRcIiBjbGFzc05hbWU9e2ByZWxhdGl2ZSBwLTUgcm91bmRlZC0yeGwgYm9yZGVyIGJvcmRlci13aGl0ZS82IGJnLWdyYWRpZW50LXRvLWJyICR7aXNTZWxlY3RlZCA/ICdmcm9tLWJsdWUtODAwIHRvLWN5YW4tODAwJyA6ICdmcm9tLXdoaXRlLzMgdG8td2hpdGUvMid9IHNoYWRvdy1bMF8xMHB4XzMwcHhfcmdiYSgyLDYsMjMsMC42KWB9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTMgLWxlZnQtMyBiZy1ncmFkaWVudC10by1yIGZyb20taW5kaWdvLTUwMCB0by12aW9sZXQtNTAwIHRleHQteHMgcHgtMyBweS0xIHJvdW5kZWQtZnVsbCB0ZXh0LXdoaXRlIGZvbnQtc2VtaWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgc2hhZG93LXNtXCI+XG4gICAgICAgIDxMYXllcnMgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgIHttb2RlbC5wcm92aWRlciB8fCAncHJvdmlkZXInfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIHtpc0V4cGVuc2l2ZSAmJiAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0zIC1yaWdodC0zIHJvdW5kZWQtZnVsbCBiZy1hbWJlci01MDAgcC0xIHNoYWRvdy1sZ1wiPlxuICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LXdoaXRlXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCI+PHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTEyIDhjLTEuNjU3IDAtMyAxLjM0My0zIDN2M2g2di0zYzAtMS42NTctMS4zNDMtMy0zLTN6XCIvPjwvc3ZnPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZm9udC1zZW1pYm9sZCB0cnVuY2F0ZSB0ZXh0LWxnXCI+PEhpZ2hsaWdodGVkVGV4dCB0ZXh0PXttb2RlbC5uYW1lfSBoaWdobGlnaHQ9e3NlYXJjaFRlcm19IC8+PC9oND5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtMzAwLzgwIGZvbnQtbW9ubyB0cnVuY2F0ZSBtdC0xXCI+PEhpZ2hsaWdodGVkVGV4dCB0ZXh0PXttb2RlbC5pZH0gaGlnaGxpZ2h0PXtzZWFyY2hUZXJtfSAvPjwvcD5cbiAgICAgICAgICB7bW9kZWwuZGVzY3JpcHRpb24gJiYgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXNsYXRlLTMwMC84MCBtdC0zIGxpbmUtY2xhbXAtM1wiPjxIaWdobGlnaHRlZFRleHQgdGV4dD17bW9kZWwuZGVzY3JpcHRpb259IGhpZ2hsaWdodD17c2VhcmNoVGVybX0gLz48L3A+fVxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC00IGdyaWQgZ3JpZC1jb2xzLTMgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvMyByb3VuZGVkLWxnIHAtMyB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1zbGF0ZS0zMDBcIj5Db250ZXh0bzwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+e3NlcnZpY2UuZm9ybWF0Q29udGV4dExlbmd0aCA/IHNlcnZpY2UuZm9ybWF0Q29udGV4dExlbmd0aChtb2RlbC5jb250ZXh0X2xlbmd0aCkgOiBtb2RlbC5jb250ZXh0X2xlbmd0aH08L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS8zIHJvdW5kZWQtbGcgcC0zIHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTMwMFwiPklucHV0PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57c2VydmljZS5mb3JtYXRQcmljZSA/IHNlcnZpY2UuZm9ybWF0UHJpY2UobW9kZWwucHJpY2luZy5wcm9tcHQpIDogbW9kZWwucHJpY2luZz8ucHJvbXB0fS8xTTwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlLzMgcm91bmRlZC1sZyBwLTMgdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtMzAwXCI+T3V0cHV0PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57c2VydmljZS5mb3JtYXRQcmljZSA/IHNlcnZpY2UuZm9ybWF0UHJpY2UobW9kZWwucHJpY2luZy5jb21wbGV0aW9uKSA6IG1vZGVsLnByaWNpbmc/LmNvbXBsZXRpb259LzFNPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTM2IGZsZXggZmxleC1jb2wgaXRlbXMtZW5kIGdhcC0zXCI+XG4gICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtvblNlbGVjdH0gY2xhc3NOYW1lPXtgdy1mdWxsIHB4LTQgcHktMiByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbiAke2lzU2VsZWN0ZWQgPyAnYmctd2hpdGUgdGV4dC1zbGF0ZS05MDAnIDogJ2JnLWdyYWRpZW50LXRvLWJyIGZyb20tYmx1ZS02MDAgdG8tY3lhbi01MDAgdGV4dC13aGl0ZSBob3Zlcjpmcm9tLWJsdWUtNTAwIGhvdmVyOnRvLWN5YW4tNDAwJ31gfT57aXNTZWxlY3RlZCA/IDw+PENoZWNrIGNsYXNzTmFtZT1cInctNCBoLTQgaW5saW5lLWJsb2NrIG1yLTJcIi8+U2VsZWNpb25hZG88Lz4gOiAnU2VsZWNpb25hcid9PC9idXR0b24+XG5cbiAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e29uVG9nZ2xlRmF2b3JpdGV9IGRpc2FibGVkPXtpc1RvZ2dsaW5nfSBjbGFzc05hbWU9e2BwLTIgcm91bmRlZC1sZyB0cmFuc2l0aW9uICR7bW9kZWwuaXNGYXZvcml0ZSA/ICdiZy15ZWxsb3ctNDAwLzIwIHRleHQteWVsbG93LTMwMCcgOiAnYmctd2hpdGUvNSB0ZXh0LXNsYXRlLTIwMCBob3ZlcjpiZy13aGl0ZS83J31gfSB0aXRsZT17bW9kZWwuaXNGYXZvcml0ZSA/ICdSZW1vdmVyIGZhdm9yaXRvJyA6ICdBZGljaW9uYXIgZmF2b3JpdG8nfT5cbiAgICAgICAgICAgIHtpc1RvZ2dsaW5nID8gPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtNSB3LTUgYm9yZGVyLXQtMiBib3JkZXItYi0yIGJvcmRlci1jdXJyZW50XCIgLz4gOiA8U3RhciBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz59XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9tb3Rpb24uZGl2PlxuICApO1xufTtcblxuY29uc3QgRGVlcFNlZWtNb2RlbENhcmQ6IFJlYWN0LkZDPHsgbW9kZWw6IEFJTW9kZWw7IGlzU2VsZWN0ZWQ6IGJvb2xlYW47IG9uU2VsZWN0OiAoKSA9PiB2b2lkOyB9PiA9ICh7IG1vZGVsLCBpc1NlbGVjdGVkLCBvblNlbGVjdCB9KSA9PiAoXG4gIDxtb3Rpb24uZGl2IHdoaWxlSG92ZXI9e3sgc2NhbGU6IDEuMDEgfX0gY2xhc3NOYW1lPXtgcC02IHJvdW5kZWQtMnhsIGJvcmRlciBib3JkZXItd2hpdGUvNiBiZy1ncmFkaWVudC10by1iciBmcm9tLXdoaXRlLzQgdG8td2hpdGUvMmB9IG9uQ2xpY2s9e29uU2VsZWN0fT5cbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQganVzdGlmeS1iZXR3ZWVuIGdhcC00XCI+XG4gICAgICA8ZGl2PlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57bW9kZWwubmFtZX08L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtc2xhdGUtMzAwIG10LTJcIj57bW9kZWwuZGVzY3JpcHRpb259PC9wPlxuICAgICAgPC9kaXY+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtMzAwXCI+Q29udGV4dG88L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPntkZWVwU2Vla1NlcnZpY2UuZm9ybWF0Q29udGV4dExlbmd0aChtb2RlbC5jb250ZXh0X2xlbmd0aCl9PC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTYgZ3JpZCBncmlkLWNvbHMtMyBnYXAtM1wiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZS8zIHJvdW5kZWQtbGcgcC0zIHRleHQtY2VudGVyXCI+PGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtc2xhdGUtMzAwLzcwIG1iLTFcIj5JbnB1dDwvZGl2PjxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj57ZGVlcFNlZWtTZXJ2aWNlLmZvcm1hdFByaWNlKG1vZGVsLnByaWNpbmcucHJvbXB0KX0vMU08L2Rpdj48L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUvMyByb3VuZGVkLWxnIHAtMyB0ZXh0LWNlbnRlclwiPjxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXNsYXRlLTMwMC83MCBtYi0xXCI+T3V0cHV0PC9kaXY+PGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPntkZWVwU2Vla1NlcnZpY2UuZm9ybWF0UHJpY2UobW9kZWwucHJpY2luZy5jb21wbGV0aW9uKX0vMU08L2Rpdj48L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMyBtdC00XCI+PGJ1dHRvbiBvbkNsaWNrPXtvblNlbGVjdH0gY2xhc3NOYW1lPXtgdy1mdWxsIHB5LTMgcm91bmRlZC1sZyAke2lzU2VsZWN0ZWQgPyAnYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNjAwIHRvLWN5YW4tNTAwIHRleHQtd2hpdGUnIDogJ2JnLXdoaXRlLzUgdGV4dC13aGl0ZSd9YH0+e2lzU2VsZWN0ZWQgPyAnU2VsZWNpb25hZG8nIDogJ1NlbGVjaW9uYXIgTW9kZWxvJ308L2J1dHRvbj48L2Rpdj5cbiAgICA8L2Rpdj5cbiAgPC9tb3Rpb24uZGl2PlxuKTtcblxuZXhwb3J0IGRlZmF1bHQgTW9kZWxTZWxlY3Rpb25Nb2RhbDtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwibW90aW9uIiwiQW5pbWF0ZVByZXNlbmNlIiwiWCIsIlN0YXIiLCJSZWZyZXNoQ3ciLCJMYXllcnMiLCJDaGVjayIsIlNwYXJrbGVzIiwidXNlQXV0aCIsImRiIiwiZ2V0VXNlckFQSUVuZHBvaW50cyIsIm9wZW5Sb3V0ZXJTZXJ2aWNlIiwiZGVlcFNlZWtTZXJ2aWNlIiwibW9kZWxGYXZvcml0ZXNTZXJ2aWNlIiwidXNlQWR2YW5jZWRTZWFyY2giLCJhZHZhbmNlZEZpbHRlcnNTZXJ2aWNlIiwiQWR2YW5jZWRTZWFyY2hJbnB1dCIsIkhpZ2hsaWdodGVkVGV4dCIsIkV4cGVuc2l2ZU1vZGVsQ29uZmlybWF0aW9uTW9kYWwiLCJDQUNIRV9EVVJBVElPTiIsIkVORFBPSU5UU19DQUNIRV9EVVJBVElPTiIsIk1PREVMU19QRVJfUEFHRSIsImZhZGVJbiIsIm9wYWNpdHkiLCJ5IiwiZmFkZU91dCIsIk1vZGVsU2VsZWN0aW9uTW9kYWwiLCJpc09wZW4iLCJvbkNsb3NlIiwiY3VycmVudE1vZGVsIiwib25Nb2RlbFNlbGVjdCIsInVzZXIiLCJlbmRwb2ludHMiLCJzZXRFbmRwb2ludHMiLCJzZWxlY3RlZEVuZHBvaW50Iiwic2V0U2VsZWN0ZWRFbmRwb2ludCIsIm1vZGVscyIsInNldE1vZGVscyIsImZhdm9yaXRlTW9kZWxJZHMiLCJzZXRGYXZvcml0ZU1vZGVsSWRzIiwiU2V0IiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwiZGlzcGxheWVkTW9kZWxzQ291bnQiLCJzZXREaXNwbGF5ZWRNb2RlbHNDb3VudCIsImN1c3RvbU1vZGVsSWQiLCJzZXRDdXN0b21Nb2RlbElkIiwic2hvd0V4cGVuc2l2ZU1vZGVsTW9kYWwiLCJzZXRTaG93RXhwZW5zaXZlTW9kZWxNb2RhbCIsInBlbmRpbmdFeHBlbnNpdmVNb2RlbCIsInNldFBlbmRpbmdFeHBlbnNpdmVNb2RlbCIsInNlbGVjdGVkQ2F0ZWdvcnkiLCJzZXRTZWxlY3RlZENhdGVnb3J5Iiwic21hcnRDYXRlZ29yaWVzIiwic2V0U21hcnRDYXRlZ29yaWVzIiwiZmF2b3JpdGVUb2dnbGluZyIsInNldEZhdm9yaXRlVG9nZ2xpbmciLCJsYXN0TG9hZGVkRW5kcG9pbnQiLCJzZXRMYXN0TG9hZGVkRW5kcG9pbnQiLCJtb2RlbHNDYWNoZSIsInNldE1vZGVsc0NhY2hlIiwiTWFwIiwiZW5kcG9pbnRzQ2FjaGUiLCJzZXRFbmRwb2ludHNDYWNoZSIsImZpbHRlcnMiLCJzZXRGaWx0ZXJzIiwiY2F0ZWdvcnkiLCJzb3J0QnkiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsInNlYXJjaFJlc3VsdHMiLCJzdWdnZXN0aW9ucyIsImlzU2VhcmNoaW5nIiwiaGFzU2VhcmNoZWQiLCJjbGVhclNlYXJjaCIsImRlYm91bmNlTXMiLCJlbmFibGVTdWdnZXN0aW9ucyIsImNhY2hlUmVzdWx0cyIsImZ1enp5VGhyZXNob2xkIiwibWF4UmVzdWx0cyIsImJvb3N0RmF2b3JpdGVzIiwiRGF0ZSIsIm5vdyIsInRpbWVzdGFtcCIsImxlbmd0aCIsIm9wZW5Sb3V0ZXJFbmRwb2ludCIsImZpbmQiLCJlcCIsIm5hbWUiLCJsb2FkRW5kcG9pbnRzIiwiY2FjaGVLZXkiLCJpZCIsImNhY2hlZERhdGEiLCJnZXQiLCJsb2FkT3BlblJvdXRlck1vZGVscyIsImxvYWREZWVwU2Vla01vZGVscyIsImdldFVzZXJuYW1lRnJvbUZpcmVzdG9yZSIsImVtYWlsIiwiY29sbGVjdGlvbiIsInF1ZXJ5Iiwid2hlcmUiLCJnZXREb2NzIiwicSIsInF1ZXJ5U25hcHNob3QiLCJlbXB0eSIsImRvY3MiLCJkYXRhIiwidXNlcm5hbWUiLCJ1c2VyRW5kcG9pbnRzIiwib3BlblJvdXRlck1vZGVscyIsImZldGNoTW9kZWxzIiwiZmF2b3JpdGVJZHMiLCJnZXRGYXZvcml0ZU1vZGVsSWRzIiwibW9kZWxzV2l0aEZhdm9yaXRlcyIsIm1hcCIsIm1vZGVsIiwiaXNGYXZvcml0ZSIsImhhcyIsInByZXYiLCJzZXQiLCJkZWVwU2Vla01vZGVscyIsImdldEZpbHRlcmVkTW9kZWxzIiwiZmlsdGVyZWQiLCJmaWx0ZXJCeUNhdGVnb3J5IiwidHJpbSIsInNlYXJjaFJlc3VsdElkcyIsInJlc3VsdCIsImZpbHRlciIsImdldE1vZGVsc0J5Q2F0ZWdvcnkiLCJzZXJ2aWNlIiwic29ydE1vZGVscyIsImZpbHRlcmVkTW9kZWxzIiwiaGFuZGxlVG9nZ2xlRmF2b3JpdGUiLCJhZGQiLCJvcHRpbWlzdGljTmV3U3RhdHVzIiwidXBkYXRlVUkiLCJzdGF0dXMiLCJuZXdTZXQiLCJkZWxldGUiLCJtIiwiYWN0dWFsTmV3U3RhdHVzIiwidG9nZ2xlRmF2b3JpdGUiLCJpc0V4cGVuc2l2ZU1vZGVsIiwiZ2V0VG90YWxQcmljZSIsImhhbmRsZVNlbGVjdE1vZGVsIiwiaGFuZGxlQ29uZmlybUV4cGVuc2l2ZU1vZGVsIiwiaGFuZGxlTG9hZE1vcmVNb2RlbHMiLCJoYW5kbGVVc2VDdXN0b21Nb2RlbCIsImhhbmRsZVJlZnJlc2hNb2RlbHMiLCJuZXdDYWNoZSIsImRpdiIsImluaXRpYWwiLCJhbmltYXRlIiwiZXhpdCIsImNsYXNzTmFtZSIsIm9uQ2xpY2siLCJzY2FsZSIsInRyYW5zaXRpb24iLCJ0eXBlIiwic3RpZmZuZXNzIiwiZGFtcGluZyIsImgzIiwicCIsImJ1dHRvbiIsInZhbHVlIiwib25DaGFuZ2UiLCJwbGFjZWhvbGRlciIsInNob3dTdWdnZXN0aW9ucyIsInRpdGxlIiwiYXNpZGUiLCJsYWJlbCIsInNlbGVjdCIsImUiLCJlbmRwb2ludCIsInRhcmdldCIsIm9wdGlvbiIsImg0IiwiaW5wdXQiLCJkaXNhYmxlZCIsIm1haW4iLCJBcnJheSIsImZyb20iLCJfIiwiaSIsImRlbGF5Iiwic2xpY2UiLCJNb2RlbENhcmQiLCJpc1NlbGVjdGVkIiwib25TZWxlY3QiLCJvblRvZ2dsZUZhdm9yaXRlIiwiaXNUb2dnbGluZyIsIm9uQ29uZmlybSIsIm9uQ2FuY2VsIiwiaXNFeHBlbnNpdmUiLCJ3aGlsZUhvdmVyIiwicHJvdmlkZXIiLCJzdmciLCJ2aWV3Qm94IiwiZmlsbCIsInN0cm9rZSIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJ0ZXh0IiwiaGlnaGxpZ2h0IiwiZGVzY3JpcHRpb24iLCJmb3JtYXRDb250ZXh0TGVuZ3RoIiwiY29udGV4dF9sZW5ndGgiLCJmb3JtYXRQcmljZSIsInByaWNpbmciLCJwcm9tcHQiLCJjb21wbGV0aW9uIiwiRGVlcFNlZWtNb2RlbENhcmQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});