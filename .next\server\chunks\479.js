"use strict";exports.id=479,exports.ids=[479],exports.modules={83479:(e,a,r)=>{r.d(a,{tO:()=>l});var t=r(49254),o=r(72025),i=r(70720),d=r(37723),s=r(1606);let p={apiKey:"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8",authDomain:"rafthor-0001.firebaseapp.com",projectId:"rafthor-0001",storageBucket:"rafthor-0001.firebasestorage.app",messagingSenderId:"863587500028",appId:"1:863587500028:web:ea161ddd3a1a024a7f3c79"};if(!p.apiKey||p.apiKey.length<30)throw Error("Firebase API Key inv\xe1lida ou n\xe3o configurada");let f=(0,t.ZF)(p);(0,o.v0)(f),(0,i.ad)(f);let l=(0,d.cF)(f);(0,s.$C)(f)}};