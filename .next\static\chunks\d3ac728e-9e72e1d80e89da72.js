"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[954],{3236:function(e,t,r){r.d(t,{ZP:function(){return tp}});let SourceLocation=class SourceLocation{constructor(e,t,r){this.lexer=void 0,this.start=void 0,this.end=void 0,this.lexer=e,this.start=t,this.end=r}static range(e,t){return t?e&&e.loc&&t.loc&&e.loc.lexer===t.loc.lexer?new SourceLocation(e.loc.lexer,e.loc.start,t.loc.end):null:e&&e.loc}};let Token=class Token{constructor(e,t){this.text=void 0,this.loc=void 0,this.noexpand=void 0,this.treatAsRelax=void 0,this.text=e,this.loc=t}range(e,t){return new Token(t,SourceLocation.range(this,e))}};let ParseError=class ParseError{constructor(e,t){this.name=void 0,this.position=void 0,this.length=void 0,this.rawMessage=void 0;var r="KaTeX parse error: "+e,n=t&&t.loc;if(n&&n.start<=n.end){var i,a,o=n.lexer.input;i=n.start,a=n.end,i===o.length?r+=" at end of input: ":r+=" at position "+(i+1)+": ";var l=o.slice(i,a).replace(/[^]/g,"$&̲");r+=(i>15?"…"+o.slice(i-15,i):o.slice(0,i))+l+(a+15<o.length?o.slice(a,a+15)+"…":o.slice(a))}var s=Error(r);return s.name="ParseError",s.__proto__=ParseError.prototype,s.position=i,null!=i&&null!=a&&(s.length=a-i),s.rawMessage=e,s}};ParseError.prototype.__proto__=Error.prototype;var n,i,a=/([A-Z])/g,o={"&":"&amp;",">":"&gt;","<":"&lt;",'"':"&quot;","'":"&#x27;"},l=/[&><"']/g,getBaseElem=function getBaseElem(e){return"ordgroup"===e.type||"color"===e.type?1===e.body.length?getBaseElem(e.body[0]):e:"font"===e.type?getBaseElem(e.body):e},assert=function(e){if(!e)throw Error("Expected non-null, but got "+String(e));return e},s={contains:function(e,t){return -1!==e.indexOf(t)},deflt:function(e,t){return void 0===e?t:e},escape:function(e){return String(e).replace(l,e=>o[e])},hyphenate:function(e){return e.replace(a,"-$1").toLowerCase()},getBaseElem,isCharacterBox:function(e){var t=getBaseElem(e);return"mathord"===t.type||"textord"===t.type||"atom"===t.type},protocolFromUrl:function(e){var t=/^[\x00-\x20]*([^\\/#?]*?)(:|&#0*58|&#x0*3a|&colon)/i.exec(e);return t?":"===t[2]&&/^[a-zA-Z][a-zA-Z0-9+\-.]*$/.test(t[1])?t[1].toLowerCase():null:"_relative"}},m={displayMode:{type:"boolean",description:"Render math in display mode, which puts the math in display style (so \\int and \\sum are large, for example), and centers the math on the page on its own line.",cli:"-d, --display-mode"},output:{type:{enum:["htmlAndMathml","html","mathml"]},description:"Determines the markup language of the output.",cli:"-F, --format <type>"},leqno:{type:"boolean",description:"Render display math in leqno style (left-justified tags)."},fleqn:{type:"boolean",description:"Render display math flush left."},throwOnError:{type:"boolean",default:!0,cli:"-t, --no-throw-on-error",cliDescription:"Render errors (in the color given by --error-color) instead of throwing a ParseError exception when encountering an error."},errorColor:{type:"string",default:"#cc0000",cli:"-c, --error-color <color>",cliDescription:"A color string given in the format 'rgb' or 'rrggbb' (no #). This option determines the color of errors rendered by the -t option.",cliProcessor:e=>"#"+e},macros:{type:"object",cli:"-m, --macro <def>",cliDescription:"Define custom macro of the form '\\foo:expansion' (use multiple -m arguments for multiple macros).",cliDefault:[],cliProcessor:(e,t)=>(t.push(e),t)},minRuleThickness:{type:"number",description:"Specifies a minimum thickness, in ems, for fraction lines, `\\sqrt` top lines, `{array}` vertical lines, `\\hline`, `\\hdashline`, `\\underline`, `\\overline`, and the borders of `\\fbox`, `\\boxed`, and `\\fcolorbox`.",processor:e=>Math.max(0,e),cli:"--min-rule-thickness <size>",cliProcessor:parseFloat},colorIsTextColor:{type:"boolean",description:"Makes \\color behave like LaTeX's 2-argument \\textcolor, instead of LaTeX's one-argument \\color mode change.",cli:"-b, --color-is-text-color"},strict:{type:[{enum:["warn","ignore","error"]},"boolean","function"],description:"Turn on strict / LaTeX faithfulness mode, which throws an error if the input uses features that are not supported by LaTeX.",cli:"-S, --strict",cliDefault:!1},trust:{type:["boolean","function"],description:"Trust the input, enabling all HTML features such as \\url.",cli:"-T, --trust"},maxSize:{type:"number",default:1/0,description:"If non-zero, all user-specified sizes, e.g. in \\rule{500em}{500em}, will be capped to maxSize ems. Otherwise, elements and spaces can be arbitrarily large",processor:e=>Math.max(0,e),cli:"-s, --max-size <n>",cliProcessor:parseInt},maxExpand:{type:"number",default:1e3,description:"Limit the number of macro expansions to the specified number, to prevent e.g. infinite macro loops. If set to Infinity, the macro expander will try to fully expand as in LaTeX.",processor:e=>Math.max(0,e),cli:"-e, --max-expand <n>",cliProcessor:e=>"Infinity"===e?1/0:parseInt(e)},globalGroup:{type:"boolean",cli:!1}};let Settings=class Settings{constructor(e){for(var t in this.displayMode=void 0,this.output=void 0,this.leqno=void 0,this.fleqn=void 0,this.throwOnError=void 0,this.errorColor=void 0,this.macros=void 0,this.minRuleThickness=void 0,this.colorIsTextColor=void 0,this.strict=void 0,this.trust=void 0,this.maxSize=void 0,this.maxExpand=void 0,this.globalGroup=void 0,e=e||{},m)if(m.hasOwnProperty(t)){var r=m[t];this[t]=void 0!==e[t]?r.processor?r.processor(e[t]):e[t]:function(e){if(e.default)return e.default;var t=e.type,r=Array.isArray(t)?t[0]:t;if("string"!=typeof r)return r.enum[0];switch(r){case"boolean":return!1;case"string":return"";case"number":return 0;case"object":return{}}}(r)}}reportNonstrict(e,t,r){var n=this.strict;if("function"==typeof n&&(n=n(e,t,r)),n&&"ignore"!==n){if(!0===n||"error"===n)throw new ParseError("LaTeX-incompatible input and strict mode is set to 'error': "+t+" ["+e+"]",r);"warn"===n?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+t+" ["+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t)+" ["+e+"]")}}useStrictBehavior(e,t,r){var n=this.strict;if("function"==typeof n)try{n=n(e,t,r)}catch(e){n="error"}return!!n&&"ignore"!==n&&(!0===n||"error"===n||("warn"===n?"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to 'warn': "+t+" ["+e+"]"):"undefined"!=typeof console&&console.warn("LaTeX-incompatible input and strict mode is set to "+("unrecognized '"+n+"': "+t)+" ["+e+"]"),!1))}isTrusted(e){if(e.url&&!e.protocol){var t=s.protocolFromUrl(e.url);if(null==t)return!1;e.protocol=t}return!!("function"==typeof this.trust?this.trust(e):this.trust)}};let Style=class Style{constructor(e,t,r){this.id=void 0,this.size=void 0,this.cramped=void 0,this.id=e,this.size=t,this.cramped=r}sup(){return h[d[this.id]]}sub(){return h[c[this.id]]}fracNum(){return h[u[this.id]]}fracDen(){return h[p[this.id]]}cramp(){return h[f[this.id]]}text(){return h[b[this.id]]}isTight(){return this.size>=2}};var h=[new Style(0,0,!1),new Style(1,0,!0),new Style(2,1,!1),new Style(3,1,!0),new Style(4,2,!1),new Style(5,2,!0),new Style(6,3,!1),new Style(7,3,!0)],d=[4,5,4,5,6,7,6,7],c=[5,5,5,5,7,7,7,7],u=[2,3,4,5,6,7,6,7],p=[3,3,5,5,7,7,7,7],f=[1,1,3,3,5,5,7,7],b=[0,1,2,3,2,3,2,3],g={DISPLAY:h[0],TEXT:h[2],SCRIPT:h[4],SCRIPTSCRIPT:h[6]},y=[{name:"latin",blocks:[[256,591],[768,879]]},{name:"cyrillic",blocks:[[1024,1279]]},{name:"armenian",blocks:[[1328,1423]]},{name:"brahmic",blocks:[[2304,4255]]},{name:"georgian",blocks:[[4256,4351]]},{name:"cjk",blocks:[[12288,12543],[19968,40879],[65280,65376]]},{name:"hangul",blocks:[[44032,55215]]}],v=[];function supportedCodepoint(e){for(var t=0;t<v.length;t+=2)if(e>=v[t]&&e<=v[t+1])return!0;return!1}y.forEach(e=>e.blocks.forEach(e=>v.push(...e)));var sqrtPath=function(e,t,r){t*=1e3;var n,i,a,o,l,s,m="";switch(e){case"sqrtMain":m="M95,"+(622+(n=t)+80)+"\nc-2.7,0,-7.17,-2.7,-13.5,-8c-5.8,-5.3,-9.5,-10,-9.5,-14\nc0,-2,0.3,-3.3,1,-4c1.3,-2.7,23.83,-20.7,67.5,-54\nc44.2,-33.3,65.8,-50.3,66.5,-51c1.3,-1.3,3,-2,5,-2c4.7,0,8.7,3.3,12,10\ns173,378,173,378c0.7,0,35.3,-71,104,-213c68.7,-142,137.5,-285,206.5,-429\nc69,-144,104.5,-217.7,106.5,-221\nl"+n/2.075+" -"+n+"\nc5.3,-9.3,12,-14,20,-14\nH400000v"+(40+n)+"H845.2724\ns-225.272,467,-225.272,467s-235,486,-235,486c-2.7,4.7,-9,7,-19,7\nc-6,0,-10,-1,-12,-3s-194,-422,-194,-422s-65,47,-65,47z\nM"+(834+n)+" 80h400000v"+(40+n)+"h-400000z";break;case"sqrtSize1":m="M263,"+(601+(i=t)+80)+"c0.7,0,18,39.7,52,119\nc34,79.3,68.167,158.7,102.5,238c34.3,79.3,51.8,119.3,52.5,120\nc340,-704.7,510.7,-1060.3,512,-1067\nl"+i/2.084+" -"+i+"\nc4.7,-7.3,11,-11,19,-11\nH40000v"+(40+i)+"H1012.3\ns-271.3,567,-271.3,567c-38.7,80.7,-84,175,-136,283c-52,108,-89.167,185.3,-111.5,232\nc-22.3,46.7,-33.8,70.3,-34.5,71c-4.7,4.7,-12.3,7,-23,7s-12,-1,-12,-1\ns-109,-253,-109,-253c-72.7,-168,-109.3,-252,-110,-252c-10.7,8,-22,16.7,-34,26\nc-22,17.3,-33.3,26,-34,26s-26,-26,-26,-26s76,-59,76,-59s76,-60,76,-60z\nM"+(1001+i)+" 80h400000v"+(40+i)+"h-400000z";break;case"sqrtSize2":m="M983 "+(10+(a=t)+80)+"\nl"+a/3.13+" -"+a+"\nc4,-6.7,10,-10,18,-10 H400000v"+(40+a)+"\nH1013.1s-83.4,268,-264.1,840c-180.7,572,-277,876.3,-289,913c-4.7,4.7,-12.7,7,-24,7\ns-12,0,-12,0c-1.3,-3.3,-3.7,-11.7,-7,-25c-35.3,-125.3,-106.7,-373.3,-214,-744\nc-10,12,-21,25,-33,39s-32,39,-32,39c-6,-5.3,-15,-14,-27,-26s25,-30,25,-30\nc26.7,-32.7,52,-63,76,-91s52,-60,52,-60s208,722,208,722\nc56,-175.3,126.3,-397.3,211,-666c84.7,-268.7,153.8,-488.2,207.5,-658.5\nc53.7,-170.3,84.5,-266.8,92.5,-289.5z\nM"+(1001+a)+" 80h400000v"+(40+a)+"h-400000z";break;case"sqrtSize3":m="M424,"+(2398+(o=t)+80)+"\nc-1.3,-0.7,-38.5,-172,-111.5,-514c-73,-342,-109.8,-513.3,-110.5,-514\nc0,-2,-10.7,14.3,-32,49c-4.7,7.3,-9.8,15.7,-15.5,25c-5.7,9.3,-9.8,16,-12.5,20\ns-5,7,-5,7c-4,-3.3,-8.3,-7.7,-13,-13s-13,-13,-13,-13s76,-122,76,-122s77,-121,77,-121\ns209,968,209,968c0,-2,84.7,-361.7,254,-1079c169.3,-717.3,254.7,-1077.7,256,-1081\nl"+o/4.223+" -"+o+"c4,-6.7,10,-10,18,-10 H400000\nv"+(40+o)+"H1014.6\ns-87.3,378.7,-272.6,1166c-185.3,787.3,-279.3,1182.3,-282,1185\nc-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2z M"+(1001+o)+" 80\nh400000v"+(40+o)+"h-400000z";break;case"sqrtSize4":m="M473,"+(2713+(l=t)+80)+"\nc339.3,-1799.3,509.3,-2700,510,-2702 l"+l/5.298+" -"+l+"\nc3.3,-7.3,9.3,-11,18,-11 H400000v"+(40+l)+"H1017.7\ns-90.5,478,-276.2,1466c-185.7,988,-279.5,1483,-281.5,1485c-2,6,-10,9,-24,9\nc-8,0,-12,-0.7,-12,-2c0,-1.3,-5.3,-32,-16,-92c-50.7,-293.3,-119.7,-693.3,-207,-1200\nc0,-1.3,-5.3,8.7,-16,30c-10.7,21.3,-21.3,42.7,-32,64s-16,33,-16,33s-26,-26,-26,-26\ns76,-153,76,-153s77,-151,77,-151c0.7,0.7,35.7,202,105,604c67.3,400.7,102,602.7,104,\n606zM"+(1001+l)+" 80h400000v"+(40+l)+"H1017.7z";break;case"sqrtTall":m="M702 "+((s=t)+80)+"H400000"+(40+s)+"\nH742v"+(r-54-80-s)+"l-4 4-4 4c-.667.7 -2 1.5-4 2.5s-4.167 1.833-6.5 2.5-5.5 1-9.5 1\nh-12l-28-84c-16.667-52-96.667 -294.333-240-727l-212 -643 -85 170\nc-4-3.333-8.333-7.667-13 -13l-13-13l77-155 77-156c66 199.333 139 419.667\n219 661 l218 661zM702 80H400000v"+(40+s)+"H742z"}return m},innerPath=function(e,t){switch(e){case"⎜":return"M291 0 H417 V"+t+" H291z M291 0 H417 V"+t+" H291z";case"∣":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z";case"∥":return"M145 0 H188 V"+t+" H145z M145 0 H188 V"+t+" H145z"+("M367 0 H410 V"+t)+" H367z M367 0 H410 V"+t+" H367z";case"⎟":return"M457 0 H583 V"+t+" H457z M457 0 H583 V"+t+" H457z";case"⎢":return"M319 0 H403 V"+t+" H319z M319 0 H403 V"+t+" H319z";case"⎥":return"M263 0 H347 V"+t+" H263z M263 0 H347 V"+t+" H263z";case"⎪":return"M384 0 H504 V"+t+" H384z M384 0 H504 V"+t+" H384z";case"⏐":return"M312 0 H355 V"+t+" H312z M312 0 H355 V"+t+" H312z";case"‖":return"M257 0 H300 V"+t+" H257z M257 0 H300 V"+t+" H257z"+("M478 0 H521 V"+t)+" H478z M478 0 H521 V"+t+" H478z";default:return""}},S={doubleleftarrow:"M262 157\nl10-10c34-36 62.7-77 86-123 3.3-8 5-13.3 5-16 0-5.3-6.7-8-20-8-7.3\n 0-12.2.5-14.5 1.5-2.3 1-4.8 4.5-7.5 10.5-49.3 97.3-121.7 169.3-217 216-28\n 14-57.3 25-88 33-6.7 2-11 3.8-13 5.5-2 1.7-3 4.2-3 7.5s1 5.8 3 7.5\nc2 1.7 6.3 3.5 13 5.5 68 17.3 128.2 47.8 180.5 91.5 52.3 43.7 93.8 96.2 124.5\n 157.5 9.3 8 15.3 12.3 18 13h6c12-.7 18-4 18-10 0-2-1.7-7-5-15-23.3-46-52-87\n-86-123l-10-10h399738v-40H218c328 0 0 0 0 0l-10-8c-26.7-20-65.7-43-117-69 2.7\n-2 6-3.7 10-5 36.7-16 72.3-37.3 107-64l10-8h399782v-40z\nm8 0v40h399730v-40zm0 194v40h399730v-40z",doublerightarrow:"M399738 392l\n-10 10c-34 36-62.7 77-86 123-3.3 8-5 13.3-5 16 0 5.3 6.7 8 20 8 7.3 0 12.2-.5\n 14.5-1.5 2.3-1 4.8-4.5 7.5-10.5 49.3-97.3 121.7-169.3 217-216 28-14 57.3-25 88\n-33 6.7-2 11-3.8 13-5.5 2-1.7 3-4.2 3-7.5s-1-5.8-3-7.5c-2-1.7-6.3-3.5-13-5.5-68\n-17.3-128.2-47.8-180.5-91.5-52.3-43.7-93.8-96.2-124.5-157.5-9.3-8-15.3-12.3-18\n-13h-6c-12 .7-18 4-18 10 0 2 1.7 7 5 15 23.3 46 52 87 86 123l10 10H0v40h399782\nc-328 0 0 0 0 0l10 8c26.7 20 65.7 43 117 69-2.7 2-6 3.7-10 5-36.7 16-72.3 37.3\n-107 64l-10 8H0v40zM0 157v40h399730v-40zm0 194v40h399730v-40z",leftarrow:"M400000 241H110l3-3c68.7-52.7 113.7-120\n 135-202 4-14.7 6-23 6-25 0-7.3-7-11-21-11-8 0-13.2.8-15.5 2.5-2.3 1.7-4.2 5.8\n-5.5 12.5-1.3 4.7-2.7 10.3-4 17-12 48.7-34.8 92-68.5 130S65.3 228.3 18 247\nc-10 4-16 7.7-18 11 0 8.7 6 14.3 18 17 47.3 18.7 87.8 47 121.5 85S196 441.3 208\n 490c.7 2 1.3 5 2 9s1.2 6.7 1.5 8c.3 1.3 1 3.3 2 6s2.2 4.5 3.5 5.5c1.3 1 3.3\n 1.8 6 2.5s6 1 10 1c14 0 21-3.7 21-11 0-2-2-10.3-6-25-20-79.3-65-146.7-135-202\n l-3-3h399890zM100 241v40h399900v-40z",leftbrace:"M6 548l-6-6v-35l6-11c56-104 135.3-181.3 238-232 57.3-28.7 117\n-45 179-50h399577v120H403c-43.3 7-81 15-113 26-100.7 33-179.7 91-237 174-2.7\n 5-6 9-10 13-.7 1-7.3 1-20 1H6z",leftbraceunder:"M0 6l6-6h17c12.688 0 19.313.3 20 1 4 4 7.313 8.3 10 13\n 35.313 51.3 80.813 93.8 136.5 127.5 55.688 33.7 117.188 55.8 184.5 66.5.688\n 0 2 .3 4 1 18.688 2.7 76 4.3 172 5h399450v120H429l-6-1c-124.688-8-235-61.7\n-331-161C60.687 138.7 32.312 99.3 7 54L0 41V6z",leftgroup:"M400000 80\nH435C64 80 168.3 229.4 21 260c-5.9 1.2-18 0-18 0-2 0-3-1-3-3v-38C76 61 257 0\n 435 0h399565z",leftgroupunder:"M400000 262\nH435C64 262 168.3 112.6 21 82c-5.9-1.2-18 0-18 0-2 0-3 1-3 3v38c76 158 257 219\n 435 219h399565z",leftharpoon:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3\n-3.3 10.2-9.5 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5\n-18.3 3-21-1.3-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7\n-196 228-6.7 4.7-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40z",leftharpoonplus:"M0 267c.7 5.3 3 10 7 14h399993v-40H93c3.3-3.3 10.2-9.5\n 20.5-18.5s17.8-15.8 22.5-20.5c50.7-52 88-110.3 112-175 4-11.3 5-18.3 3-21-1.3\n-4-7.3-6-18-6-8 0-13 .7-15 2s-4.7 6.7-8 16c-42 98.7-107.3 174.7-196 228-6.7 4.7\n-10.7 8-12 10-1.3 2-2 5.7-2 11zm100-26v40h399900v-40zM0 435v40h400000v-40z\nm0 0v40h400000v-40z",leftharpoondown:"M7 241c-4 4-6.333 8.667-7 14 0 5.333.667 9 2 11s5.333\n 5.333 12 10c90.667 54 156 130 196 228 3.333 10.667 6.333 16.333 9 17 2 .667 5\n 1 9 1h5c10.667 0 16.667-2 18-6 2-2.667 1-9.667-3-21-32-87.333-82.667-157.667\n-152-211l-3-3h399907v-40zM93 281 H400000 v-40L7 241z",leftharpoondownplus:"M7 435c-4 4-6.3 8.7-7 14 0 5.3.7 9 2 11s5.3 5.3 12\n 10c90.7 54 156 130 196 228 3.3 10.7 6.3 16.3 9 17 2 .7 5 1 9 1h5c10.7 0 16.7\n-2 18-6 2-2.7 1-9.7-3-21-32-87.3-82.7-157.7-152-211l-3-3h399907v-40H7zm93 0\nv40h399900v-40zM0 241v40h399900v-40zm0 0v40h399900v-40z",lefthook:"M400000 281 H103s-33-11.2-61-33.5S0 197.3 0 164s14.2-61.2 42.5\n-83.5C70.8 58.2 104 47 142 47 c16.7 0 25 6.7 25 20 0 12-8.7 18.7-26 20-40 3.3\n-68.7 15.7-86 37-10 12-15 25.3-15 40 0 22.7 9.8 40.7 29.5 54 19.7 13.3 43.5 21\n 71.5 23h399859zM103 281v-40h399897v40z",leftlinesegment:"M40 281 V428 H0 V94 H40 V241 H400000 v40z\nM40 281 V428 H0 V94 H40 V241 H400000 v40z",leftmapsto:"M40 281 V448H0V74H40V241H400000v40z\nM40 281 V448H0V74H40V241H400000v40z",leftToFrom:"M0 147h400000v40H0zm0 214c68 40 115.7 95.7 143 167h22c15.3 0 23\n-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69-70-101l-7-8h399905v-40H95l7-8\nc28.7-32 52-65.7 70-101 10.7-23.3 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 265.3\n 68 321 0 361zm0-174v-40h399900v40zm100 154v40h399900v-40z",longequal:"M0 50 h400000 v40H0z m0 194h40000v40H0z\nM0 50 h400000 v40H0z m0 194h40000v40H0z",midbrace:"M200428 334\nc-100.7-8.3-195.3-44-280-108-55.3-42-101.7-93-139-153l-9-14c-2.7 4-5.7 8.7-9 14\n-53.3 86.7-123.7 153-211 199-66.7 36-137.3 56.3-212 62H0V214h199568c178.3-11.7\n 311.7-78.3 403-201 6-8 9.7-12 11-12 .7-.7 6.7-1 18-1s17.3.3 18 1c1.3 0 5 4 11\n 12 44.7 59.3 101.3 106.3 170 141s145.3 54.3 229 60h199572v120z",midbraceunder:"M199572 214\nc100.7 8.3 195.3 44 280 108 55.3 42 101.7 93 139 153l9 14c2.7-4 5.7-8.7 9-14\n 53.3-86.7 123.7-153 211-199 66.7-36 137.3-56.3 212-62h199568v120H200432c-178.3\n 11.7-311.7 78.3-403 201-6 8-9.7 12-11 12-.7.7-6.7 1-18 1s-17.3-.3-18-1c-1.3 0\n-5-4-11-12-44.7-59.3-101.3-106.3-170-141s-145.3-54.3-229-60H0V214z",oiintSize1:"M512.6 71.6c272.6 0 320.3 106.8 320.3 178.2 0 70.8-47.7 177.6\n-320.3 177.6S193.1 320.6 193.1 249.8c0-71.4 46.9-178.2 319.5-178.2z\nm368.1 178.2c0-86.4-60.9-215.4-368.1-215.4-306.4 0-367.3 129-367.3 215.4 0 85.8\n60.9 214.8 367.3 214.8 307.2 0 368.1-129 368.1-214.8z",oiintSize2:"M757.8 100.1c384.7 0 451.1 137.6 451.1 230 0 91.3-66.4 228.8\n-451.1 228.8-386.3 0-452.7-137.5-452.7-228.8 0-92.4 66.4-230 452.7-230z\nm502.4 230c0-111.2-82.4-277.2-502.4-277.2s-504 166-504 277.2\nc0 110 84 276 504 276s502.4-166 502.4-276z",oiiintSize1:"M681.4 71.6c408.9 0 480.5 106.8 480.5 178.2 0 70.8-71.6 177.6\n-480.5 177.6S202.1 320.6 202.1 249.8c0-71.4 70.5-178.2 479.3-178.2z\nm525.8 178.2c0-86.4-86.8-215.4-525.7-215.4-437.9 0-524.7 129-524.7 215.4 0\n85.8 86.8 214.8 524.7 214.8 438.9 0 525.7-129 525.7-214.8z",oiiintSize2:"M1021.2 53c603.6 0 707.8 165.8 707.8 277.2 0 110-104.2 275.8\n-707.8 275.8-606 0-710.2-165.8-710.2-275.8C311 218.8 415.2 53 1021.2 53z\nm770.4 277.1c0-131.2-126.4-327.6-770.5-327.6S248.4 198.9 248.4 330.1\nc0 130 128.8 326.4 772.7 326.4s770.5-196.4 770.5-326.4z",rightarrow:"M0 241v40h399891c-47.3 35.3-84 78-110 128\n-16.7 32-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20\n 11 8 0 13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7\n 39-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85\n-40.5-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n 151.7 139 205zm0 0v40h399900v-40z",rightbrace:"M400000 542l\n-6 6h-17c-12.7 0-19.3-.3-20-1-4-4-7.3-8.3-10-13-35.3-51.3-80.8-93.8-136.5-127.5\ns-117.2-55.8-184.5-66.5c-.7 0-2-.3-4-1-18.7-2.7-76-4.3-172-5H0V214h399571l6 1\nc124.7 8 235 61.7 331 161 31.3 33.3 59.7 72.7 85 118l7 13v35z",rightbraceunder:"M399994 0l6 6v35l-6 11c-56 104-135.3 181.3-238 232-57.3\n 28.7-117 45-179 50H-300V214h399897c43.3-7 81-15 113-26 100.7-33 179.7-91 237\n-174 2.7-5 6-9 10-13 .7-1 7.3-1 20-1h17z",rightgroup:"M0 80h399565c371 0 266.7 149.4 414 180 5.9 1.2 18 0 18 0 2 0\n 3-1 3-3v-38c-76-158-257-219-435-219H0z",rightgroupunder:"M0 262h399565c371 0 266.7-149.4 414-180 5.9-1.2 18 0 18\n 0 2 0 3 1 3 3v38c-76 158-257 219-435 219H0z",rightharpoon:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3\n-3.7-15.3-11-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2\n-10.7 0-16.7 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58\n 69.2 92 94.5zm0 0v40h399900v-40z",rightharpoonplus:"M0 241v40h399993c4.7-4.7 7-9.3 7-14 0-9.3-3.7-15.3-11\n-18-92.7-56.7-159-133.7-199-231-3.3-9.3-6-14.7-8-16-2-1.3-7-2-15-2-10.7 0-16.7\n 2-18 6-2 2.7-1 9.7 3 21 15.3 42 36.7 81.8 64 119.5 27.3 37.7 58 69.2 92 94.5z\nm0 0v40h399900v-40z m100 194v40h399900v-40zm0 0v40h399900v-40z",rightharpoondown:"M399747 511c0 7.3 6.7 11 20 11 8 0 13-.8 15-2.5s4.7-6.8\n 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3 8.5-5.8 9.5\n-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3-64.7 57-92 95\n-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 241v40h399900v-40z",rightharpoondownplus:"M399747 705c0 7.3 6.7 11 20 11 8 0 13-.8\n 15-2.5s4.7-6.8 8-15.5c40-94 99.3-166.3 178-217 13.3-8 20.3-12.3 21-13 5.3-3.3\n 8.5-5.8 9.5-7.5 1-1.7 1.5-5.2 1.5-10.5s-2.3-10.3-7-15H0v40h399908c-34 25.3\n-64.7 57-92 95-27.3 38-48.7 77.7-64 119-3.3 8.7-5 14-5 16zM0 435v40h399900v-40z\nm0-194v40h400000v-40zm0 0v40h400000v-40z",righthook:"M399859 241c-764 0 0 0 0 0 40-3.3 68.7-15.7 86-37 10-12 15-25.3\n 15-40 0-22.7-9.8-40.7-29.5-54-19.7-13.3-43.5-21-71.5-23-17.3-1.3-26-8-26-20 0\n-13.3 8.7-20 26-20 38 0 71 11.2 99 33.5 0 0 7 5.6 21 16.7 14 11.2 21 33.5 21\n 66.8s-14 61.2-42 83.5c-28 22.3-61 33.5-99 33.5L0 241z M0 281v-40h399859v40z",rightlinesegment:"M399960 241 V94 h40 V428 h-40 V281 H0 v-40z\nM399960 241 V94 h40 V428 h-40 V281 H0 v-40z",rightToFrom:"M400000 167c-70.7-42-118-97.7-142-167h-23c-15.3 0-23 .3-23\n 1 0 1.3 5.3 13.7 16 37 18 35.3 41.3 69 70 101l7 8H0v40h399905l-7 8c-28.7 32\n-52 65.7-70 101-10.7 23.3-16 35.7-16 37 0 .7 7.7 1 23 1h23c24-69.3 71.3-125 142\n-167z M100 147v40h399900v-40zM0 341v40h399900v-40z",twoheadleftarrow:"M0 167c68 40\n 115.7 95.7 143 167h22c15.3 0 23-.3 23-1 0-1.3-5.3-13.7-16-37-18-35.3-41.3-69\n-70-101l-7-8h125l9 7c50.7 39.3 85 86 103 140h46c0-4.7-6.3-18.7-19-42-18-35.3\n-40-67.3-66-96l-9-9h399716v-40H284l9-9c26-28.7 48-60.7 66-96 12.7-23.333 19\n-37.333 19-42h-46c-18 54-52.3 100.7-103 140l-9 7H95l7-8c28.7-32 52-65.7 70-101\n 10.7-23.333 16-35.7 16-37 0-.7-7.7-1-23-1h-22C115.7 71.3 68 127 0 167z",twoheadrightarrow:"M400000 167\nc-68-40-115.7-95.7-143-167h-22c-15.3 0-23 .3-23 1 0 1.3 5.3 13.7 16 37 18 35.3\n 41.3 69 70 101l7 8h-125l-9-7c-50.7-39.3-85-86-103-140h-46c0 4.7 6.3 18.7 19 42\n 18 35.3 40 67.3 66 96l9 9H0v40h399716l-9 9c-26 28.7-48 60.7-66 96-12.7 23.333\n-19 37.333-19 42h46c18-54 52.3-100.7 103-140l9-7h125l-7 8c-28.7 32-52 65.7-70\n 101-10.7 23.333-16 35.7-16 37 0 .7 7.7 1 23 1h22c27.3-71.3 75-127 143-167z",tilde1:"M200 55.538c-77 0-168 73.953-177 73.953-3 0-7\n-2.175-9-5.437L2 97c-1-2-2-4-2-6 0-4 2-7 5-9l20-12C116 12 171 0 207 0c86 0\n 114 68 191 68 78 0 168-68 177-68 4 0 7 2 9 5l12 19c1 2.175 2 4.35 2 6.525 0\n 4.35-2 7.613-5 9.788l-19 13.05c-92 63.077-116.937 75.308-183 76.128\n-68.267.847-113-73.952-191-73.952z",tilde2:"M344 55.266c-142 0-300.638 81.316-311.5 86.418\n-8.01 3.762-22.5 10.91-23.5 5.562L1 120c-1-2-1-3-1-4 0-5 3-9 8-10l18.4-9C160.9\n 31.9 283 0 358 0c148 0 188 122 331 122s314-97 326-97c4 0 8 2 10 7l7 21.114\nc1 2.14 1 3.21 1 4.28 0 5.347-3 9.626-7 10.696l-22.3 12.622C852.6 158.372 751\n 181.476 676 181.476c-149 0-189-126.21-332-126.21z",tilde3:"M786 59C457 59 32 175.242 13 175.242c-6 0-10-3.457\n-11-10.37L.15 138c-1-7 3-12 10-13l19.2-6.4C378.4 40.7 634.3 0 804.3 0c337 0\n 411.8 157 746.8 157 328 0 754-112 773-112 5 0 10 3 11 9l1 14.075c1 8.066-.697\n 16.595-6.697 17.492l-21.052 7.31c-367.9 98.146-609.15 122.696-778.15 122.696\n -338 0-409-156.573-744-156.573z",tilde4:"M786 58C457 58 32 177.487 13 177.487c-6 0-10-3.345\n-11-10.035L.15 143c-1-7 3-12 10-13l22-6.7C381.2 35 637.15 0 807.15 0c337 0 409\n 177 744 177 328 0 754-127 773-127 5 0 10 3 11 9l1 14.794c1 7.805-3 13.38-9\n 14.495l-20.7 5.574c-366.85 99.79-607.3 139.372-776.3 139.372-338 0-409\n -175.236-744-175.236z",vec:"M377 20c0-5.333 1.833-10 5.5-14S391 0 397 0c4.667 0 8.667 1.667 12 5\n3.333 2.667 6.667 9 10 19 6.667 24.667 20.333 43.667 41 57 7.333 4.667 11\n10.667 11 18 0 6-1 10-3 12s-6.667 5-14 9c-28.667 14.667-53.667 35.667-75 63\n-1.333 1.333-3.167 3.5-5.5 6.5s-4 4.833-5 5.5c-1 .667-2.5 1.333-4.5 2s-4.333 1\n-7 1c-4.667 0-9.167-1.833-13.5-5.5S337 184 337 178c0-12.667 15.667-32.333 47-59\nH213l-171-1c-8.667-6-13-12.333-13-19 0-4.667 4.333-11.333 13-20h359\nc-16-25.333-24-45-24-59z",widehat1:"M529 0h5l519 115c5 1 9 5 9 10 0 1-1 2-1 3l-4 22\nc-1 5-5 9-11 9h-2L532 67 19 159h-2c-5 0-9-4-11-9l-5-22c-1-6 2-12 8-13z",widehat2:"M1181 0h2l1171 176c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 220h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat3:"M1181 0h2l1171 236c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 280h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widehat4:"M1181 0h2l1171 296c6 0 10 5 10 11l-2 23c-1 6-5 10\n-11 10h-1L1182 67 15 340h-1c-6 0-10-4-11-10l-2-23c-1-6 4-11 10-11z",widecheck1:"M529,159h5l519,-115c5,-1,9,-5,9,-10c0,-1,-1,-2,-1,-3l-4,-22c-1,\n-5,-5,-9,-11,-9h-2l-512,92l-513,-92h-2c-5,0,-9,4,-11,9l-5,22c-1,6,2,12,8,13z",widecheck2:"M1181,220h2l1171,-176c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,153l-1167,-153h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck3:"M1181,280h2l1171,-236c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,213l-1167,-213h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",widecheck4:"M1181,340h2l1171,-296c6,0,10,-5,10,-11l-2,-23c-1,-6,-5,-10,\n-11,-10h-1l-1168,273l-1167,-273h-1c-6,0,-10,4,-11,10l-2,23c-1,6,4,11,10,11z",baraboveleftarrow:"M400000 620h-399890l3 -3c68.7 -52.7 113.7 -120 135 -202\nc4 -14.7 6 -23 6 -25c0 -7.3 -7 -11 -21 -11c-8 0 -13.2 0.8 -15.5 2.5\nc-2.3 1.7 -4.2 5.8 -5.5 12.5c-1.3 4.7 -2.7 10.3 -4 17c-12 48.7 -34.8 92 -68.5 130\ns-74.2 66.3 -121.5 85c-10 4 -16 7.7 -18 11c0 8.7 6 14.3 18 17c47.3 18.7 87.8 47\n121.5 85s56.5 81.3 68.5 130c0.7 2 1.3 5 2 9s1.2 6.7 1.5 8c0.3 1.3 1 3.3 2 6\ns2.2 4.5 3.5 5.5c1.3 1 3.3 1.8 6 2.5s6 1 10 1c14 0 21 -3.7 21 -11\nc0 -2 -2 -10.3 -6 -25c-20 -79.3 -65 -146.7 -135 -202l-3 -3h399890z\nM100 620v40h399900v-40z M0 241v40h399900v-40zM0 241v40h399900v-40z",rightarrowabovebar:"M0 241v40h399891c-47.3 35.3-84 78-110 128-16.7 32\n-27.7 63.7-33 95 0 1.3-.2 2.7-.5 4-.3 1.3-.5 2.3-.5 3 0 7.3 6.7 11 20 11 8 0\n13.2-.8 15.5-2.5 2.3-1.7 4.2-5.5 5.5-11.5 2-13.3 5.7-27 11-41 14.7-44.7 39\n-84.5 73-119.5s73.7-60.2 119-75.5c6-2 9-5.7 9-11s-3-9-9-11c-45.3-15.3-85-40.5\n-119-75.5s-58.3-74.8-73-119.5c-4.7-14-8.3-27.3-11-40-1.3-6.7-3.2-10.8-5.5\n-12.5-2.3-1.7-7.5-2.5-15.5-2.5-14 0-21 3.7-21 11 0 2 2 10.3 6 25 20.7 83.3 67\n151.7 139 205zm96 379h399894v40H0zm0 0h399904v40H0z",baraboveshortleftharpoon:"M507,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17\nc2,0.7,5,1,9,1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21\nc-32,-87.3,-82.7,-157.7,-152,-211c0,0,-3,-3,-3,-3l399351,0l0,-40\nc-398570,0,-399437,0,-399437,0z M593 435 v40 H399500 v-40z\nM0 281 v-40 H399908 v40z M0 281 v-40 H399908 v40z",rightharpoonaboveshortbar:"M0,241 l0,40c399126,0,399993,0,399993,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM0 241 v40 H399908 v-40z M0 475 v-40 H399500 v40z M0 475 v-40 H399500 v40z",shortbaraboveleftharpoon:"M7,435c-4,4,-6.3,8.7,-7,14c0,5.3,0.7,9,2,11\nc1.3,2,5.3,5.3,12,10c90.7,54,156,130,196,228c3.3,10.7,6.3,16.3,9,17c2,0.7,5,1,9,\n1c0,0,5,0,5,0c10.7,0,16.7,-2,18,-6c2,-2.7,1,-9.7,-3,-21c-32,-87.3,-82.7,-157.7,\n-152,-211c0,0,-3,-3,-3,-3l399907,0l0,-40c-399126,0,-399993,0,-399993,0z\nM93 435 v40 H400000 v-40z M500 241 v40 H400000 v-40z M500 241 v40 H400000 v-40z",shortrightharpoonabovebar:"M53,241l0,40c398570,0,399437,0,399437,0\nc4.7,-4.7,7,-9.3,7,-14c0,-9.3,-3.7,-15.3,-11,-18c-92.7,-56.7,-159,-133.7,-199,\n-231c-3.3,-9.3,-6,-14.7,-8,-16c-2,-1.3,-7,-2,-15,-2c-10.7,0,-16.7,2,-18,6\nc-2,2.7,-1,9.7,3,21c15.3,42,36.7,81.8,64,119.5c27.3,37.7,58,69.2,92,94.5z\nM500 241 v40 H399408 v-40z M500 435 v40 H400000 v-40z"},tallDelim=function(e,t){switch(e){case"lbrack":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v1759 h347 v-84\nH403z M403 1759 V0 H319 V1759 v"+t+" v1759 h84z";case"rbrack":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v1759 H0 v84 H347z\nM347 1759 V0 H263 V1759 v"+t+" v1759 h84z";case"vert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z";case"doublevert":return"M145 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M188 15 H145 v585 v"+t+" v585 h43z\nM367 15 v585 v"+t+" v585 c2.667,10,9.667,15,21,15\nc10,0,16.667,-5,20,-15 v-585 v"+-t+" v-585 c-2.667,-10,-9.667,-15,-21,-15\nc-10,0,-16.667,5,-20,15z M410 15 H367 v585 v"+t+" v585 h43z";case"lfloor":return"M319 602 V0 H403 V602 v"+t+" v1715 h263 v84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"rfloor":return"M319 602 V0 H403 V602 v"+t+" v1799 H0 v-84 H319z\nMM319 602 V0 H403 V602 v"+t+" v1715 H319z";case"lceil":return"M403 1759 V84 H666 V0 H319 V1759 v"+t+" v602 h84z\nM403 1759 V0 H319 V1759 v"+t+" v602 h84z";case"rceil":return"M347 1759 V0 H0 V84 H263 V1759 v"+t+" v602 h84z\nM347 1759 V0 h-84 V1759 v"+t+" v602 h84z";case"lparen":return"M863,9c0,-2,-2,-5,-6,-9c0,0,-17,0,-17,0c-12.7,0,-19.3,0.3,-20,1\nc-5.3,5.3,-10.3,11,-15,17c-242.7,294.7,-395.3,682,-458,1162c-21.3,163.3,-33.3,349,\n-36,557 l0,"+(t+84)+"c0.2,6,0,26,0,60c2,159.3,10,310.7,24,454c53.3,528,210,\n949.7,470,1265c4.7,6,9.7,11.7,15,17c0.7,0.7,7,1,19,1c0,0,18,0,18,0c4,-4,6,-7,6,-9\nc0,-2.7,-3.3,-8.7,-10,-18c-135.3,-192.7,-235.5,-414.3,-300.5,-665c-65,-250.7,-102.5,\n-544.7,-112.5,-882c-2,-104,-3,-167,-3,-189\nl0,-"+(t+92)+"c0,-162.7,5.7,-314,17,-454c20.7,-272,63.7,-513,129,-723c65.3,\n-210,155.3,-396.3,270,-559c6.7,-9.3,10,-15.3,10,-18z";case"rparen":return"M76,0c-16.7,0,-25,3,-25,9c0,2,2,6.3,6,13c21.3,28.7,42.3,60.3,\n63,95c96.7,156.7,172.8,332.5,228.5,527.5c55.7,195,92.8,416.5,111.5,664.5\nc11.3,139.3,17,290.7,17,454c0,28,1.7,43,3.3,45l0,"+(t+9)+"\nc-3,4,-3.3,16.7,-3.3,38c0,162,-5.7,313.7,-17,455c-18.7,248,-55.8,469.3,-111.5,664\nc-55.7,194.7,-131.8,370.3,-228.5,527c-20.7,34.7,-41.7,66.3,-63,95c-2,3.3,-4,7,-6,11\nc0,7.3,5.7,11,17,11c0,0,11,0,11,0c9.3,0,14.3,-0.3,15,-1c5.3,-5.3,10.3,-11,15,-17\nc242.7,-294.7,395.3,-681.7,458,-1161c21.3,-164.7,33.3,-350.7,36,-558\nl0,-"+(t+144)+"c-2,-159.3,-10,-310.7,-24,-454c-53.3,-528,-210,-949.7,\n-470,-1265c-4.7,-6,-9.7,-11.7,-15,-17c-0.7,-0.7,-6.7,-1,-18,-1z";default:throw Error("Unknown stretchy delimiter.")}};let DocumentFragment=class DocumentFragment{constructor(e){this.children=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.children=e,this.classes=[],this.height=0,this.depth=0,this.maxFontSize=0,this.style={}}hasClass(e){return s.contains(this.classes,e)}toNode(){for(var e=document.createDocumentFragment(),t=0;t<this.children.length;t++)e.appendChild(this.children[t].toNode());return e}toMarkup(){for(var e="",t=0;t<this.children.length;t++)e+=this.children[t].toMarkup();return e}toText(){return this.children.map(e=>e.toText()).join("")}};var x={"AMS-Regular":{32:[0,0,0,0,.25],65:[0,.68889,0,0,.72222],66:[0,.68889,0,0,.66667],67:[0,.68889,0,0,.72222],68:[0,.68889,0,0,.72222],69:[0,.68889,0,0,.66667],70:[0,.68889,0,0,.61111],71:[0,.68889,0,0,.77778],72:[0,.68889,0,0,.77778],73:[0,.68889,0,0,.38889],74:[.16667,.68889,0,0,.5],75:[0,.68889,0,0,.77778],76:[0,.68889,0,0,.66667],77:[0,.68889,0,0,.94445],78:[0,.68889,0,0,.72222],79:[.16667,.68889,0,0,.77778],80:[0,.68889,0,0,.61111],81:[.16667,.68889,0,0,.77778],82:[0,.68889,0,0,.72222],83:[0,.68889,0,0,.55556],84:[0,.68889,0,0,.66667],85:[0,.68889,0,0,.72222],86:[0,.68889,0,0,.72222],87:[0,.68889,0,0,1],88:[0,.68889,0,0,.72222],89:[0,.68889,0,0,.72222],90:[0,.68889,0,0,.66667],107:[0,.68889,0,0,.55556],160:[0,0,0,0,.25],165:[0,.675,.025,0,.75],174:[.15559,.69224,0,0,.94666],240:[0,.68889,0,0,.55556],295:[0,.68889,0,0,.54028],710:[0,.825,0,0,2.33334],732:[0,.9,0,0,2.33334],770:[0,.825,0,0,2.33334],771:[0,.9,0,0,2.33334],989:[.08167,.58167,0,0,.77778],1008:[0,.43056,.04028,0,.66667],8245:[0,.54986,0,0,.275],8463:[0,.68889,0,0,.54028],8487:[0,.68889,0,0,.72222],8498:[0,.68889,0,0,.55556],8502:[0,.68889,0,0,.66667],8503:[0,.68889,0,0,.44445],8504:[0,.68889,0,0,.66667],8513:[0,.68889,0,0,.63889],8592:[-.03598,.46402,0,0,.5],8594:[-.03598,.46402,0,0,.5],8602:[-.13313,.36687,0,0,1],8603:[-.13313,.36687,0,0,1],8606:[.01354,.52239,0,0,1],8608:[.01354,.52239,0,0,1],8610:[.01354,.52239,0,0,1.11111],8611:[.01354,.52239,0,0,1.11111],8619:[0,.54986,0,0,1],8620:[0,.54986,0,0,1],8621:[-.13313,.37788,0,0,1.38889],8622:[-.13313,.36687,0,0,1],8624:[0,.69224,0,0,.5],8625:[0,.69224,0,0,.5],8630:[0,.43056,0,0,1],8631:[0,.43056,0,0,1],8634:[.08198,.58198,0,0,.77778],8635:[.08198,.58198,0,0,.77778],8638:[.19444,.69224,0,0,.41667],8639:[.19444,.69224,0,0,.41667],8642:[.19444,.69224,0,0,.41667],8643:[.19444,.69224,0,0,.41667],8644:[.1808,.675,0,0,1],8646:[.1808,.675,0,0,1],8647:[.1808,.675,0,0,1],8648:[.19444,.69224,0,0,.83334],8649:[.1808,.675,0,0,1],8650:[.19444,.69224,0,0,.83334],8651:[.01354,.52239,0,0,1],8652:[.01354,.52239,0,0,1],8653:[-.13313,.36687,0,0,1],8654:[-.13313,.36687,0,0,1],8655:[-.13313,.36687,0,0,1],8666:[.13667,.63667,0,0,1],8667:[.13667,.63667,0,0,1],8669:[-.13313,.37788,0,0,1],8672:[-.064,.437,0,0,1.334],8674:[-.064,.437,0,0,1.334],8705:[0,.825,0,0,.5],8708:[0,.68889,0,0,.55556],8709:[.08167,.58167,0,0,.77778],8717:[0,.43056,0,0,.42917],8722:[-.03598,.46402,0,0,.5],8724:[.08198,.69224,0,0,.77778],8726:[.08167,.58167,0,0,.77778],8733:[0,.69224,0,0,.77778],8736:[0,.69224,0,0,.72222],8737:[0,.69224,0,0,.72222],8738:[.03517,.52239,0,0,.72222],8739:[.08167,.58167,0,0,.22222],8740:[.25142,.74111,0,0,.27778],8741:[.08167,.58167,0,0,.38889],8742:[.25142,.74111,0,0,.5],8756:[0,.69224,0,0,.66667],8757:[0,.69224,0,0,.66667],8764:[-.13313,.36687,0,0,.77778],8765:[-.13313,.37788,0,0,.77778],8769:[-.13313,.36687,0,0,.77778],8770:[-.03625,.46375,0,0,.77778],8774:[.30274,.79383,0,0,.77778],8776:[-.01688,.48312,0,0,.77778],8778:[.08167,.58167,0,0,.77778],8782:[.06062,.54986,0,0,.77778],8783:[.06062,.54986,0,0,.77778],8785:[.08198,.58198,0,0,.77778],8786:[.08198,.58198,0,0,.77778],8787:[.08198,.58198,0,0,.77778],8790:[0,.69224,0,0,.77778],8791:[.22958,.72958,0,0,.77778],8796:[.08198,.91667,0,0,.77778],8806:[.25583,.75583,0,0,.77778],8807:[.25583,.75583,0,0,.77778],8808:[.25142,.75726,0,0,.77778],8809:[.25142,.75726,0,0,.77778],8812:[.25583,.75583,0,0,.5],8814:[.20576,.70576,0,0,.77778],8815:[.20576,.70576,0,0,.77778],8816:[.30274,.79383,0,0,.77778],8817:[.30274,.79383,0,0,.77778],8818:[.22958,.72958,0,0,.77778],8819:[.22958,.72958,0,0,.77778],8822:[.1808,.675,0,0,.77778],8823:[.1808,.675,0,0,.77778],8828:[.13667,.63667,0,0,.77778],8829:[.13667,.63667,0,0,.77778],8830:[.22958,.72958,0,0,.77778],8831:[.22958,.72958,0,0,.77778],8832:[.20576,.70576,0,0,.77778],8833:[.20576,.70576,0,0,.77778],8840:[.30274,.79383,0,0,.77778],8841:[.30274,.79383,0,0,.77778],8842:[.13597,.63597,0,0,.77778],8843:[.13597,.63597,0,0,.77778],8847:[.03517,.54986,0,0,.77778],8848:[.03517,.54986,0,0,.77778],8858:[.08198,.58198,0,0,.77778],8859:[.08198,.58198,0,0,.77778],8861:[.08198,.58198,0,0,.77778],8862:[0,.675,0,0,.77778],8863:[0,.675,0,0,.77778],8864:[0,.675,0,0,.77778],8865:[0,.675,0,0,.77778],8872:[0,.69224,0,0,.61111],8873:[0,.69224,0,0,.72222],8874:[0,.69224,0,0,.88889],8876:[0,.68889,0,0,.61111],8877:[0,.68889,0,0,.61111],8878:[0,.68889,0,0,.72222],8879:[0,.68889,0,0,.72222],8882:[.03517,.54986,0,0,.77778],8883:[.03517,.54986,0,0,.77778],8884:[.13667,.63667,0,0,.77778],8885:[.13667,.63667,0,0,.77778],8888:[0,.54986,0,0,1.11111],8890:[.19444,.43056,0,0,.55556],8891:[.19444,.69224,0,0,.61111],8892:[.19444,.69224,0,0,.61111],8901:[0,.54986,0,0,.27778],8903:[.08167,.58167,0,0,.77778],8905:[.08167,.58167,0,0,.77778],8906:[.08167,.58167,0,0,.77778],8907:[0,.69224,0,0,.77778],8908:[0,.69224,0,0,.77778],8909:[-.03598,.46402,0,0,.77778],8910:[0,.54986,0,0,.76042],8911:[0,.54986,0,0,.76042],8912:[.03517,.54986,0,0,.77778],8913:[.03517,.54986,0,0,.77778],8914:[0,.54986,0,0,.66667],8915:[0,.54986,0,0,.66667],8916:[0,.69224,0,0,.66667],8918:[.0391,.5391,0,0,.77778],8919:[.0391,.5391,0,0,.77778],8920:[.03517,.54986,0,0,1.33334],8921:[.03517,.54986,0,0,1.33334],8922:[.38569,.88569,0,0,.77778],8923:[.38569,.88569,0,0,.77778],8926:[.13667,.63667,0,0,.77778],8927:[.13667,.63667,0,0,.77778],8928:[.30274,.79383,0,0,.77778],8929:[.30274,.79383,0,0,.77778],8934:[.23222,.74111,0,0,.77778],8935:[.23222,.74111,0,0,.77778],8936:[.23222,.74111,0,0,.77778],8937:[.23222,.74111,0,0,.77778],8938:[.20576,.70576,0,0,.77778],8939:[.20576,.70576,0,0,.77778],8940:[.30274,.79383,0,0,.77778],8941:[.30274,.79383,0,0,.77778],8994:[.19444,.69224,0,0,.77778],8995:[.19444,.69224,0,0,.77778],9416:[.15559,.69224,0,0,.90222],9484:[0,.69224,0,0,.5],9488:[0,.69224,0,0,.5],9492:[0,.37788,0,0,.5],9496:[0,.37788,0,0,.5],9585:[.19444,.68889,0,0,.88889],9586:[.19444,.74111,0,0,.88889],9632:[0,.675,0,0,.77778],9633:[0,.675,0,0,.77778],9650:[0,.54986,0,0,.72222],9651:[0,.54986,0,0,.72222],9654:[.03517,.54986,0,0,.77778],9660:[0,.54986,0,0,.72222],9661:[0,.54986,0,0,.72222],9664:[.03517,.54986,0,0,.77778],9674:[.11111,.69224,0,0,.66667],9733:[.19444,.69224,0,0,.94445],10003:[0,.69224,0,0,.83334],10016:[0,.69224,0,0,.83334],10731:[.11111,.69224,0,0,.66667],10846:[.19444,.75583,0,0,.61111],10877:[.13667,.63667,0,0,.77778],10878:[.13667,.63667,0,0,.77778],10885:[.25583,.75583,0,0,.77778],10886:[.25583,.75583,0,0,.77778],10887:[.13597,.63597,0,0,.77778],10888:[.13597,.63597,0,0,.77778],10889:[.26167,.75726,0,0,.77778],10890:[.26167,.75726,0,0,.77778],10891:[.48256,.98256,0,0,.77778],10892:[.48256,.98256,0,0,.77778],10901:[.13667,.63667,0,0,.77778],10902:[.13667,.63667,0,0,.77778],10933:[.25142,.75726,0,0,.77778],10934:[.25142,.75726,0,0,.77778],10935:[.26167,.75726,0,0,.77778],10936:[.26167,.75726,0,0,.77778],10937:[.26167,.75726,0,0,.77778],10938:[.26167,.75726,0,0,.77778],10949:[.25583,.75583,0,0,.77778],10950:[.25583,.75583,0,0,.77778],10955:[.28481,.79383,0,0,.77778],10956:[.28481,.79383,0,0,.77778],57350:[.08167,.58167,0,0,.22222],57351:[.08167,.58167,0,0,.38889],57352:[.08167,.58167,0,0,.77778],57353:[0,.43056,.04028,0,.66667],57356:[.25142,.75726,0,0,.77778],57357:[.25142,.75726,0,0,.77778],57358:[.41951,.91951,0,0,.77778],57359:[.30274,.79383,0,0,.77778],57360:[.30274,.79383,0,0,.77778],57361:[.41951,.91951,0,0,.77778],57366:[.25142,.75726,0,0,.77778],57367:[.25142,.75726,0,0,.77778],57368:[.25142,.75726,0,0,.77778],57369:[.25142,.75726,0,0,.77778],57370:[.13597,.63597,0,0,.77778],57371:[.13597,.63597,0,0,.77778]},"Caligraphic-Regular":{32:[0,0,0,0,.25],65:[0,.68333,0,.19445,.79847],66:[0,.68333,.03041,.13889,.65681],67:[0,.68333,.05834,.13889,.52653],68:[0,.68333,.02778,.08334,.77139],69:[0,.68333,.08944,.11111,.52778],70:[0,.68333,.09931,.11111,.71875],71:[.09722,.68333,.0593,.11111,.59487],72:[0,.68333,.00965,.11111,.84452],73:[0,.68333,.07382,0,.54452],74:[.09722,.68333,.18472,.16667,.67778],75:[0,.68333,.01445,.05556,.76195],76:[0,.68333,0,.13889,.68972],77:[0,.68333,0,.13889,1.2009],78:[0,.68333,.14736,.08334,.82049],79:[0,.68333,.02778,.11111,.79611],80:[0,.68333,.08222,.08334,.69556],81:[.09722,.68333,0,.11111,.81667],82:[0,.68333,0,.08334,.8475],83:[0,.68333,.075,.13889,.60556],84:[0,.68333,.25417,0,.54464],85:[0,.68333,.09931,.08334,.62583],86:[0,.68333,.08222,0,.61278],87:[0,.68333,.08222,.08334,.98778],88:[0,.68333,.14643,.13889,.7133],89:[.09722,.68333,.08222,.08334,.66834],90:[0,.68333,.07944,.13889,.72473],160:[0,0,0,0,.25]},"Fraktur-Regular":{32:[0,0,0,0,.25],33:[0,.69141,0,0,.29574],34:[0,.69141,0,0,.21471],38:[0,.69141,0,0,.73786],39:[0,.69141,0,0,.21201],40:[.24982,.74947,0,0,.38865],41:[.24982,.74947,0,0,.38865],42:[0,.62119,0,0,.27764],43:[.08319,.58283,0,0,.75623],44:[0,.10803,0,0,.27764],45:[.08319,.58283,0,0,.75623],46:[0,.10803,0,0,.27764],47:[.24982,.74947,0,0,.50181],48:[0,.47534,0,0,.50181],49:[0,.47534,0,0,.50181],50:[0,.47534,0,0,.50181],51:[.18906,.47534,0,0,.50181],52:[.18906,.47534,0,0,.50181],53:[.18906,.47534,0,0,.50181],54:[0,.69141,0,0,.50181],55:[.18906,.47534,0,0,.50181],56:[0,.69141,0,0,.50181],57:[.18906,.47534,0,0,.50181],58:[0,.47534,0,0,.21606],59:[.12604,.47534,0,0,.21606],61:[-.13099,.36866,0,0,.75623],63:[0,.69141,0,0,.36245],65:[0,.69141,0,0,.7176],66:[0,.69141,0,0,.88397],67:[0,.69141,0,0,.61254],68:[0,.69141,0,0,.83158],69:[0,.69141,0,0,.66278],70:[.12604,.69141,0,0,.61119],71:[0,.69141,0,0,.78539],72:[.06302,.69141,0,0,.7203],73:[0,.69141,0,0,.55448],74:[.12604,.69141,0,0,.55231],75:[0,.69141,0,0,.66845],76:[0,.69141,0,0,.66602],77:[0,.69141,0,0,1.04953],78:[0,.69141,0,0,.83212],79:[0,.69141,0,0,.82699],80:[.18906,.69141,0,0,.82753],81:[.03781,.69141,0,0,.82699],82:[0,.69141,0,0,.82807],83:[0,.69141,0,0,.82861],84:[0,.69141,0,0,.66899],85:[0,.69141,0,0,.64576],86:[0,.69141,0,0,.83131],87:[0,.69141,0,0,1.04602],88:[0,.69141,0,0,.71922],89:[.18906,.69141,0,0,.83293],90:[.12604,.69141,0,0,.60201],91:[.24982,.74947,0,0,.27764],93:[.24982,.74947,0,0,.27764],94:[0,.69141,0,0,.49965],97:[0,.47534,0,0,.50046],98:[0,.69141,0,0,.51315],99:[0,.47534,0,0,.38946],100:[0,.62119,0,0,.49857],101:[0,.47534,0,0,.40053],102:[.18906,.69141,0,0,.32626],103:[.18906,.47534,0,0,.5037],104:[.18906,.69141,0,0,.52126],105:[0,.69141,0,0,.27899],106:[0,.69141,0,0,.28088],107:[0,.69141,0,0,.38946],108:[0,.69141,0,0,.27953],109:[0,.47534,0,0,.76676],110:[0,.47534,0,0,.52666],111:[0,.47534,0,0,.48885],112:[.18906,.52396,0,0,.50046],113:[.18906,.47534,0,0,.48912],114:[0,.47534,0,0,.38919],115:[0,.47534,0,0,.44266],116:[0,.62119,0,0,.33301],117:[0,.47534,0,0,.5172],118:[0,.52396,0,0,.5118],119:[0,.52396,0,0,.77351],120:[.18906,.47534,0,0,.38865],121:[.18906,.47534,0,0,.49884],122:[.18906,.47534,0,0,.39054],160:[0,0,0,0,.25],8216:[0,.69141,0,0,.21471],8217:[0,.69141,0,0,.21471],58112:[0,.62119,0,0,.49749],58113:[0,.62119,0,0,.4983],58114:[.18906,.69141,0,0,.33328],58115:[.18906,.69141,0,0,.32923],58116:[.18906,.47534,0,0,.50343],58117:[0,.69141,0,0,.33301],58118:[0,.62119,0,0,.33409],58119:[0,.47534,0,0,.50073]},"Main-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.35],34:[0,.69444,0,0,.60278],35:[.19444,.69444,0,0,.95833],36:[.05556,.75,0,0,.575],37:[.05556,.75,0,0,.95833],38:[0,.69444,0,0,.89444],39:[0,.69444,0,0,.31944],40:[.25,.75,0,0,.44722],41:[.25,.75,0,0,.44722],42:[0,.75,0,0,.575],43:[.13333,.63333,0,0,.89444],44:[.19444,.15556,0,0,.31944],45:[0,.44444,0,0,.38333],46:[0,.15556,0,0,.31944],47:[.25,.75,0,0,.575],48:[0,.64444,0,0,.575],49:[0,.64444,0,0,.575],50:[0,.64444,0,0,.575],51:[0,.64444,0,0,.575],52:[0,.64444,0,0,.575],53:[0,.64444,0,0,.575],54:[0,.64444,0,0,.575],55:[0,.64444,0,0,.575],56:[0,.64444,0,0,.575],57:[0,.64444,0,0,.575],58:[0,.44444,0,0,.31944],59:[.19444,.44444,0,0,.31944],60:[.08556,.58556,0,0,.89444],61:[-.10889,.39111,0,0,.89444],62:[.08556,.58556,0,0,.89444],63:[0,.69444,0,0,.54305],64:[0,.69444,0,0,.89444],65:[0,.68611,0,0,.86944],66:[0,.68611,0,0,.81805],67:[0,.68611,0,0,.83055],68:[0,.68611,0,0,.88194],69:[0,.68611,0,0,.75555],70:[0,.68611,0,0,.72361],71:[0,.68611,0,0,.90416],72:[0,.68611,0,0,.9],73:[0,.68611,0,0,.43611],74:[0,.68611,0,0,.59444],75:[0,.68611,0,0,.90138],76:[0,.68611,0,0,.69166],77:[0,.68611,0,0,1.09166],78:[0,.68611,0,0,.9],79:[0,.68611,0,0,.86388],80:[0,.68611,0,0,.78611],81:[.19444,.68611,0,0,.86388],82:[0,.68611,0,0,.8625],83:[0,.68611,0,0,.63889],84:[0,.68611,0,0,.8],85:[0,.68611,0,0,.88472],86:[0,.68611,.01597,0,.86944],87:[0,.68611,.01597,0,1.18888],88:[0,.68611,0,0,.86944],89:[0,.68611,.02875,0,.86944],90:[0,.68611,0,0,.70277],91:[.25,.75,0,0,.31944],92:[.25,.75,0,0,.575],93:[.25,.75,0,0,.31944],94:[0,.69444,0,0,.575],95:[.31,.13444,.03194,0,.575],97:[0,.44444,0,0,.55902],98:[0,.69444,0,0,.63889],99:[0,.44444,0,0,.51111],100:[0,.69444,0,0,.63889],101:[0,.44444,0,0,.52708],102:[0,.69444,.10903,0,.35139],103:[.19444,.44444,.01597,0,.575],104:[0,.69444,0,0,.63889],105:[0,.69444,0,0,.31944],106:[.19444,.69444,0,0,.35139],107:[0,.69444,0,0,.60694],108:[0,.69444,0,0,.31944],109:[0,.44444,0,0,.95833],110:[0,.44444,0,0,.63889],111:[0,.44444,0,0,.575],112:[.19444,.44444,0,0,.63889],113:[.19444,.44444,0,0,.60694],114:[0,.44444,0,0,.47361],115:[0,.44444,0,0,.45361],116:[0,.63492,0,0,.44722],117:[0,.44444,0,0,.63889],118:[0,.44444,.01597,0,.60694],119:[0,.44444,.01597,0,.83055],120:[0,.44444,0,0,.60694],121:[.19444,.44444,.01597,0,.60694],122:[0,.44444,0,0,.51111],123:[.25,.75,0,0,.575],124:[.25,.75,0,0,.31944],125:[.25,.75,0,0,.575],126:[.35,.34444,0,0,.575],160:[0,0,0,0,.25],163:[0,.69444,0,0,.86853],168:[0,.69444,0,0,.575],172:[0,.44444,0,0,.76666],176:[0,.69444,0,0,.86944],177:[.13333,.63333,0,0,.89444],184:[.17014,0,0,0,.51111],198:[0,.68611,0,0,1.04166],215:[.13333,.63333,0,0,.89444],216:[.04861,.73472,0,0,.89444],223:[0,.69444,0,0,.59722],230:[0,.44444,0,0,.83055],247:[.13333,.63333,0,0,.89444],248:[.09722,.54167,0,0,.575],305:[0,.44444,0,0,.31944],338:[0,.68611,0,0,1.16944],339:[0,.44444,0,0,.89444],567:[.19444,.44444,0,0,.35139],710:[0,.69444,0,0,.575],711:[0,.63194,0,0,.575],713:[0,.59611,0,0,.575],714:[0,.69444,0,0,.575],715:[0,.69444,0,0,.575],728:[0,.69444,0,0,.575],729:[0,.69444,0,0,.31944],730:[0,.69444,0,0,.86944],732:[0,.69444,0,0,.575],733:[0,.69444,0,0,.575],915:[0,.68611,0,0,.69166],916:[0,.68611,0,0,.95833],920:[0,.68611,0,0,.89444],923:[0,.68611,0,0,.80555],926:[0,.68611,0,0,.76666],928:[0,.68611,0,0,.9],931:[0,.68611,0,0,.83055],933:[0,.68611,0,0,.89444],934:[0,.68611,0,0,.83055],936:[0,.68611,0,0,.89444],937:[0,.68611,0,0,.83055],8211:[0,.44444,.03194,0,.575],8212:[0,.44444,.03194,0,1.14999],8216:[0,.69444,0,0,.31944],8217:[0,.69444,0,0,.31944],8220:[0,.69444,0,0,.60278],8221:[0,.69444,0,0,.60278],8224:[.19444,.69444,0,0,.51111],8225:[.19444,.69444,0,0,.51111],8242:[0,.55556,0,0,.34444],8407:[0,.72444,.15486,0,.575],8463:[0,.69444,0,0,.66759],8465:[0,.69444,0,0,.83055],8467:[0,.69444,0,0,.47361],8472:[.19444,.44444,0,0,.74027],8476:[0,.69444,0,0,.83055],8501:[0,.69444,0,0,.70277],8592:[-.10889,.39111,0,0,1.14999],8593:[.19444,.69444,0,0,.575],8594:[-.10889,.39111,0,0,1.14999],8595:[.19444,.69444,0,0,.575],8596:[-.10889,.39111,0,0,1.14999],8597:[.25,.75,0,0,.575],8598:[.19444,.69444,0,0,1.14999],8599:[.19444,.69444,0,0,1.14999],8600:[.19444,.69444,0,0,1.14999],8601:[.19444,.69444,0,0,1.14999],8636:[-.10889,.39111,0,0,1.14999],8637:[-.10889,.39111,0,0,1.14999],8640:[-.10889,.39111,0,0,1.14999],8641:[-.10889,.39111,0,0,1.14999],8656:[-.10889,.39111,0,0,1.14999],8657:[.19444,.69444,0,0,.70277],8658:[-.10889,.39111,0,0,1.14999],8659:[.19444,.69444,0,0,.70277],8660:[-.10889,.39111,0,0,1.14999],8661:[.25,.75,0,0,.70277],8704:[0,.69444,0,0,.63889],8706:[0,.69444,.06389,0,.62847],8707:[0,.69444,0,0,.63889],8709:[.05556,.75,0,0,.575],8711:[0,.68611,0,0,.95833],8712:[.08556,.58556,0,0,.76666],8715:[.08556,.58556,0,0,.76666],8722:[.13333,.63333,0,0,.89444],8723:[.13333,.63333,0,0,.89444],8725:[.25,.75,0,0,.575],8726:[.25,.75,0,0,.575],8727:[-.02778,.47222,0,0,.575],8728:[-.02639,.47361,0,0,.575],8729:[-.02639,.47361,0,0,.575],8730:[.18,.82,0,0,.95833],8733:[0,.44444,0,0,.89444],8734:[0,.44444,0,0,1.14999],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.31944],8741:[.25,.75,0,0,.575],8743:[0,.55556,0,0,.76666],8744:[0,.55556,0,0,.76666],8745:[0,.55556,0,0,.76666],8746:[0,.55556,0,0,.76666],8747:[.19444,.69444,.12778,0,.56875],8764:[-.10889,.39111,0,0,.89444],8768:[.19444,.69444,0,0,.31944],8771:[.00222,.50222,0,0,.89444],8773:[.027,.638,0,0,.894],8776:[.02444,.52444,0,0,.89444],8781:[.00222,.50222,0,0,.89444],8801:[.00222,.50222,0,0,.89444],8804:[.19667,.69667,0,0,.89444],8805:[.19667,.69667,0,0,.89444],8810:[.08556,.58556,0,0,1.14999],8811:[.08556,.58556,0,0,1.14999],8826:[.08556,.58556,0,0,.89444],8827:[.08556,.58556,0,0,.89444],8834:[.08556,.58556,0,0,.89444],8835:[.08556,.58556,0,0,.89444],8838:[.19667,.69667,0,0,.89444],8839:[.19667,.69667,0,0,.89444],8846:[0,.55556,0,0,.76666],8849:[.19667,.69667,0,0,.89444],8850:[.19667,.69667,0,0,.89444],8851:[0,.55556,0,0,.76666],8852:[0,.55556,0,0,.76666],8853:[.13333,.63333,0,0,.89444],8854:[.13333,.63333,0,0,.89444],8855:[.13333,.63333,0,0,.89444],8856:[.13333,.63333,0,0,.89444],8857:[.13333,.63333,0,0,.89444],8866:[0,.69444,0,0,.70277],8867:[0,.69444,0,0,.70277],8868:[0,.69444,0,0,.89444],8869:[0,.69444,0,0,.89444],8900:[-.02639,.47361,0,0,.575],8901:[-.02639,.47361,0,0,.31944],8902:[-.02778,.47222,0,0,.575],8968:[.25,.75,0,0,.51111],8969:[.25,.75,0,0,.51111],8970:[.25,.75,0,0,.51111],8971:[.25,.75,0,0,.51111],8994:[-.13889,.36111,0,0,1.14999],8995:[-.13889,.36111,0,0,1.14999],9651:[.19444,.69444,0,0,1.02222],9657:[-.02778,.47222,0,0,.575],9661:[.19444,.69444,0,0,1.02222],9667:[-.02778,.47222,0,0,.575],9711:[.19444,.69444,0,0,1.14999],9824:[.12963,.69444,0,0,.89444],9825:[.12963,.69444,0,0,.89444],9826:[.12963,.69444,0,0,.89444],9827:[.12963,.69444,0,0,.89444],9837:[0,.75,0,0,.44722],9838:[.19444,.69444,0,0,.44722],9839:[.19444,.69444,0,0,.44722],10216:[.25,.75,0,0,.44722],10217:[.25,.75,0,0,.44722],10815:[0,.68611,0,0,.9],10927:[.19667,.69667,0,0,.89444],10928:[.19667,.69667,0,0,.89444],57376:[.19444,.69444,0,0,0]},"Main-BoldItalic":{32:[0,0,0,0,.25],33:[0,.69444,.11417,0,.38611],34:[0,.69444,.07939,0,.62055],35:[.19444,.69444,.06833,0,.94444],37:[.05556,.75,.12861,0,.94444],38:[0,.69444,.08528,0,.88555],39:[0,.69444,.12945,0,.35555],40:[.25,.75,.15806,0,.47333],41:[.25,.75,.03306,0,.47333],42:[0,.75,.14333,0,.59111],43:[.10333,.60333,.03306,0,.88555],44:[.19444,.14722,0,0,.35555],45:[0,.44444,.02611,0,.41444],46:[0,.14722,0,0,.35555],47:[.25,.75,.15806,0,.59111],48:[0,.64444,.13167,0,.59111],49:[0,.64444,.13167,0,.59111],50:[0,.64444,.13167,0,.59111],51:[0,.64444,.13167,0,.59111],52:[.19444,.64444,.13167,0,.59111],53:[0,.64444,.13167,0,.59111],54:[0,.64444,.13167,0,.59111],55:[.19444,.64444,.13167,0,.59111],56:[0,.64444,.13167,0,.59111],57:[0,.64444,.13167,0,.59111],58:[0,.44444,.06695,0,.35555],59:[.19444,.44444,.06695,0,.35555],61:[-.10889,.39111,.06833,0,.88555],63:[0,.69444,.11472,0,.59111],64:[0,.69444,.09208,0,.88555],65:[0,.68611,0,0,.86555],66:[0,.68611,.0992,0,.81666],67:[0,.68611,.14208,0,.82666],68:[0,.68611,.09062,0,.87555],69:[0,.68611,.11431,0,.75666],70:[0,.68611,.12903,0,.72722],71:[0,.68611,.07347,0,.89527],72:[0,.68611,.17208,0,.8961],73:[0,.68611,.15681,0,.47166],74:[0,.68611,.145,0,.61055],75:[0,.68611,.14208,0,.89499],76:[0,.68611,0,0,.69777],77:[0,.68611,.17208,0,1.07277],78:[0,.68611,.17208,0,.8961],79:[0,.68611,.09062,0,.85499],80:[0,.68611,.0992,0,.78721],81:[.19444,.68611,.09062,0,.85499],82:[0,.68611,.02559,0,.85944],83:[0,.68611,.11264,0,.64999],84:[0,.68611,.12903,0,.7961],85:[0,.68611,.17208,0,.88083],86:[0,.68611,.18625,0,.86555],87:[0,.68611,.18625,0,1.15999],88:[0,.68611,.15681,0,.86555],89:[0,.68611,.19803,0,.86555],90:[0,.68611,.14208,0,.70888],91:[.25,.75,.1875,0,.35611],93:[.25,.75,.09972,0,.35611],94:[0,.69444,.06709,0,.59111],95:[.31,.13444,.09811,0,.59111],97:[0,.44444,.09426,0,.59111],98:[0,.69444,.07861,0,.53222],99:[0,.44444,.05222,0,.53222],100:[0,.69444,.10861,0,.59111],101:[0,.44444,.085,0,.53222],102:[.19444,.69444,.21778,0,.4],103:[.19444,.44444,.105,0,.53222],104:[0,.69444,.09426,0,.59111],105:[0,.69326,.11387,0,.35555],106:[.19444,.69326,.1672,0,.35555],107:[0,.69444,.11111,0,.53222],108:[0,.69444,.10861,0,.29666],109:[0,.44444,.09426,0,.94444],110:[0,.44444,.09426,0,.64999],111:[0,.44444,.07861,0,.59111],112:[.19444,.44444,.07861,0,.59111],113:[.19444,.44444,.105,0,.53222],114:[0,.44444,.11111,0,.50167],115:[0,.44444,.08167,0,.48694],116:[0,.63492,.09639,0,.385],117:[0,.44444,.09426,0,.62055],118:[0,.44444,.11111,0,.53222],119:[0,.44444,.11111,0,.76777],120:[0,.44444,.12583,0,.56055],121:[.19444,.44444,.105,0,.56166],122:[0,.44444,.13889,0,.49055],126:[.35,.34444,.11472,0,.59111],160:[0,0,0,0,.25],168:[0,.69444,.11473,0,.59111],176:[0,.69444,0,0,.94888],184:[.17014,0,0,0,.53222],198:[0,.68611,.11431,0,1.02277],216:[.04861,.73472,.09062,0,.88555],223:[.19444,.69444,.09736,0,.665],230:[0,.44444,.085,0,.82666],248:[.09722,.54167,.09458,0,.59111],305:[0,.44444,.09426,0,.35555],338:[0,.68611,.11431,0,1.14054],339:[0,.44444,.085,0,.82666],567:[.19444,.44444,.04611,0,.385],710:[0,.69444,.06709,0,.59111],711:[0,.63194,.08271,0,.59111],713:[0,.59444,.10444,0,.59111],714:[0,.69444,.08528,0,.59111],715:[0,.69444,0,0,.59111],728:[0,.69444,.10333,0,.59111],729:[0,.69444,.12945,0,.35555],730:[0,.69444,0,0,.94888],732:[0,.69444,.11472,0,.59111],733:[0,.69444,.11472,0,.59111],915:[0,.68611,.12903,0,.69777],916:[0,.68611,0,0,.94444],920:[0,.68611,.09062,0,.88555],923:[0,.68611,0,0,.80666],926:[0,.68611,.15092,0,.76777],928:[0,.68611,.17208,0,.8961],931:[0,.68611,.11431,0,.82666],933:[0,.68611,.10778,0,.88555],934:[0,.68611,.05632,0,.82666],936:[0,.68611,.10778,0,.88555],937:[0,.68611,.0992,0,.82666],8211:[0,.44444,.09811,0,.59111],8212:[0,.44444,.09811,0,1.18221],8216:[0,.69444,.12945,0,.35555],8217:[0,.69444,.12945,0,.35555],8220:[0,.69444,.16772,0,.62055],8221:[0,.69444,.07939,0,.62055]},"Main-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.12417,0,.30667],34:[0,.69444,.06961,0,.51444],35:[.19444,.69444,.06616,0,.81777],37:[.05556,.75,.13639,0,.81777],38:[0,.69444,.09694,0,.76666],39:[0,.69444,.12417,0,.30667],40:[.25,.75,.16194,0,.40889],41:[.25,.75,.03694,0,.40889],42:[0,.75,.14917,0,.51111],43:[.05667,.56167,.03694,0,.76666],44:[.19444,.10556,0,0,.30667],45:[0,.43056,.02826,0,.35778],46:[0,.10556,0,0,.30667],47:[.25,.75,.16194,0,.51111],48:[0,.64444,.13556,0,.51111],49:[0,.64444,.13556,0,.51111],50:[0,.64444,.13556,0,.51111],51:[0,.64444,.13556,0,.51111],52:[.19444,.64444,.13556,0,.51111],53:[0,.64444,.13556,0,.51111],54:[0,.64444,.13556,0,.51111],55:[.19444,.64444,.13556,0,.51111],56:[0,.64444,.13556,0,.51111],57:[0,.64444,.13556,0,.51111],58:[0,.43056,.0582,0,.30667],59:[.19444,.43056,.0582,0,.30667],61:[-.13313,.36687,.06616,0,.76666],63:[0,.69444,.1225,0,.51111],64:[0,.69444,.09597,0,.76666],65:[0,.68333,0,0,.74333],66:[0,.68333,.10257,0,.70389],67:[0,.68333,.14528,0,.71555],68:[0,.68333,.09403,0,.755],69:[0,.68333,.12028,0,.67833],70:[0,.68333,.13305,0,.65277],71:[0,.68333,.08722,0,.77361],72:[0,.68333,.16389,0,.74333],73:[0,.68333,.15806,0,.38555],74:[0,.68333,.14028,0,.525],75:[0,.68333,.14528,0,.76888],76:[0,.68333,0,0,.62722],77:[0,.68333,.16389,0,.89666],78:[0,.68333,.16389,0,.74333],79:[0,.68333,.09403,0,.76666],80:[0,.68333,.10257,0,.67833],81:[.19444,.68333,.09403,0,.76666],82:[0,.68333,.03868,0,.72944],83:[0,.68333,.11972,0,.56222],84:[0,.68333,.13305,0,.71555],85:[0,.68333,.16389,0,.74333],86:[0,.68333,.18361,0,.74333],87:[0,.68333,.18361,0,.99888],88:[0,.68333,.15806,0,.74333],89:[0,.68333,.19383,0,.74333],90:[0,.68333,.14528,0,.61333],91:[.25,.75,.1875,0,.30667],93:[.25,.75,.10528,0,.30667],94:[0,.69444,.06646,0,.51111],95:[.31,.12056,.09208,0,.51111],97:[0,.43056,.07671,0,.51111],98:[0,.69444,.06312,0,.46],99:[0,.43056,.05653,0,.46],100:[0,.69444,.10333,0,.51111],101:[0,.43056,.07514,0,.46],102:[.19444,.69444,.21194,0,.30667],103:[.19444,.43056,.08847,0,.46],104:[0,.69444,.07671,0,.51111],105:[0,.65536,.1019,0,.30667],106:[.19444,.65536,.14467,0,.30667],107:[0,.69444,.10764,0,.46],108:[0,.69444,.10333,0,.25555],109:[0,.43056,.07671,0,.81777],110:[0,.43056,.07671,0,.56222],111:[0,.43056,.06312,0,.51111],112:[.19444,.43056,.06312,0,.51111],113:[.19444,.43056,.08847,0,.46],114:[0,.43056,.10764,0,.42166],115:[0,.43056,.08208,0,.40889],116:[0,.61508,.09486,0,.33222],117:[0,.43056,.07671,0,.53666],118:[0,.43056,.10764,0,.46],119:[0,.43056,.10764,0,.66444],120:[0,.43056,.12042,0,.46389],121:[.19444,.43056,.08847,0,.48555],122:[0,.43056,.12292,0,.40889],126:[.35,.31786,.11585,0,.51111],160:[0,0,0,0,.25],168:[0,.66786,.10474,0,.51111],176:[0,.69444,0,0,.83129],184:[.17014,0,0,0,.46],198:[0,.68333,.12028,0,.88277],216:[.04861,.73194,.09403,0,.76666],223:[.19444,.69444,.10514,0,.53666],230:[0,.43056,.07514,0,.71555],248:[.09722,.52778,.09194,0,.51111],338:[0,.68333,.12028,0,.98499],339:[0,.43056,.07514,0,.71555],710:[0,.69444,.06646,0,.51111],711:[0,.62847,.08295,0,.51111],713:[0,.56167,.10333,0,.51111],714:[0,.69444,.09694,0,.51111],715:[0,.69444,0,0,.51111],728:[0,.69444,.10806,0,.51111],729:[0,.66786,.11752,0,.30667],730:[0,.69444,0,0,.83129],732:[0,.66786,.11585,0,.51111],733:[0,.69444,.1225,0,.51111],915:[0,.68333,.13305,0,.62722],916:[0,.68333,0,0,.81777],920:[0,.68333,.09403,0,.76666],923:[0,.68333,0,0,.69222],926:[0,.68333,.15294,0,.66444],928:[0,.68333,.16389,0,.74333],931:[0,.68333,.12028,0,.71555],933:[0,.68333,.11111,0,.76666],934:[0,.68333,.05986,0,.71555],936:[0,.68333,.11111,0,.76666],937:[0,.68333,.10257,0,.71555],8211:[0,.43056,.09208,0,.51111],8212:[0,.43056,.09208,0,1.02222],8216:[0,.69444,.12417,0,.30667],8217:[0,.69444,.12417,0,.30667],8220:[0,.69444,.1685,0,.51444],8221:[0,.69444,.06961,0,.51444],8463:[0,.68889,0,0,.54028]},"Main-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.27778],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.77778],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.19444,.10556,0,0,.27778],45:[0,.43056,0,0,.33333],46:[0,.10556,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.64444,0,0,.5],49:[0,.64444,0,0,.5],50:[0,.64444,0,0,.5],51:[0,.64444,0,0,.5],52:[0,.64444,0,0,.5],53:[0,.64444,0,0,.5],54:[0,.64444,0,0,.5],55:[0,.64444,0,0,.5],56:[0,.64444,0,0,.5],57:[0,.64444,0,0,.5],58:[0,.43056,0,0,.27778],59:[.19444,.43056,0,0,.27778],60:[.0391,.5391,0,0,.77778],61:[-.13313,.36687,0,0,.77778],62:[.0391,.5391,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.77778],65:[0,.68333,0,0,.75],66:[0,.68333,0,0,.70834],67:[0,.68333,0,0,.72222],68:[0,.68333,0,0,.76389],69:[0,.68333,0,0,.68056],70:[0,.68333,0,0,.65278],71:[0,.68333,0,0,.78472],72:[0,.68333,0,0,.75],73:[0,.68333,0,0,.36111],74:[0,.68333,0,0,.51389],75:[0,.68333,0,0,.77778],76:[0,.68333,0,0,.625],77:[0,.68333,0,0,.91667],78:[0,.68333,0,0,.75],79:[0,.68333,0,0,.77778],80:[0,.68333,0,0,.68056],81:[.19444,.68333,0,0,.77778],82:[0,.68333,0,0,.73611],83:[0,.68333,0,0,.55556],84:[0,.68333,0,0,.72222],85:[0,.68333,0,0,.75],86:[0,.68333,.01389,0,.75],87:[0,.68333,.01389,0,1.02778],88:[0,.68333,0,0,.75],89:[0,.68333,.025,0,.75],90:[0,.68333,0,0,.61111],91:[.25,.75,0,0,.27778],92:[.25,.75,0,0,.5],93:[.25,.75,0,0,.27778],94:[0,.69444,0,0,.5],95:[.31,.12056,.02778,0,.5],97:[0,.43056,0,0,.5],98:[0,.69444,0,0,.55556],99:[0,.43056,0,0,.44445],100:[0,.69444,0,0,.55556],101:[0,.43056,0,0,.44445],102:[0,.69444,.07778,0,.30556],103:[.19444,.43056,.01389,0,.5],104:[0,.69444,0,0,.55556],105:[0,.66786,0,0,.27778],106:[.19444,.66786,0,0,.30556],107:[0,.69444,0,0,.52778],108:[0,.69444,0,0,.27778],109:[0,.43056,0,0,.83334],110:[0,.43056,0,0,.55556],111:[0,.43056,0,0,.5],112:[.19444,.43056,0,0,.55556],113:[.19444,.43056,0,0,.52778],114:[0,.43056,0,0,.39167],115:[0,.43056,0,0,.39445],116:[0,.61508,0,0,.38889],117:[0,.43056,0,0,.55556],118:[0,.43056,.01389,0,.52778],119:[0,.43056,.01389,0,.72222],120:[0,.43056,0,0,.52778],121:[.19444,.43056,.01389,0,.52778],122:[0,.43056,0,0,.44445],123:[.25,.75,0,0,.5],124:[.25,.75,0,0,.27778],125:[.25,.75,0,0,.5],126:[.35,.31786,0,0,.5],160:[0,0,0,0,.25],163:[0,.69444,0,0,.76909],167:[.19444,.69444,0,0,.44445],168:[0,.66786,0,0,.5],172:[0,.43056,0,0,.66667],176:[0,.69444,0,0,.75],177:[.08333,.58333,0,0,.77778],182:[.19444,.69444,0,0,.61111],184:[.17014,0,0,0,.44445],198:[0,.68333,0,0,.90278],215:[.08333,.58333,0,0,.77778],216:[.04861,.73194,0,0,.77778],223:[0,.69444,0,0,.5],230:[0,.43056,0,0,.72222],247:[.08333,.58333,0,0,.77778],248:[.09722,.52778,0,0,.5],305:[0,.43056,0,0,.27778],338:[0,.68333,0,0,1.01389],339:[0,.43056,0,0,.77778],567:[.19444,.43056,0,0,.30556],710:[0,.69444,0,0,.5],711:[0,.62847,0,0,.5],713:[0,.56778,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.66786,0,0,.27778],730:[0,.69444,0,0,.75],732:[0,.66786,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.68333,0,0,.625],916:[0,.68333,0,0,.83334],920:[0,.68333,0,0,.77778],923:[0,.68333,0,0,.69445],926:[0,.68333,0,0,.66667],928:[0,.68333,0,0,.75],931:[0,.68333,0,0,.72222],933:[0,.68333,0,0,.77778],934:[0,.68333,0,0,.72222],936:[0,.68333,0,0,.77778],937:[0,.68333,0,0,.72222],8211:[0,.43056,.02778,0,.5],8212:[0,.43056,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5],8224:[.19444,.69444,0,0,.44445],8225:[.19444,.69444,0,0,.44445],8230:[0,.123,0,0,1.172],8242:[0,.55556,0,0,.275],8407:[0,.71444,.15382,0,.5],8463:[0,.68889,0,0,.54028],8465:[0,.69444,0,0,.72222],8467:[0,.69444,0,.11111,.41667],8472:[.19444,.43056,0,.11111,.63646],8476:[0,.69444,0,0,.72222],8501:[0,.69444,0,0,.61111],8592:[-.13313,.36687,0,0,1],8593:[.19444,.69444,0,0,.5],8594:[-.13313,.36687,0,0,1],8595:[.19444,.69444,0,0,.5],8596:[-.13313,.36687,0,0,1],8597:[.25,.75,0,0,.5],8598:[.19444,.69444,0,0,1],8599:[.19444,.69444,0,0,1],8600:[.19444,.69444,0,0,1],8601:[.19444,.69444,0,0,1],8614:[.011,.511,0,0,1],8617:[.011,.511,0,0,1.126],8618:[.011,.511,0,0,1.126],8636:[-.13313,.36687,0,0,1],8637:[-.13313,.36687,0,0,1],8640:[-.13313,.36687,0,0,1],8641:[-.13313,.36687,0,0,1],8652:[.011,.671,0,0,1],8656:[-.13313,.36687,0,0,1],8657:[.19444,.69444,0,0,.61111],8658:[-.13313,.36687,0,0,1],8659:[.19444,.69444,0,0,.61111],8660:[-.13313,.36687,0,0,1],8661:[.25,.75,0,0,.61111],8704:[0,.69444,0,0,.55556],8706:[0,.69444,.05556,.08334,.5309],8707:[0,.69444,0,0,.55556],8709:[.05556,.75,0,0,.5],8711:[0,.68333,0,0,.83334],8712:[.0391,.5391,0,0,.66667],8715:[.0391,.5391,0,0,.66667],8722:[.08333,.58333,0,0,.77778],8723:[.08333,.58333,0,0,.77778],8725:[.25,.75,0,0,.5],8726:[.25,.75,0,0,.5],8727:[-.03472,.46528,0,0,.5],8728:[-.05555,.44445,0,0,.5],8729:[-.05555,.44445,0,0,.5],8730:[.2,.8,0,0,.83334],8733:[0,.43056,0,0,.77778],8734:[0,.43056,0,0,1],8736:[0,.69224,0,0,.72222],8739:[.25,.75,0,0,.27778],8741:[.25,.75,0,0,.5],8743:[0,.55556,0,0,.66667],8744:[0,.55556,0,0,.66667],8745:[0,.55556,0,0,.66667],8746:[0,.55556,0,0,.66667],8747:[.19444,.69444,.11111,0,.41667],8764:[-.13313,.36687,0,0,.77778],8768:[.19444,.69444,0,0,.27778],8771:[-.03625,.46375,0,0,.77778],8773:[-.022,.589,0,0,.778],8776:[-.01688,.48312,0,0,.77778],8781:[-.03625,.46375,0,0,.77778],8784:[-.133,.673,0,0,.778],8801:[-.03625,.46375,0,0,.77778],8804:[.13597,.63597,0,0,.77778],8805:[.13597,.63597,0,0,.77778],8810:[.0391,.5391,0,0,1],8811:[.0391,.5391,0,0,1],8826:[.0391,.5391,0,0,.77778],8827:[.0391,.5391,0,0,.77778],8834:[.0391,.5391,0,0,.77778],8835:[.0391,.5391,0,0,.77778],8838:[.13597,.63597,0,0,.77778],8839:[.13597,.63597,0,0,.77778],8846:[0,.55556,0,0,.66667],8849:[.13597,.63597,0,0,.77778],8850:[.13597,.63597,0,0,.77778],8851:[0,.55556,0,0,.66667],8852:[0,.55556,0,0,.66667],8853:[.08333,.58333,0,0,.77778],8854:[.08333,.58333,0,0,.77778],8855:[.08333,.58333,0,0,.77778],8856:[.08333,.58333,0,0,.77778],8857:[.08333,.58333,0,0,.77778],8866:[0,.69444,0,0,.61111],8867:[0,.69444,0,0,.61111],8868:[0,.69444,0,0,.77778],8869:[0,.69444,0,0,.77778],8872:[.249,.75,0,0,.867],8900:[-.05555,.44445,0,0,.5],8901:[-.05555,.44445,0,0,.27778],8902:[-.03472,.46528,0,0,.5],8904:[.005,.505,0,0,.9],8942:[.03,.903,0,0,.278],8943:[-.19,.313,0,0,1.172],8945:[-.1,.823,0,0,1.282],8968:[.25,.75,0,0,.44445],8969:[.25,.75,0,0,.44445],8970:[.25,.75,0,0,.44445],8971:[.25,.75,0,0,.44445],8994:[-.14236,.35764,0,0,1],8995:[-.14236,.35764,0,0,1],9136:[.244,.744,0,0,.412],9137:[.244,.745,0,0,.412],9651:[.19444,.69444,0,0,.88889],9657:[-.03472,.46528,0,0,.5],9661:[.19444,.69444,0,0,.88889],9667:[-.03472,.46528,0,0,.5],9711:[.19444,.69444,0,0,1],9824:[.12963,.69444,0,0,.77778],9825:[.12963,.69444,0,0,.77778],9826:[.12963,.69444,0,0,.77778],9827:[.12963,.69444,0,0,.77778],9837:[0,.75,0,0,.38889],9838:[.19444,.69444,0,0,.38889],9839:[.19444,.69444,0,0,.38889],10216:[.25,.75,0,0,.38889],10217:[.25,.75,0,0,.38889],10222:[.244,.744,0,0,.412],10223:[.244,.745,0,0,.412],10229:[.011,.511,0,0,1.609],10230:[.011,.511,0,0,1.638],10231:[.011,.511,0,0,1.859],10232:[.024,.525,0,0,1.609],10233:[.024,.525,0,0,1.638],10234:[.024,.525,0,0,1.858],10236:[.011,.511,0,0,1.638],10815:[0,.68333,0,0,.75],10927:[.13597,.63597,0,0,.77778],10928:[.13597,.63597,0,0,.77778],57376:[.19444,.69444,0,0,0]},"Math-BoldItalic":{32:[0,0,0,0,.25],48:[0,.44444,0,0,.575],49:[0,.44444,0,0,.575],50:[0,.44444,0,0,.575],51:[.19444,.44444,0,0,.575],52:[.19444,.44444,0,0,.575],53:[.19444,.44444,0,0,.575],54:[0,.64444,0,0,.575],55:[.19444,.44444,0,0,.575],56:[0,.64444,0,0,.575],57:[.19444,.44444,0,0,.575],65:[0,.68611,0,0,.86944],66:[0,.68611,.04835,0,.8664],67:[0,.68611,.06979,0,.81694],68:[0,.68611,.03194,0,.93812],69:[0,.68611,.05451,0,.81007],70:[0,.68611,.15972,0,.68889],71:[0,.68611,0,0,.88673],72:[0,.68611,.08229,0,.98229],73:[0,.68611,.07778,0,.51111],74:[0,.68611,.10069,0,.63125],75:[0,.68611,.06979,0,.97118],76:[0,.68611,0,0,.75555],77:[0,.68611,.11424,0,1.14201],78:[0,.68611,.11424,0,.95034],79:[0,.68611,.03194,0,.83666],80:[0,.68611,.15972,0,.72309],81:[.19444,.68611,0,0,.86861],82:[0,.68611,.00421,0,.87235],83:[0,.68611,.05382,0,.69271],84:[0,.68611,.15972,0,.63663],85:[0,.68611,.11424,0,.80027],86:[0,.68611,.25555,0,.67778],87:[0,.68611,.15972,0,1.09305],88:[0,.68611,.07778,0,.94722],89:[0,.68611,.25555,0,.67458],90:[0,.68611,.06979,0,.77257],97:[0,.44444,0,0,.63287],98:[0,.69444,0,0,.52083],99:[0,.44444,0,0,.51342],100:[0,.69444,0,0,.60972],101:[0,.44444,0,0,.55361],102:[.19444,.69444,.11042,0,.56806],103:[.19444,.44444,.03704,0,.5449],104:[0,.69444,0,0,.66759],105:[0,.69326,0,0,.4048],106:[.19444,.69326,.0622,0,.47083],107:[0,.69444,.01852,0,.6037],108:[0,.69444,.0088,0,.34815],109:[0,.44444,0,0,1.0324],110:[0,.44444,0,0,.71296],111:[0,.44444,0,0,.58472],112:[.19444,.44444,0,0,.60092],113:[.19444,.44444,.03704,0,.54213],114:[0,.44444,.03194,0,.5287],115:[0,.44444,0,0,.53125],116:[0,.63492,0,0,.41528],117:[0,.44444,0,0,.68102],118:[0,.44444,.03704,0,.56666],119:[0,.44444,.02778,0,.83148],120:[0,.44444,0,0,.65903],121:[.19444,.44444,.03704,0,.59028],122:[0,.44444,.04213,0,.55509],160:[0,0,0,0,.25],915:[0,.68611,.15972,0,.65694],916:[0,.68611,0,0,.95833],920:[0,.68611,.03194,0,.86722],923:[0,.68611,0,0,.80555],926:[0,.68611,.07458,0,.84125],928:[0,.68611,.08229,0,.98229],931:[0,.68611,.05451,0,.88507],933:[0,.68611,.15972,0,.67083],934:[0,.68611,0,0,.76666],936:[0,.68611,.11653,0,.71402],937:[0,.68611,.04835,0,.8789],945:[0,.44444,0,0,.76064],946:[.19444,.69444,.03403,0,.65972],947:[.19444,.44444,.06389,0,.59003],948:[0,.69444,.03819,0,.52222],949:[0,.44444,0,0,.52882],950:[.19444,.69444,.06215,0,.50833],951:[.19444,.44444,.03704,0,.6],952:[0,.69444,.03194,0,.5618],953:[0,.44444,0,0,.41204],954:[0,.44444,0,0,.66759],955:[0,.69444,0,0,.67083],956:[.19444,.44444,0,0,.70787],957:[0,.44444,.06898,0,.57685],958:[.19444,.69444,.03021,0,.50833],959:[0,.44444,0,0,.58472],960:[0,.44444,.03704,0,.68241],961:[.19444,.44444,0,0,.6118],962:[.09722,.44444,.07917,0,.42361],963:[0,.44444,.03704,0,.68588],964:[0,.44444,.13472,0,.52083],965:[0,.44444,.03704,0,.63055],966:[.19444,.44444,0,0,.74722],967:[.19444,.44444,0,0,.71805],968:[.19444,.69444,.03704,0,.75833],969:[0,.44444,.03704,0,.71782],977:[0,.69444,0,0,.69155],981:[.19444,.69444,0,0,.7125],982:[0,.44444,.03194,0,.975],1009:[.19444,.44444,0,0,.6118],1013:[0,.44444,0,0,.48333],57649:[0,.44444,0,0,.39352],57911:[.19444,.44444,0,0,.43889]},"Math-Italic":{32:[0,0,0,0,.25],48:[0,.43056,0,0,.5],49:[0,.43056,0,0,.5],50:[0,.43056,0,0,.5],51:[.19444,.43056,0,0,.5],52:[.19444,.43056,0,0,.5],53:[.19444,.43056,0,0,.5],54:[0,.64444,0,0,.5],55:[.19444,.43056,0,0,.5],56:[0,.64444,0,0,.5],57:[.19444,.43056,0,0,.5],65:[0,.68333,0,.13889,.75],66:[0,.68333,.05017,.08334,.75851],67:[0,.68333,.07153,.08334,.71472],68:[0,.68333,.02778,.05556,.82792],69:[0,.68333,.05764,.08334,.7382],70:[0,.68333,.13889,.08334,.64306],71:[0,.68333,0,.08334,.78625],72:[0,.68333,.08125,.05556,.83125],73:[0,.68333,.07847,.11111,.43958],74:[0,.68333,.09618,.16667,.55451],75:[0,.68333,.07153,.05556,.84931],76:[0,.68333,0,.02778,.68056],77:[0,.68333,.10903,.08334,.97014],78:[0,.68333,.10903,.08334,.80347],79:[0,.68333,.02778,.08334,.76278],80:[0,.68333,.13889,.08334,.64201],81:[.19444,.68333,0,.08334,.79056],82:[0,.68333,.00773,.08334,.75929],83:[0,.68333,.05764,.08334,.6132],84:[0,.68333,.13889,.08334,.58438],85:[0,.68333,.10903,.02778,.68278],86:[0,.68333,.22222,0,.58333],87:[0,.68333,.13889,0,.94445],88:[0,.68333,.07847,.08334,.82847],89:[0,.68333,.22222,0,.58056],90:[0,.68333,.07153,.08334,.68264],97:[0,.43056,0,0,.52859],98:[0,.69444,0,0,.42917],99:[0,.43056,0,.05556,.43276],100:[0,.69444,0,.16667,.52049],101:[0,.43056,0,.05556,.46563],102:[.19444,.69444,.10764,.16667,.48959],103:[.19444,.43056,.03588,.02778,.47697],104:[0,.69444,0,0,.57616],105:[0,.65952,0,0,.34451],106:[.19444,.65952,.05724,0,.41181],107:[0,.69444,.03148,0,.5206],108:[0,.69444,.01968,.08334,.29838],109:[0,.43056,0,0,.87801],110:[0,.43056,0,0,.60023],111:[0,.43056,0,.05556,.48472],112:[.19444,.43056,0,.08334,.50313],113:[.19444,.43056,.03588,.08334,.44641],114:[0,.43056,.02778,.05556,.45116],115:[0,.43056,0,.05556,.46875],116:[0,.61508,0,.08334,.36111],117:[0,.43056,0,.02778,.57246],118:[0,.43056,.03588,.02778,.48472],119:[0,.43056,.02691,.08334,.71592],120:[0,.43056,0,.02778,.57153],121:[.19444,.43056,.03588,.05556,.49028],122:[0,.43056,.04398,.05556,.46505],160:[0,0,0,0,.25],915:[0,.68333,.13889,.08334,.61528],916:[0,.68333,0,.16667,.83334],920:[0,.68333,.02778,.08334,.76278],923:[0,.68333,0,.16667,.69445],926:[0,.68333,.07569,.08334,.74236],928:[0,.68333,.08125,.05556,.83125],931:[0,.68333,.05764,.08334,.77986],933:[0,.68333,.13889,.05556,.58333],934:[0,.68333,0,.08334,.66667],936:[0,.68333,.11,.05556,.61222],937:[0,.68333,.05017,.08334,.7724],945:[0,.43056,.0037,.02778,.6397],946:[.19444,.69444,.05278,.08334,.56563],947:[.19444,.43056,.05556,0,.51773],948:[0,.69444,.03785,.05556,.44444],949:[0,.43056,0,.08334,.46632],950:[.19444,.69444,.07378,.08334,.4375],951:[.19444,.43056,.03588,.05556,.49653],952:[0,.69444,.02778,.08334,.46944],953:[0,.43056,0,.05556,.35394],954:[0,.43056,0,0,.57616],955:[0,.69444,0,0,.58334],956:[.19444,.43056,0,.02778,.60255],957:[0,.43056,.06366,.02778,.49398],958:[.19444,.69444,.04601,.11111,.4375],959:[0,.43056,0,.05556,.48472],960:[0,.43056,.03588,0,.57003],961:[.19444,.43056,0,.08334,.51702],962:[.09722,.43056,.07986,.08334,.36285],963:[0,.43056,.03588,0,.57141],964:[0,.43056,.1132,.02778,.43715],965:[0,.43056,.03588,.02778,.54028],966:[.19444,.43056,0,.08334,.65417],967:[.19444,.43056,0,.05556,.62569],968:[.19444,.69444,.03588,.11111,.65139],969:[0,.43056,.03588,0,.62245],977:[0,.69444,0,.08334,.59144],981:[.19444,.69444,0,.08334,.59583],982:[0,.43056,.02778,0,.82813],1009:[.19444,.43056,0,.08334,.51702],1013:[0,.43056,0,.05556,.4059],57649:[0,.43056,0,.02778,.32246],57911:[.19444,.43056,0,.08334,.38403]},"SansSerif-Bold":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.36667],34:[0,.69444,0,0,.55834],35:[.19444,.69444,0,0,.91667],36:[.05556,.75,0,0,.55],37:[.05556,.75,0,0,1.02912],38:[0,.69444,0,0,.83056],39:[0,.69444,0,0,.30556],40:[.25,.75,0,0,.42778],41:[.25,.75,0,0,.42778],42:[0,.75,0,0,.55],43:[.11667,.61667,0,0,.85556],44:[.10556,.13056,0,0,.30556],45:[0,.45833,0,0,.36667],46:[0,.13056,0,0,.30556],47:[.25,.75,0,0,.55],48:[0,.69444,0,0,.55],49:[0,.69444,0,0,.55],50:[0,.69444,0,0,.55],51:[0,.69444,0,0,.55],52:[0,.69444,0,0,.55],53:[0,.69444,0,0,.55],54:[0,.69444,0,0,.55],55:[0,.69444,0,0,.55],56:[0,.69444,0,0,.55],57:[0,.69444,0,0,.55],58:[0,.45833,0,0,.30556],59:[.10556,.45833,0,0,.30556],61:[-.09375,.40625,0,0,.85556],63:[0,.69444,0,0,.51945],64:[0,.69444,0,0,.73334],65:[0,.69444,0,0,.73334],66:[0,.69444,0,0,.73334],67:[0,.69444,0,0,.70278],68:[0,.69444,0,0,.79445],69:[0,.69444,0,0,.64167],70:[0,.69444,0,0,.61111],71:[0,.69444,0,0,.73334],72:[0,.69444,0,0,.79445],73:[0,.69444,0,0,.33056],74:[0,.69444,0,0,.51945],75:[0,.69444,0,0,.76389],76:[0,.69444,0,0,.58056],77:[0,.69444,0,0,.97778],78:[0,.69444,0,0,.79445],79:[0,.69444,0,0,.79445],80:[0,.69444,0,0,.70278],81:[.10556,.69444,0,0,.79445],82:[0,.69444,0,0,.70278],83:[0,.69444,0,0,.61111],84:[0,.69444,0,0,.73334],85:[0,.69444,0,0,.76389],86:[0,.69444,.01528,0,.73334],87:[0,.69444,.01528,0,1.03889],88:[0,.69444,0,0,.73334],89:[0,.69444,.0275,0,.73334],90:[0,.69444,0,0,.67223],91:[.25,.75,0,0,.34306],93:[.25,.75,0,0,.34306],94:[0,.69444,0,0,.55],95:[.35,.10833,.03056,0,.55],97:[0,.45833,0,0,.525],98:[0,.69444,0,0,.56111],99:[0,.45833,0,0,.48889],100:[0,.69444,0,0,.56111],101:[0,.45833,0,0,.51111],102:[0,.69444,.07639,0,.33611],103:[.19444,.45833,.01528,0,.55],104:[0,.69444,0,0,.56111],105:[0,.69444,0,0,.25556],106:[.19444,.69444,0,0,.28611],107:[0,.69444,0,0,.53056],108:[0,.69444,0,0,.25556],109:[0,.45833,0,0,.86667],110:[0,.45833,0,0,.56111],111:[0,.45833,0,0,.55],112:[.19444,.45833,0,0,.56111],113:[.19444,.45833,0,0,.56111],114:[0,.45833,.01528,0,.37222],115:[0,.45833,0,0,.42167],116:[0,.58929,0,0,.40417],117:[0,.45833,0,0,.56111],118:[0,.45833,.01528,0,.5],119:[0,.45833,.01528,0,.74445],120:[0,.45833,0,0,.5],121:[.19444,.45833,.01528,0,.5],122:[0,.45833,0,0,.47639],126:[.35,.34444,0,0,.55],160:[0,0,0,0,.25],168:[0,.69444,0,0,.55],176:[0,.69444,0,0,.73334],180:[0,.69444,0,0,.55],184:[.17014,0,0,0,.48889],305:[0,.45833,0,0,.25556],567:[.19444,.45833,0,0,.28611],710:[0,.69444,0,0,.55],711:[0,.63542,0,0,.55],713:[0,.63778,0,0,.55],728:[0,.69444,0,0,.55],729:[0,.69444,0,0,.30556],730:[0,.69444,0,0,.73334],732:[0,.69444,0,0,.55],733:[0,.69444,0,0,.55],915:[0,.69444,0,0,.58056],916:[0,.69444,0,0,.91667],920:[0,.69444,0,0,.85556],923:[0,.69444,0,0,.67223],926:[0,.69444,0,0,.73334],928:[0,.69444,0,0,.79445],931:[0,.69444,0,0,.79445],933:[0,.69444,0,0,.85556],934:[0,.69444,0,0,.79445],936:[0,.69444,0,0,.85556],937:[0,.69444,0,0,.79445],8211:[0,.45833,.03056,0,.55],8212:[0,.45833,.03056,0,1.10001],8216:[0,.69444,0,0,.30556],8217:[0,.69444,0,0,.30556],8220:[0,.69444,0,0,.55834],8221:[0,.69444,0,0,.55834]},"SansSerif-Italic":{32:[0,0,0,0,.25],33:[0,.69444,.05733,0,.31945],34:[0,.69444,.00316,0,.5],35:[.19444,.69444,.05087,0,.83334],36:[.05556,.75,.11156,0,.5],37:[.05556,.75,.03126,0,.83334],38:[0,.69444,.03058,0,.75834],39:[0,.69444,.07816,0,.27778],40:[.25,.75,.13164,0,.38889],41:[.25,.75,.02536,0,.38889],42:[0,.75,.11775,0,.5],43:[.08333,.58333,.02536,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,.01946,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,.13164,0,.5],48:[0,.65556,.11156,0,.5],49:[0,.65556,.11156,0,.5],50:[0,.65556,.11156,0,.5],51:[0,.65556,.11156,0,.5],52:[0,.65556,.11156,0,.5],53:[0,.65556,.11156,0,.5],54:[0,.65556,.11156,0,.5],55:[0,.65556,.11156,0,.5],56:[0,.65556,.11156,0,.5],57:[0,.65556,.11156,0,.5],58:[0,.44444,.02502,0,.27778],59:[.125,.44444,.02502,0,.27778],61:[-.13,.37,.05087,0,.77778],63:[0,.69444,.11809,0,.47222],64:[0,.69444,.07555,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,.08293,0,.66667],67:[0,.69444,.11983,0,.63889],68:[0,.69444,.07555,0,.72223],69:[0,.69444,.11983,0,.59722],70:[0,.69444,.13372,0,.56945],71:[0,.69444,.11983,0,.66667],72:[0,.69444,.08094,0,.70834],73:[0,.69444,.13372,0,.27778],74:[0,.69444,.08094,0,.47222],75:[0,.69444,.11983,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,.08094,0,.875],78:[0,.69444,.08094,0,.70834],79:[0,.69444,.07555,0,.73611],80:[0,.69444,.08293,0,.63889],81:[.125,.69444,.07555,0,.73611],82:[0,.69444,.08293,0,.64584],83:[0,.69444,.09205,0,.55556],84:[0,.69444,.13372,0,.68056],85:[0,.69444,.08094,0,.6875],86:[0,.69444,.1615,0,.66667],87:[0,.69444,.1615,0,.94445],88:[0,.69444,.13372,0,.66667],89:[0,.69444,.17261,0,.66667],90:[0,.69444,.11983,0,.61111],91:[.25,.75,.15942,0,.28889],93:[.25,.75,.08719,0,.28889],94:[0,.69444,.0799,0,.5],95:[.35,.09444,.08616,0,.5],97:[0,.44444,.00981,0,.48056],98:[0,.69444,.03057,0,.51667],99:[0,.44444,.08336,0,.44445],100:[0,.69444,.09483,0,.51667],101:[0,.44444,.06778,0,.44445],102:[0,.69444,.21705,0,.30556],103:[.19444,.44444,.10836,0,.5],104:[0,.69444,.01778,0,.51667],105:[0,.67937,.09718,0,.23889],106:[.19444,.67937,.09162,0,.26667],107:[0,.69444,.08336,0,.48889],108:[0,.69444,.09483,0,.23889],109:[0,.44444,.01778,0,.79445],110:[0,.44444,.01778,0,.51667],111:[0,.44444,.06613,0,.5],112:[.19444,.44444,.0389,0,.51667],113:[.19444,.44444,.04169,0,.51667],114:[0,.44444,.10836,0,.34167],115:[0,.44444,.0778,0,.38333],116:[0,.57143,.07225,0,.36111],117:[0,.44444,.04169,0,.51667],118:[0,.44444,.10836,0,.46111],119:[0,.44444,.10836,0,.68334],120:[0,.44444,.09169,0,.46111],121:[.19444,.44444,.10836,0,.46111],122:[0,.44444,.08752,0,.43472],126:[.35,.32659,.08826,0,.5],160:[0,0,0,0,.25],168:[0,.67937,.06385,0,.5],176:[0,.69444,0,0,.73752],184:[.17014,0,0,0,.44445],305:[0,.44444,.04169,0,.23889],567:[.19444,.44444,.04169,0,.26667],710:[0,.69444,.0799,0,.5],711:[0,.63194,.08432,0,.5],713:[0,.60889,.08776,0,.5],714:[0,.69444,.09205,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,.09483,0,.5],729:[0,.67937,.07774,0,.27778],730:[0,.69444,0,0,.73752],732:[0,.67659,.08826,0,.5],733:[0,.69444,.09205,0,.5],915:[0,.69444,.13372,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,.07555,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,.12816,0,.66667],928:[0,.69444,.08094,0,.70834],931:[0,.69444,.11983,0,.72222],933:[0,.69444,.09031,0,.77778],934:[0,.69444,.04603,0,.72222],936:[0,.69444,.09031,0,.77778],937:[0,.69444,.08293,0,.72222],8211:[0,.44444,.08616,0,.5],8212:[0,.44444,.08616,0,1],8216:[0,.69444,.07816,0,.27778],8217:[0,.69444,.07816,0,.27778],8220:[0,.69444,.14205,0,.5],8221:[0,.69444,.00316,0,.5]},"SansSerif-Regular":{32:[0,0,0,0,.25],33:[0,.69444,0,0,.31945],34:[0,.69444,0,0,.5],35:[.19444,.69444,0,0,.83334],36:[.05556,.75,0,0,.5],37:[.05556,.75,0,0,.83334],38:[0,.69444,0,0,.75834],39:[0,.69444,0,0,.27778],40:[.25,.75,0,0,.38889],41:[.25,.75,0,0,.38889],42:[0,.75,0,0,.5],43:[.08333,.58333,0,0,.77778],44:[.125,.08333,0,0,.27778],45:[0,.44444,0,0,.33333],46:[0,.08333,0,0,.27778],47:[.25,.75,0,0,.5],48:[0,.65556,0,0,.5],49:[0,.65556,0,0,.5],50:[0,.65556,0,0,.5],51:[0,.65556,0,0,.5],52:[0,.65556,0,0,.5],53:[0,.65556,0,0,.5],54:[0,.65556,0,0,.5],55:[0,.65556,0,0,.5],56:[0,.65556,0,0,.5],57:[0,.65556,0,0,.5],58:[0,.44444,0,0,.27778],59:[.125,.44444,0,0,.27778],61:[-.13,.37,0,0,.77778],63:[0,.69444,0,0,.47222],64:[0,.69444,0,0,.66667],65:[0,.69444,0,0,.66667],66:[0,.69444,0,0,.66667],67:[0,.69444,0,0,.63889],68:[0,.69444,0,0,.72223],69:[0,.69444,0,0,.59722],70:[0,.69444,0,0,.56945],71:[0,.69444,0,0,.66667],72:[0,.69444,0,0,.70834],73:[0,.69444,0,0,.27778],74:[0,.69444,0,0,.47222],75:[0,.69444,0,0,.69445],76:[0,.69444,0,0,.54167],77:[0,.69444,0,0,.875],78:[0,.69444,0,0,.70834],79:[0,.69444,0,0,.73611],80:[0,.69444,0,0,.63889],81:[.125,.69444,0,0,.73611],82:[0,.69444,0,0,.64584],83:[0,.69444,0,0,.55556],84:[0,.69444,0,0,.68056],85:[0,.69444,0,0,.6875],86:[0,.69444,.01389,0,.66667],87:[0,.69444,.01389,0,.94445],88:[0,.69444,0,0,.66667],89:[0,.69444,.025,0,.66667],90:[0,.69444,0,0,.61111],91:[.25,.75,0,0,.28889],93:[.25,.75,0,0,.28889],94:[0,.69444,0,0,.5],95:[.35,.09444,.02778,0,.5],97:[0,.44444,0,0,.48056],98:[0,.69444,0,0,.51667],99:[0,.44444,0,0,.44445],100:[0,.69444,0,0,.51667],101:[0,.44444,0,0,.44445],102:[0,.69444,.06944,0,.30556],103:[.19444,.44444,.01389,0,.5],104:[0,.69444,0,0,.51667],105:[0,.67937,0,0,.23889],106:[.19444,.67937,0,0,.26667],107:[0,.69444,0,0,.48889],108:[0,.69444,0,0,.23889],109:[0,.44444,0,0,.79445],110:[0,.44444,0,0,.51667],111:[0,.44444,0,0,.5],112:[.19444,.44444,0,0,.51667],113:[.19444,.44444,0,0,.51667],114:[0,.44444,.01389,0,.34167],115:[0,.44444,0,0,.38333],116:[0,.57143,0,0,.36111],117:[0,.44444,0,0,.51667],118:[0,.44444,.01389,0,.46111],119:[0,.44444,.01389,0,.68334],120:[0,.44444,0,0,.46111],121:[.19444,.44444,.01389,0,.46111],122:[0,.44444,0,0,.43472],126:[.35,.32659,0,0,.5],160:[0,0,0,0,.25],168:[0,.67937,0,0,.5],176:[0,.69444,0,0,.66667],184:[.17014,0,0,0,.44445],305:[0,.44444,0,0,.23889],567:[.19444,.44444,0,0,.26667],710:[0,.69444,0,0,.5],711:[0,.63194,0,0,.5],713:[0,.60889,0,0,.5],714:[0,.69444,0,0,.5],715:[0,.69444,0,0,.5],728:[0,.69444,0,0,.5],729:[0,.67937,0,0,.27778],730:[0,.69444,0,0,.66667],732:[0,.67659,0,0,.5],733:[0,.69444,0,0,.5],915:[0,.69444,0,0,.54167],916:[0,.69444,0,0,.83334],920:[0,.69444,0,0,.77778],923:[0,.69444,0,0,.61111],926:[0,.69444,0,0,.66667],928:[0,.69444,0,0,.70834],931:[0,.69444,0,0,.72222],933:[0,.69444,0,0,.77778],934:[0,.69444,0,0,.72222],936:[0,.69444,0,0,.77778],937:[0,.69444,0,0,.72222],8211:[0,.44444,.02778,0,.5],8212:[0,.44444,.02778,0,1],8216:[0,.69444,0,0,.27778],8217:[0,.69444,0,0,.27778],8220:[0,.69444,0,0,.5],8221:[0,.69444,0,0,.5]},"Script-Regular":{32:[0,0,0,0,.25],65:[0,.7,.22925,0,.80253],66:[0,.7,.04087,0,.90757],67:[0,.7,.1689,0,.66619],68:[0,.7,.09371,0,.77443],69:[0,.7,.18583,0,.56162],70:[0,.7,.13634,0,.89544],71:[0,.7,.17322,0,.60961],72:[0,.7,.29694,0,.96919],73:[0,.7,.19189,0,.80907],74:[.27778,.7,.19189,0,1.05159],75:[0,.7,.31259,0,.91364],76:[0,.7,.19189,0,.87373],77:[0,.7,.15981,0,1.08031],78:[0,.7,.3525,0,.9015],79:[0,.7,.08078,0,.73787],80:[0,.7,.08078,0,1.01262],81:[0,.7,.03305,0,.88282],82:[0,.7,.06259,0,.85],83:[0,.7,.19189,0,.86767],84:[0,.7,.29087,0,.74697],85:[0,.7,.25815,0,.79996],86:[0,.7,.27523,0,.62204],87:[0,.7,.27523,0,.80532],88:[0,.7,.26006,0,.94445],89:[0,.7,.2939,0,.70961],90:[0,.7,.24037,0,.8212],160:[0,0,0,0,.25]},"Size1-Regular":{32:[0,0,0,0,.25],40:[.35001,.85,0,0,.45834],41:[.35001,.85,0,0,.45834],47:[.35001,.85,0,0,.57778],91:[.35001,.85,0,0,.41667],92:[.35001,.85,0,0,.57778],93:[.35001,.85,0,0,.41667],123:[.35001,.85,0,0,.58334],125:[.35001,.85,0,0,.58334],160:[0,0,0,0,.25],710:[0,.72222,0,0,.55556],732:[0,.72222,0,0,.55556],770:[0,.72222,0,0,.55556],771:[0,.72222,0,0,.55556],8214:[-.00099,.601,0,0,.77778],8593:[1e-5,.6,0,0,.66667],8595:[1e-5,.6,0,0,.66667],8657:[1e-5,.6,0,0,.77778],8659:[1e-5,.6,0,0,.77778],8719:[.25001,.75,0,0,.94445],8720:[.25001,.75,0,0,.94445],8721:[.25001,.75,0,0,1.05556],8730:[.35001,.85,0,0,1],8739:[-.00599,.606,0,0,.33333],8741:[-.00599,.606,0,0,.55556],8747:[.30612,.805,.19445,0,.47222],8748:[.306,.805,.19445,0,.47222],8749:[.306,.805,.19445,0,.47222],8750:[.30612,.805,.19445,0,.47222],8896:[.25001,.75,0,0,.83334],8897:[.25001,.75,0,0,.83334],8898:[.25001,.75,0,0,.83334],8899:[.25001,.75,0,0,.83334],8968:[.35001,.85,0,0,.47222],8969:[.35001,.85,0,0,.47222],8970:[.35001,.85,0,0,.47222],8971:[.35001,.85,0,0,.47222],9168:[-.00099,.601,0,0,.66667],10216:[.35001,.85,0,0,.47222],10217:[.35001,.85,0,0,.47222],10752:[.25001,.75,0,0,1.11111],10753:[.25001,.75,0,0,1.11111],10754:[.25001,.75,0,0,1.11111],10756:[.25001,.75,0,0,.83334],10758:[.25001,.75,0,0,.83334]},"Size2-Regular":{32:[0,0,0,0,.25],40:[.65002,1.15,0,0,.59722],41:[.65002,1.15,0,0,.59722],47:[.65002,1.15,0,0,.81111],91:[.65002,1.15,0,0,.47222],92:[.65002,1.15,0,0,.81111],93:[.65002,1.15,0,0,.47222],123:[.65002,1.15,0,0,.66667],125:[.65002,1.15,0,0,.66667],160:[0,0,0,0,.25],710:[0,.75,0,0,1],732:[0,.75,0,0,1],770:[0,.75,0,0,1],771:[0,.75,0,0,1],8719:[.55001,1.05,0,0,1.27778],8720:[.55001,1.05,0,0,1.27778],8721:[.55001,1.05,0,0,1.44445],8730:[.65002,1.15,0,0,1],8747:[.86225,1.36,.44445,0,.55556],8748:[.862,1.36,.44445,0,.55556],8749:[.862,1.36,.44445,0,.55556],8750:[.86225,1.36,.44445,0,.55556],8896:[.55001,1.05,0,0,1.11111],8897:[.55001,1.05,0,0,1.11111],8898:[.55001,1.05,0,0,1.11111],8899:[.55001,1.05,0,0,1.11111],8968:[.65002,1.15,0,0,.52778],8969:[.65002,1.15,0,0,.52778],8970:[.65002,1.15,0,0,.52778],8971:[.65002,1.15,0,0,.52778],10216:[.65002,1.15,0,0,.61111],10217:[.65002,1.15,0,0,.61111],10752:[.55001,1.05,0,0,1.51112],10753:[.55001,1.05,0,0,1.51112],10754:[.55001,1.05,0,0,1.51112],10756:[.55001,1.05,0,0,1.11111],10758:[.55001,1.05,0,0,1.11111]},"Size3-Regular":{32:[0,0,0,0,.25],40:[.95003,1.45,0,0,.73611],41:[.95003,1.45,0,0,.73611],47:[.95003,1.45,0,0,1.04445],91:[.95003,1.45,0,0,.52778],92:[.95003,1.45,0,0,1.04445],93:[.95003,1.45,0,0,.52778],123:[.95003,1.45,0,0,.75],125:[.95003,1.45,0,0,.75],160:[0,0,0,0,.25],710:[0,.75,0,0,1.44445],732:[0,.75,0,0,1.44445],770:[0,.75,0,0,1.44445],771:[0,.75,0,0,1.44445],8730:[.95003,1.45,0,0,1],8968:[.95003,1.45,0,0,.58334],8969:[.95003,1.45,0,0,.58334],8970:[.95003,1.45,0,0,.58334],8971:[.95003,1.45,0,0,.58334],10216:[.95003,1.45,0,0,.75],10217:[.95003,1.45,0,0,.75]},"Size4-Regular":{32:[0,0,0,0,.25],40:[1.25003,1.75,0,0,.79167],41:[1.25003,1.75,0,0,.79167],47:[1.25003,1.75,0,0,1.27778],91:[1.25003,1.75,0,0,.58334],92:[1.25003,1.75,0,0,1.27778],93:[1.25003,1.75,0,0,.58334],123:[1.25003,1.75,0,0,.80556],125:[1.25003,1.75,0,0,.80556],160:[0,0,0,0,.25],710:[0,.825,0,0,1.8889],732:[0,.825,0,0,1.8889],770:[0,.825,0,0,1.8889],771:[0,.825,0,0,1.8889],8730:[1.25003,1.75,0,0,1],8968:[1.25003,1.75,0,0,.63889],8969:[1.25003,1.75,0,0,.63889],8970:[1.25003,1.75,0,0,.63889],8971:[1.25003,1.75,0,0,.63889],9115:[.64502,1.155,0,0,.875],9116:[1e-5,.6,0,0,.875],9117:[.64502,1.155,0,0,.875],9118:[.64502,1.155,0,0,.875],9119:[1e-5,.6,0,0,.875],9120:[.64502,1.155,0,0,.875],9121:[.64502,1.155,0,0,.66667],9122:[-.00099,.601,0,0,.66667],9123:[.64502,1.155,0,0,.66667],9124:[.64502,1.155,0,0,.66667],9125:[-.00099,.601,0,0,.66667],9126:[.64502,1.155,0,0,.66667],9127:[1e-5,.9,0,0,.88889],9128:[.65002,1.15,0,0,.88889],9129:[.90001,0,0,0,.88889],9130:[0,.3,0,0,.88889],9131:[1e-5,.9,0,0,.88889],9132:[.65002,1.15,0,0,.88889],9133:[.90001,0,0,0,.88889],9143:[.88502,.915,0,0,1.05556],10216:[1.25003,1.75,0,0,.80556],10217:[1.25003,1.75,0,0,.80556],57344:[-.00499,.605,0,0,1.05556],57345:[-.00499,.605,0,0,1.05556],57680:[0,.12,0,0,.45],57681:[0,.12,0,0,.45],57682:[0,.12,0,0,.45],57683:[0,.12,0,0,.45]},"Typewriter-Regular":{32:[0,0,0,0,.525],33:[0,.61111,0,0,.525],34:[0,.61111,0,0,.525],35:[0,.61111,0,0,.525],36:[.08333,.69444,0,0,.525],37:[.08333,.69444,0,0,.525],38:[0,.61111,0,0,.525],39:[0,.61111,0,0,.525],40:[.08333,.69444,0,0,.525],41:[.08333,.69444,0,0,.525],42:[0,.52083,0,0,.525],43:[-.08056,.53055,0,0,.525],44:[.13889,.125,0,0,.525],45:[-.08056,.53055,0,0,.525],46:[0,.125,0,0,.525],47:[.08333,.69444,0,0,.525],48:[0,.61111,0,0,.525],49:[0,.61111,0,0,.525],50:[0,.61111,0,0,.525],51:[0,.61111,0,0,.525],52:[0,.61111,0,0,.525],53:[0,.61111,0,0,.525],54:[0,.61111,0,0,.525],55:[0,.61111,0,0,.525],56:[0,.61111,0,0,.525],57:[0,.61111,0,0,.525],58:[0,.43056,0,0,.525],59:[.13889,.43056,0,0,.525],60:[-.05556,.55556,0,0,.525],61:[-.19549,.41562,0,0,.525],62:[-.05556,.55556,0,0,.525],63:[0,.61111,0,0,.525],64:[0,.61111,0,0,.525],65:[0,.61111,0,0,.525],66:[0,.61111,0,0,.525],67:[0,.61111,0,0,.525],68:[0,.61111,0,0,.525],69:[0,.61111,0,0,.525],70:[0,.61111,0,0,.525],71:[0,.61111,0,0,.525],72:[0,.61111,0,0,.525],73:[0,.61111,0,0,.525],74:[0,.61111,0,0,.525],75:[0,.61111,0,0,.525],76:[0,.61111,0,0,.525],77:[0,.61111,0,0,.525],78:[0,.61111,0,0,.525],79:[0,.61111,0,0,.525],80:[0,.61111,0,0,.525],81:[.13889,.61111,0,0,.525],82:[0,.61111,0,0,.525],83:[0,.61111,0,0,.525],84:[0,.61111,0,0,.525],85:[0,.61111,0,0,.525],86:[0,.61111,0,0,.525],87:[0,.61111,0,0,.525],88:[0,.61111,0,0,.525],89:[0,.61111,0,0,.525],90:[0,.61111,0,0,.525],91:[.08333,.69444,0,0,.525],92:[.08333,.69444,0,0,.525],93:[.08333,.69444,0,0,.525],94:[0,.61111,0,0,.525],95:[.09514,0,0,0,.525],96:[0,.61111,0,0,.525],97:[0,.43056,0,0,.525],98:[0,.61111,0,0,.525],99:[0,.43056,0,0,.525],100:[0,.61111,0,0,.525],101:[0,.43056,0,0,.525],102:[0,.61111,0,0,.525],103:[.22222,.43056,0,0,.525],104:[0,.61111,0,0,.525],105:[0,.61111,0,0,.525],106:[.22222,.61111,0,0,.525],107:[0,.61111,0,0,.525],108:[0,.61111,0,0,.525],109:[0,.43056,0,0,.525],110:[0,.43056,0,0,.525],111:[0,.43056,0,0,.525],112:[.22222,.43056,0,0,.525],113:[.22222,.43056,0,0,.525],114:[0,.43056,0,0,.525],115:[0,.43056,0,0,.525],116:[0,.55358,0,0,.525],117:[0,.43056,0,0,.525],118:[0,.43056,0,0,.525],119:[0,.43056,0,0,.525],120:[0,.43056,0,0,.525],121:[.22222,.43056,0,0,.525],122:[0,.43056,0,0,.525],123:[.08333,.69444,0,0,.525],124:[.08333,.69444,0,0,.525],125:[.08333,.69444,0,0,.525],126:[0,.61111,0,0,.525],127:[0,.61111,0,0,.525],160:[0,0,0,0,.525],176:[0,.61111,0,0,.525],184:[.19445,0,0,0,.525],305:[0,.43056,0,0,.525],567:[.22222,.43056,0,0,.525],711:[0,.56597,0,0,.525],713:[0,.56555,0,0,.525],714:[0,.61111,0,0,.525],715:[0,.61111,0,0,.525],728:[0,.61111,0,0,.525],730:[0,.61111,0,0,.525],770:[0,.61111,0,0,.525],771:[0,.61111,0,0,.525],776:[0,.61111,0,0,.525],915:[0,.61111,0,0,.525],916:[0,.61111,0,0,.525],920:[0,.61111,0,0,.525],923:[0,.61111,0,0,.525],926:[0,.61111,0,0,.525],928:[0,.61111,0,0,.525],931:[0,.61111,0,0,.525],933:[0,.61111,0,0,.525],934:[0,.61111,0,0,.525],936:[0,.61111,0,0,.525],937:[0,.61111,0,0,.525],8216:[0,.61111,0,0,.525],8217:[0,.61111,0,0,.525],8242:[0,.61111,0,0,.525],9251:[.11111,.21944,0,0,.525]}},w={slant:[.25,.25,.25],space:[0,0,0],stretch:[0,0,0],shrink:[0,0,0],xHeight:[.431,.431,.431],quad:[1,1.171,1.472],extraSpace:[0,0,0],num1:[.677,.732,.925],num2:[.394,.384,.387],num3:[.444,.471,.504],denom1:[.686,.752,1.025],denom2:[.345,.344,.532],sup1:[.413,.503,.504],sup2:[.363,.431,.404],sup3:[.289,.286,.294],sub1:[.15,.143,.2],sub2:[.247,.286,.4],supDrop:[.386,.353,.494],subDrop:[.05,.071,.1],delim1:[2.39,1.7,1.98],delim2:[1.01,1.157,1.42],axisHeight:[.25,.25,.25],defaultRuleThickness:[.04,.049,.049],bigOpSpacing1:[.111,.111,.111],bigOpSpacing2:[.166,.166,.166],bigOpSpacing3:[.2,.2,.2],bigOpSpacing4:[.6,.611,.611],bigOpSpacing5:[.1,.143,.143],sqrtRuleThickness:[.04,.04,.04],ptPerEm:[10,10,10],doubleRuleSep:[.2,.2,.2],arrayRuleWidth:[.04,.04,.04],fboxsep:[.3,.3,.3],fboxrule:[.04,.04,.04]},k={Å:"A",Ð:"D",Þ:"o",å:"a",ð:"d",þ:"o",А:"A",Б:"B",В:"B",Г:"F",Д:"A",Е:"E",Ж:"K",З:"3",И:"N",Й:"N",К:"K",Л:"N",М:"M",Н:"H",О:"O",П:"N",Р:"P",С:"C",Т:"T",У:"y",Ф:"O",Х:"X",Ц:"U",Ч:"h",Ш:"W",Щ:"W",Ъ:"B",Ы:"X",Ь:"B",Э:"3",Ю:"X",Я:"R",а:"a",б:"b",в:"a",г:"r",д:"y",е:"e",ж:"m",з:"e",и:"n",й:"n",к:"n",л:"n",м:"m",н:"n",о:"o",п:"n",р:"p",с:"c",т:"o",у:"y",ф:"b",х:"x",ц:"n",ч:"n",ш:"w",щ:"w",ъ:"a",ы:"m",ь:"a",э:"e",ю:"m",я:"r"};function getCharacterMetrics(e,t,r){if(!x[t])throw Error("Font metrics not found for font: "+t+".");var n=e.charCodeAt(0),i=x[t][n];if(!i&&e[0]in k&&(n=k[e[0]].charCodeAt(0),i=x[t][n]),!i&&"text"===r&&supportedCodepoint(n)&&(i=x[t][77]),i)return{depth:i[0],height:i[1],italic:i[2],skew:i[3],width:i[4]}}var M={},z=[[1,1,1],[2,1,1],[3,1,1],[4,2,1],[5,2,1],[6,3,1],[7,4,2],[8,6,3],[9,7,6],[10,8,7],[11,10,9]],T=[.5,.6,.7,.8,.9,1,1.2,1.44,1.728,2.074,2.488],sizeAtStyle=function(e,t){return t.size<2?e:z[e-1][t.size-1]};let Options=class Options{constructor(e){this.style=void 0,this.color=void 0,this.size=void 0,this.textSize=void 0,this.phantom=void 0,this.font=void 0,this.fontFamily=void 0,this.fontWeight=void 0,this.fontShape=void 0,this.sizeMultiplier=void 0,this.maxSize=void 0,this.minRuleThickness=void 0,this._fontMetrics=void 0,this.style=e.style,this.color=e.color,this.size=e.size||Options.BASESIZE,this.textSize=e.textSize||this.size,this.phantom=!!e.phantom,this.font=e.font||"",this.fontFamily=e.fontFamily||"",this.fontWeight=e.fontWeight||"",this.fontShape=e.fontShape||"",this.sizeMultiplier=T[this.size-1],this.maxSize=e.maxSize,this.minRuleThickness=e.minRuleThickness,this._fontMetrics=void 0}extend(e){var t={style:this.style,size:this.size,textSize:this.textSize,color:this.color,phantom:this.phantom,font:this.font,fontFamily:this.fontFamily,fontWeight:this.fontWeight,fontShape:this.fontShape,maxSize:this.maxSize,minRuleThickness:this.minRuleThickness};for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r]);return new Options(t)}havingStyle(e){return this.style===e?this:this.extend({style:e,size:sizeAtStyle(this.textSize,e)})}havingCrampedStyle(){return this.havingStyle(this.style.cramp())}havingSize(e){return this.size===e&&this.textSize===e?this:this.extend({style:this.style.text(),size:e,textSize:e,sizeMultiplier:T[e-1]})}havingBaseStyle(e){e=e||this.style.text();var t=sizeAtStyle(Options.BASESIZE,e);return this.size===t&&this.textSize===Options.BASESIZE&&this.style===e?this:this.extend({style:e,size:t})}havingBaseSizing(){var e;switch(this.style.id){case 4:case 5:e=3;break;case 6:case 7:e=1;break;default:e=6}return this.extend({style:this.style.text(),size:e})}withColor(e){return this.extend({color:e})}withPhantom(){return this.extend({phantom:!0})}withFont(e){return this.extend({font:e})}withTextFontFamily(e){return this.extend({fontFamily:e,font:""})}withTextFontWeight(e){return this.extend({fontWeight:e,font:""})}withTextFontShape(e){return this.extend({fontShape:e,font:""})}sizingClasses(e){return e.size!==this.size?["sizing","reset-size"+e.size,"size"+this.size]:[]}baseSizingClasses(){return this.size!==Options.BASESIZE?["sizing","reset-size"+this.size,"size"+Options.BASESIZE]:[]}fontMetrics(){return this._fontMetrics||(this._fontMetrics=function(e){var t;if(!M[t=e>=5?0:e>=3?1:2]){var r=M[t]={cssEmPerMu:w.quad[t]/18};for(var n in w)w.hasOwnProperty(n)&&(r[n]=w[n][t])}return M[t]}(this.size)),this._fontMetrics}getColor(){return this.phantom?"transparent":this.color}};Options.BASESIZE=6;var A={pt:1,mm:7227/2540,cm:7227/254,in:72.27,bp:1.00375,pc:12,dd:1238/1157,cc:14856/1157,nd:685/642,nc:1370/107,sp:1/65536,px:1.00375},E={ex:!0,em:!0,mu:!0},validUnit=function(e){return"string"!=typeof e&&(e=e.unit),e in A||e in E||"ex"===e},calculateSize=function(e,t){var r,n;if(e.unit in A)r=A[e.unit]/t.fontMetrics().ptPerEm/t.sizeMultiplier;else if("mu"===e.unit)r=t.fontMetrics().cssEmPerMu;else{if(n=t.style.isTight()?t.havingStyle(t.style.text()):t,"ex"===e.unit)r=n.fontMetrics().xHeight;else if("em"===e.unit)r=n.fontMetrics().quad;else throw new ParseError("Invalid unit: '"+e.unit+"'");n!==t&&(r*=n.sizeMultiplier/t.sizeMultiplier)}return Math.min(e.number*r,t.maxSize)},makeEm=function(e){return+e.toFixed(4)+"em"},createClass=function(e){return e.filter(e=>e).join(" ")},initNode=function(e,t,r){if(this.classes=e||[],this.attributes={},this.height=0,this.depth=0,this.maxFontSize=0,this.style=r||{},t){t.style.isTight()&&this.classes.push("mtight");var n=t.getColor();n&&(this.style.color=n)}},toNode=function(e){var t=document.createElement(e);for(var r in t.className=createClass(this.classes),this.style)this.style.hasOwnProperty(r)&&(t.style[r]=this.style[r]);for(var n in this.attributes)this.attributes.hasOwnProperty(n)&&t.setAttribute(n,this.attributes[n]);for(var i=0;i<this.children.length;i++)t.appendChild(this.children[i].toNode());return t},B=/[\s"'>/=\x00-\x1f]/,toMarkup=function(e){var t="<"+e;this.classes.length&&(t+=' class="'+s.escape(createClass(this.classes))+'"');var r="";for(var n in this.style)this.style.hasOwnProperty(n)&&(r+=s.hyphenate(n)+":"+this.style[n]+";");for(var i in r&&(t+=' style="'+s.escape(r)+'"'),this.attributes)if(this.attributes.hasOwnProperty(i)){if(B.test(i))throw new ParseError("Invalid attribute name '"+i+"'");t+=" "+i+'="'+s.escape(this.attributes[i])+'"'}t+=">";for(var a=0;a<this.children.length;a++)t+=this.children[a].toMarkup();return t+("</"+e)+">"};let Span=class Span{constructor(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.width=void 0,this.maxFontSize=void 0,this.style=void 0,initNode.call(this,e,r,n),this.children=t||[]}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return s.contains(this.classes,e)}toNode(){return toNode.call(this,"span")}toMarkup(){return toMarkup.call(this,"span")}};let Anchor=class Anchor{constructor(e,t,r,n){this.children=void 0,this.attributes=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,initNode.call(this,t,n),this.children=r||[],this.setAttribute("href",e)}setAttribute(e,t){this.attributes[e]=t}hasClass(e){return s.contains(this.classes,e)}toNode(){return toNode.call(this,"a")}toMarkup(){return toMarkup.call(this,"a")}};let Img=class Img{constructor(e,t,r){this.src=void 0,this.alt=void 0,this.classes=void 0,this.height=void 0,this.depth=void 0,this.maxFontSize=void 0,this.style=void 0,this.alt=t,this.src=e,this.classes=["mord"],this.style=r}hasClass(e){return s.contains(this.classes,e)}toNode(){var e=document.createElement("img");for(var t in e.src=this.src,e.alt=this.alt,e.className="mord",this.style)this.style.hasOwnProperty(t)&&(e.style[t]=this.style[t]);return e}toMarkup(){var e='<img src="'+s.escape(this.src)+'" alt="'+s.escape(this.alt)+'"',t="";for(var r in this.style)this.style.hasOwnProperty(r)&&(t+=s.hyphenate(r)+":"+this.style[r]+";");return t&&(e+=' style="'+s.escape(t)+'"'),e+="'/>"}};var N={î:"ı̂",ï:"ı̈",í:"ı́",ì:"ı̀"};let SymbolNode=class SymbolNode{constructor(e,t,r,n,i,a,o,l){this.text=void 0,this.height=void 0,this.depth=void 0,this.italic=void 0,this.skew=void 0,this.width=void 0,this.maxFontSize=void 0,this.classes=void 0,this.style=void 0,this.text=e,this.height=t||0,this.depth=r||0,this.italic=n||0,this.skew=i||0,this.width=a||0,this.classes=o||[],this.style=l||{},this.maxFontSize=0;var s=function(e){for(var t=0;t<y.length;t++)for(var r=y[t],n=0;n<r.blocks.length;n++){var i=r.blocks[n];if(e>=i[0]&&e<=i[1])return r.name}return null}(this.text.charCodeAt(0));s&&this.classes.push(s+"_fallback"),/[îïíì]/.test(this.text)&&(this.text=N[this.text])}hasClass(e){return s.contains(this.classes,e)}toNode(){var e=document.createTextNode(this.text),t=null;for(var r in this.italic>0&&((t=document.createElement("span")).style.marginRight=makeEm(this.italic)),this.classes.length>0&&((t=t||document.createElement("span")).className=createClass(this.classes)),this.style)this.style.hasOwnProperty(r)&&((t=t||document.createElement("span")).style[r]=this.style[r]);return t?(t.appendChild(e),t):e}toMarkup(){var e=!1,t="<span";this.classes.length&&(e=!0,t+=' class="'+s.escape(createClass(this.classes))+'"');var r="";for(var n in this.italic>0&&(r+="margin-right:"+this.italic+"em;"),this.style)this.style.hasOwnProperty(n)&&(r+=s.hyphenate(n)+":"+this.style[n]+";");r&&(e=!0,t+=' style="'+s.escape(r)+'"');var i=s.escape(this.text);return e?t+=">"+i+"</span>":i}};let SvgNode=class SvgNode{constructor(e,t){this.children=void 0,this.attributes=void 0,this.children=e||[],this.attributes=t||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","svg");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);for(var r=0;r<this.children.length;r++)e.appendChild(this.children[r].toNode());return e}toMarkup(){var e='<svg xmlns="http://www.w3.org/2000/svg"';for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+s.escape(this.attributes[t])+'"');e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+"</svg>"}};let PathNode=class PathNode{constructor(e,t){this.pathName=void 0,this.alternate=void 0,this.pathName=e,this.alternate=t}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","path");return this.alternate?e.setAttribute("d",this.alternate):e.setAttribute("d",S[this.pathName]),e}toMarkup(){return this.alternate?'<path d="'+s.escape(this.alternate)+'"/>':'<path d="'+s.escape(S[this.pathName])+'"/>'}};let LineNode=class LineNode{constructor(e){this.attributes=void 0,this.attributes=e||{}}toNode(){var e=document.createElementNS("http://www.w3.org/2000/svg","line");for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);return e}toMarkup(){var e="<line";for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+s.escape(this.attributes[t])+'"');return e+"/>"}};function assertSymbolDomNode(e){if(e instanceof SymbolNode)return e;throw Error("Expected symbolNode but got "+String(e)+".")}var C={bin:1,close:1,inner:1,open:1,punct:1,rel:1},q={"accent-token":1,mathord:1,"op-token":1,spacing:1,textord:1},P={math:{},text:{}};function defineSymbol(e,t,r,n,i,a){P[e][i]={font:t,group:r,replace:n},a&&n&&(P[e][n]=P[e][i])}var F="math",H="text",R="main",I="accent-token",D="close",L="inner",O="mathord",$="op-token",G="open",V="punct",U="spacing",Y="textord";defineSymbol(F,R,"rel","≡","\\equiv",!0),defineSymbol(F,R,"rel","≺","\\prec",!0),defineSymbol(F,R,"rel","≻","\\succ",!0),defineSymbol(F,R,"rel","∼","\\sim",!0),defineSymbol(F,R,"rel","⊥","\\perp"),defineSymbol(F,R,"rel","⪯","\\preceq",!0),defineSymbol(F,R,"rel","⪰","\\succeq",!0),defineSymbol(F,R,"rel","≃","\\simeq",!0),defineSymbol(F,R,"rel","∣","\\mid",!0),defineSymbol(F,R,"rel","≪","\\ll",!0),defineSymbol(F,R,"rel","≫","\\gg",!0),defineSymbol(F,R,"rel","≍","\\asymp",!0),defineSymbol(F,R,"rel","∥","\\parallel"),defineSymbol(F,R,"rel","⋈","\\bowtie",!0),defineSymbol(F,R,"rel","⌣","\\smile",!0),defineSymbol(F,R,"rel","⊑","\\sqsubseteq",!0),defineSymbol(F,R,"rel","⊒","\\sqsupseteq",!0),defineSymbol(F,R,"rel","≐","\\doteq",!0),defineSymbol(F,R,"rel","⌢","\\frown",!0),defineSymbol(F,R,"rel","∋","\\ni",!0),defineSymbol(F,R,"rel","∝","\\propto",!0),defineSymbol(F,R,"rel","⊢","\\vdash",!0),defineSymbol(F,R,"rel","⊣","\\dashv",!0),defineSymbol(F,R,"rel","∋","\\owns"),defineSymbol(F,R,V,".","\\ldotp"),defineSymbol(F,R,V,"⋅","\\cdotp"),defineSymbol(F,R,Y,"#","\\#"),defineSymbol(H,R,Y,"#","\\#"),defineSymbol(F,R,Y,"&","\\&"),defineSymbol(H,R,Y,"&","\\&"),defineSymbol(F,R,Y,"ℵ","\\aleph",!0),defineSymbol(F,R,Y,"∀","\\forall",!0),defineSymbol(F,R,Y,"ℏ","\\hbar",!0),defineSymbol(F,R,Y,"∃","\\exists",!0),defineSymbol(F,R,Y,"∇","\\nabla",!0),defineSymbol(F,R,Y,"♭","\\flat",!0),defineSymbol(F,R,Y,"ℓ","\\ell",!0),defineSymbol(F,R,Y,"♮","\\natural",!0),defineSymbol(F,R,Y,"♣","\\clubsuit",!0),defineSymbol(F,R,Y,"℘","\\wp",!0),defineSymbol(F,R,Y,"♯","\\sharp",!0),defineSymbol(F,R,Y,"♢","\\diamondsuit",!0),defineSymbol(F,R,Y,"ℜ","\\Re",!0),defineSymbol(F,R,Y,"♡","\\heartsuit",!0),defineSymbol(F,R,Y,"ℑ","\\Im",!0),defineSymbol(F,R,Y,"♠","\\spadesuit",!0),defineSymbol(F,R,Y,"\xa7","\\S",!0),defineSymbol(H,R,Y,"\xa7","\\S"),defineSymbol(F,R,Y,"\xb6","\\P",!0),defineSymbol(H,R,Y,"\xb6","\\P"),defineSymbol(F,R,Y,"†","\\dag"),defineSymbol(H,R,Y,"†","\\dag"),defineSymbol(H,R,Y,"†","\\textdagger"),defineSymbol(F,R,Y,"‡","\\ddag"),defineSymbol(H,R,Y,"‡","\\ddag"),defineSymbol(H,R,Y,"‡","\\textdaggerdbl"),defineSymbol(F,R,D,"⎱","\\rmoustache",!0),defineSymbol(F,R,G,"⎰","\\lmoustache",!0),defineSymbol(F,R,D,"⟯","\\rgroup",!0),defineSymbol(F,R,G,"⟮","\\lgroup",!0),defineSymbol(F,R,"bin","∓","\\mp",!0),defineSymbol(F,R,"bin","⊖","\\ominus",!0),defineSymbol(F,R,"bin","⊎","\\uplus",!0),defineSymbol(F,R,"bin","⊓","\\sqcap",!0),defineSymbol(F,R,"bin","∗","\\ast"),defineSymbol(F,R,"bin","⊔","\\sqcup",!0),defineSymbol(F,R,"bin","◯","\\bigcirc",!0),defineSymbol(F,R,"bin","∙","\\bullet",!0),defineSymbol(F,R,"bin","‡","\\ddagger"),defineSymbol(F,R,"bin","≀","\\wr",!0),defineSymbol(F,R,"bin","⨿","\\amalg"),defineSymbol(F,R,"bin","&","\\And"),defineSymbol(F,R,"rel","⟵","\\longleftarrow",!0),defineSymbol(F,R,"rel","⇐","\\Leftarrow",!0),defineSymbol(F,R,"rel","⟸","\\Longleftarrow",!0),defineSymbol(F,R,"rel","⟶","\\longrightarrow",!0),defineSymbol(F,R,"rel","⇒","\\Rightarrow",!0),defineSymbol(F,R,"rel","⟹","\\Longrightarrow",!0),defineSymbol(F,R,"rel","↔","\\leftrightarrow",!0),defineSymbol(F,R,"rel","⟷","\\longleftrightarrow",!0),defineSymbol(F,R,"rel","⇔","\\Leftrightarrow",!0),defineSymbol(F,R,"rel","⟺","\\Longleftrightarrow",!0),defineSymbol(F,R,"rel","↦","\\mapsto",!0),defineSymbol(F,R,"rel","⟼","\\longmapsto",!0),defineSymbol(F,R,"rel","↗","\\nearrow",!0),defineSymbol(F,R,"rel","↩","\\hookleftarrow",!0),defineSymbol(F,R,"rel","↪","\\hookrightarrow",!0),defineSymbol(F,R,"rel","↘","\\searrow",!0),defineSymbol(F,R,"rel","↼","\\leftharpoonup",!0),defineSymbol(F,R,"rel","⇀","\\rightharpoonup",!0),defineSymbol(F,R,"rel","↙","\\swarrow",!0),defineSymbol(F,R,"rel","↽","\\leftharpoondown",!0),defineSymbol(F,R,"rel","⇁","\\rightharpoondown",!0),defineSymbol(F,R,"rel","↖","\\nwarrow",!0),defineSymbol(F,R,"rel","⇌","\\rightleftharpoons",!0),defineSymbol(F,"ams","rel","≮","\\nless",!0),defineSymbol(F,"ams","rel","","\\@nleqslant"),defineSymbol(F,"ams","rel","","\\@nleqq"),defineSymbol(F,"ams","rel","⪇","\\lneq",!0),defineSymbol(F,"ams","rel","≨","\\lneqq",!0),defineSymbol(F,"ams","rel","","\\@lvertneqq"),defineSymbol(F,"ams","rel","⋦","\\lnsim",!0),defineSymbol(F,"ams","rel","⪉","\\lnapprox",!0),defineSymbol(F,"ams","rel","⊀","\\nprec",!0),defineSymbol(F,"ams","rel","⋠","\\npreceq",!0),defineSymbol(F,"ams","rel","⋨","\\precnsim",!0),defineSymbol(F,"ams","rel","⪹","\\precnapprox",!0),defineSymbol(F,"ams","rel","≁","\\nsim",!0),defineSymbol(F,"ams","rel","","\\@nshortmid"),defineSymbol(F,"ams","rel","∤","\\nmid",!0),defineSymbol(F,"ams","rel","⊬","\\nvdash",!0),defineSymbol(F,"ams","rel","⊭","\\nvDash",!0),defineSymbol(F,"ams","rel","⋪","\\ntriangleleft"),defineSymbol(F,"ams","rel","⋬","\\ntrianglelefteq",!0),defineSymbol(F,"ams","rel","⊊","\\subsetneq",!0),defineSymbol(F,"ams","rel","","\\@varsubsetneq"),defineSymbol(F,"ams","rel","⫋","\\subsetneqq",!0),defineSymbol(F,"ams","rel","","\\@varsubsetneqq"),defineSymbol(F,"ams","rel","≯","\\ngtr",!0),defineSymbol(F,"ams","rel","","\\@ngeqslant"),defineSymbol(F,"ams","rel","","\\@ngeqq"),defineSymbol(F,"ams","rel","⪈","\\gneq",!0),defineSymbol(F,"ams","rel","≩","\\gneqq",!0),defineSymbol(F,"ams","rel","","\\@gvertneqq"),defineSymbol(F,"ams","rel","⋧","\\gnsim",!0),defineSymbol(F,"ams","rel","⪊","\\gnapprox",!0),defineSymbol(F,"ams","rel","⊁","\\nsucc",!0),defineSymbol(F,"ams","rel","⋡","\\nsucceq",!0),defineSymbol(F,"ams","rel","⋩","\\succnsim",!0),defineSymbol(F,"ams","rel","⪺","\\succnapprox",!0),defineSymbol(F,"ams","rel","≆","\\ncong",!0),defineSymbol(F,"ams","rel","","\\@nshortparallel"),defineSymbol(F,"ams","rel","∦","\\nparallel",!0),defineSymbol(F,"ams","rel","⊯","\\nVDash",!0),defineSymbol(F,"ams","rel","⋫","\\ntriangleright"),defineSymbol(F,"ams","rel","⋭","\\ntrianglerighteq",!0),defineSymbol(F,"ams","rel","","\\@nsupseteqq"),defineSymbol(F,"ams","rel","⊋","\\supsetneq",!0),defineSymbol(F,"ams","rel","","\\@varsupsetneq"),defineSymbol(F,"ams","rel","⫌","\\supsetneqq",!0),defineSymbol(F,"ams","rel","","\\@varsupsetneqq"),defineSymbol(F,"ams","rel","⊮","\\nVdash",!0),defineSymbol(F,"ams","rel","⪵","\\precneqq",!0),defineSymbol(F,"ams","rel","⪶","\\succneqq",!0),defineSymbol(F,"ams","rel","","\\@nsubseteqq"),defineSymbol(F,"ams","bin","⊴","\\unlhd"),defineSymbol(F,"ams","bin","⊵","\\unrhd"),defineSymbol(F,"ams","rel","↚","\\nleftarrow",!0),defineSymbol(F,"ams","rel","↛","\\nrightarrow",!0),defineSymbol(F,"ams","rel","⇍","\\nLeftarrow",!0),defineSymbol(F,"ams","rel","⇏","\\nRightarrow",!0),defineSymbol(F,"ams","rel","↮","\\nleftrightarrow",!0),defineSymbol(F,"ams","rel","⇎","\\nLeftrightarrow",!0),defineSymbol(F,"ams","rel","△","\\vartriangle"),defineSymbol(F,"ams",Y,"ℏ","\\hslash"),defineSymbol(F,"ams",Y,"▽","\\triangledown"),defineSymbol(F,"ams",Y,"◊","\\lozenge"),defineSymbol(F,"ams",Y,"Ⓢ","\\circledS"),defineSymbol(F,"ams",Y,"\xae","\\circledR"),defineSymbol(H,"ams",Y,"\xae","\\circledR"),defineSymbol(F,"ams",Y,"∡","\\measuredangle",!0),defineSymbol(F,"ams",Y,"∄","\\nexists"),defineSymbol(F,"ams",Y,"℧","\\mho"),defineSymbol(F,"ams",Y,"Ⅎ","\\Finv",!0),defineSymbol(F,"ams",Y,"⅁","\\Game",!0),defineSymbol(F,"ams",Y,"‵","\\backprime"),defineSymbol(F,"ams",Y,"▲","\\blacktriangle"),defineSymbol(F,"ams",Y,"▼","\\blacktriangledown"),defineSymbol(F,"ams",Y,"■","\\blacksquare"),defineSymbol(F,"ams",Y,"⧫","\\blacklozenge"),defineSymbol(F,"ams",Y,"★","\\bigstar"),defineSymbol(F,"ams",Y,"∢","\\sphericalangle",!0),defineSymbol(F,"ams",Y,"∁","\\complement",!0),defineSymbol(F,"ams",Y,"\xf0","\\eth",!0),defineSymbol(H,R,Y,"\xf0","\xf0"),defineSymbol(F,"ams",Y,"╱","\\diagup"),defineSymbol(F,"ams",Y,"╲","\\diagdown"),defineSymbol(F,"ams",Y,"□","\\square"),defineSymbol(F,"ams",Y,"□","\\Box"),defineSymbol(F,"ams",Y,"◊","\\Diamond"),defineSymbol(F,"ams",Y,"\xa5","\\yen",!0),defineSymbol(H,"ams",Y,"\xa5","\\yen",!0),defineSymbol(F,"ams",Y,"✓","\\checkmark",!0),defineSymbol(H,"ams",Y,"✓","\\checkmark"),defineSymbol(F,"ams",Y,"ℶ","\\beth",!0),defineSymbol(F,"ams",Y,"ℸ","\\daleth",!0),defineSymbol(F,"ams",Y,"ℷ","\\gimel",!0),defineSymbol(F,"ams",Y,"ϝ","\\digamma",!0),defineSymbol(F,"ams",Y,"ϰ","\\varkappa"),defineSymbol(F,"ams",G,"┌","\\@ulcorner",!0),defineSymbol(F,"ams",D,"┐","\\@urcorner",!0),defineSymbol(F,"ams",G,"└","\\@llcorner",!0),defineSymbol(F,"ams",D,"┘","\\@lrcorner",!0),defineSymbol(F,"ams","rel","≦","\\leqq",!0),defineSymbol(F,"ams","rel","⩽","\\leqslant",!0),defineSymbol(F,"ams","rel","⪕","\\eqslantless",!0),defineSymbol(F,"ams","rel","≲","\\lesssim",!0),defineSymbol(F,"ams","rel","⪅","\\lessapprox",!0),defineSymbol(F,"ams","rel","≊","\\approxeq",!0),defineSymbol(F,"ams","bin","⋖","\\lessdot"),defineSymbol(F,"ams","rel","⋘","\\lll",!0),defineSymbol(F,"ams","rel","≶","\\lessgtr",!0),defineSymbol(F,"ams","rel","⋚","\\lesseqgtr",!0),defineSymbol(F,"ams","rel","⪋","\\lesseqqgtr",!0),defineSymbol(F,"ams","rel","≑","\\doteqdot"),defineSymbol(F,"ams","rel","≓","\\risingdotseq",!0),defineSymbol(F,"ams","rel","≒","\\fallingdotseq",!0),defineSymbol(F,"ams","rel","∽","\\backsim",!0),defineSymbol(F,"ams","rel","⋍","\\backsimeq",!0),defineSymbol(F,"ams","rel","⫅","\\subseteqq",!0),defineSymbol(F,"ams","rel","⋐","\\Subset",!0),defineSymbol(F,"ams","rel","⊏","\\sqsubset",!0),defineSymbol(F,"ams","rel","≼","\\preccurlyeq",!0),defineSymbol(F,"ams","rel","⋞","\\curlyeqprec",!0),defineSymbol(F,"ams","rel","≾","\\precsim",!0),defineSymbol(F,"ams","rel","⪷","\\precapprox",!0),defineSymbol(F,"ams","rel","⊲","\\vartriangleleft"),defineSymbol(F,"ams","rel","⊴","\\trianglelefteq"),defineSymbol(F,"ams","rel","⊨","\\vDash",!0),defineSymbol(F,"ams","rel","⊪","\\Vvdash",!0),defineSymbol(F,"ams","rel","⌣","\\smallsmile"),defineSymbol(F,"ams","rel","⌢","\\smallfrown"),defineSymbol(F,"ams","rel","≏","\\bumpeq",!0),defineSymbol(F,"ams","rel","≎","\\Bumpeq",!0),defineSymbol(F,"ams","rel","≧","\\geqq",!0),defineSymbol(F,"ams","rel","⩾","\\geqslant",!0),defineSymbol(F,"ams","rel","⪖","\\eqslantgtr",!0),defineSymbol(F,"ams","rel","≳","\\gtrsim",!0),defineSymbol(F,"ams","rel","⪆","\\gtrapprox",!0),defineSymbol(F,"ams","bin","⋗","\\gtrdot"),defineSymbol(F,"ams","rel","⋙","\\ggg",!0),defineSymbol(F,"ams","rel","≷","\\gtrless",!0),defineSymbol(F,"ams","rel","⋛","\\gtreqless",!0),defineSymbol(F,"ams","rel","⪌","\\gtreqqless",!0),defineSymbol(F,"ams","rel","≖","\\eqcirc",!0),defineSymbol(F,"ams","rel","≗","\\circeq",!0),defineSymbol(F,"ams","rel","≜","\\triangleq",!0),defineSymbol(F,"ams","rel","∼","\\thicksim"),defineSymbol(F,"ams","rel","≈","\\thickapprox"),defineSymbol(F,"ams","rel","⫆","\\supseteqq",!0),defineSymbol(F,"ams","rel","⋑","\\Supset",!0),defineSymbol(F,"ams","rel","⊐","\\sqsupset",!0),defineSymbol(F,"ams","rel","≽","\\succcurlyeq",!0),defineSymbol(F,"ams","rel","⋟","\\curlyeqsucc",!0),defineSymbol(F,"ams","rel","≿","\\succsim",!0),defineSymbol(F,"ams","rel","⪸","\\succapprox",!0),defineSymbol(F,"ams","rel","⊳","\\vartriangleright"),defineSymbol(F,"ams","rel","⊵","\\trianglerighteq"),defineSymbol(F,"ams","rel","⊩","\\Vdash",!0),defineSymbol(F,"ams","rel","∣","\\shortmid"),defineSymbol(F,"ams","rel","∥","\\shortparallel"),defineSymbol(F,"ams","rel","≬","\\between",!0),defineSymbol(F,"ams","rel","⋔","\\pitchfork",!0),defineSymbol(F,"ams","rel","∝","\\varpropto"),defineSymbol(F,"ams","rel","◀","\\blacktriangleleft"),defineSymbol(F,"ams","rel","∴","\\therefore",!0),defineSymbol(F,"ams","rel","∍","\\backepsilon"),defineSymbol(F,"ams","rel","▶","\\blacktriangleright"),defineSymbol(F,"ams","rel","∵","\\because",!0),defineSymbol(F,"ams","rel","⋘","\\llless"),defineSymbol(F,"ams","rel","⋙","\\gggtr"),defineSymbol(F,"ams","bin","⊲","\\lhd"),defineSymbol(F,"ams","bin","⊳","\\rhd"),defineSymbol(F,"ams","rel","≂","\\eqsim",!0),defineSymbol(F,R,"rel","⋈","\\Join"),defineSymbol(F,"ams","rel","≑","\\Doteq",!0),defineSymbol(F,"ams","bin","∔","\\dotplus",!0),defineSymbol(F,"ams","bin","∖","\\smallsetminus"),defineSymbol(F,"ams","bin","⋒","\\Cap",!0),defineSymbol(F,"ams","bin","⋓","\\Cup",!0),defineSymbol(F,"ams","bin","⩞","\\doublebarwedge",!0),defineSymbol(F,"ams","bin","⊟","\\boxminus",!0),defineSymbol(F,"ams","bin","⊞","\\boxplus",!0),defineSymbol(F,"ams","bin","⋇","\\divideontimes",!0),defineSymbol(F,"ams","bin","⋉","\\ltimes",!0),defineSymbol(F,"ams","bin","⋊","\\rtimes",!0),defineSymbol(F,"ams","bin","⋋","\\leftthreetimes",!0),defineSymbol(F,"ams","bin","⋌","\\rightthreetimes",!0),defineSymbol(F,"ams","bin","⋏","\\curlywedge",!0),defineSymbol(F,"ams","bin","⋎","\\curlyvee",!0),defineSymbol(F,"ams","bin","⊝","\\circleddash",!0),defineSymbol(F,"ams","bin","⊛","\\circledast",!0),defineSymbol(F,"ams","bin","⋅","\\centerdot"),defineSymbol(F,"ams","bin","⊺","\\intercal",!0),defineSymbol(F,"ams","bin","⋒","\\doublecap"),defineSymbol(F,"ams","bin","⋓","\\doublecup"),defineSymbol(F,"ams","bin","⊠","\\boxtimes",!0),defineSymbol(F,"ams","rel","⇢","\\dashrightarrow",!0),defineSymbol(F,"ams","rel","⇠","\\dashleftarrow",!0),defineSymbol(F,"ams","rel","⇇","\\leftleftarrows",!0),defineSymbol(F,"ams","rel","⇆","\\leftrightarrows",!0),defineSymbol(F,"ams","rel","⇚","\\Lleftarrow",!0),defineSymbol(F,"ams","rel","↞","\\twoheadleftarrow",!0),defineSymbol(F,"ams","rel","↢","\\leftarrowtail",!0),defineSymbol(F,"ams","rel","↫","\\looparrowleft",!0),defineSymbol(F,"ams","rel","⇋","\\leftrightharpoons",!0),defineSymbol(F,"ams","rel","↶","\\curvearrowleft",!0),defineSymbol(F,"ams","rel","↺","\\circlearrowleft",!0),defineSymbol(F,"ams","rel","↰","\\Lsh",!0),defineSymbol(F,"ams","rel","⇈","\\upuparrows",!0),defineSymbol(F,"ams","rel","↿","\\upharpoonleft",!0),defineSymbol(F,"ams","rel","⇃","\\downharpoonleft",!0),defineSymbol(F,R,"rel","⊶","\\origof",!0),defineSymbol(F,R,"rel","⊷","\\imageof",!0),defineSymbol(F,"ams","rel","⊸","\\multimap",!0),defineSymbol(F,"ams","rel","↭","\\leftrightsquigarrow",!0),defineSymbol(F,"ams","rel","⇉","\\rightrightarrows",!0),defineSymbol(F,"ams","rel","⇄","\\rightleftarrows",!0),defineSymbol(F,"ams","rel","↠","\\twoheadrightarrow",!0),defineSymbol(F,"ams","rel","↣","\\rightarrowtail",!0),defineSymbol(F,"ams","rel","↬","\\looparrowright",!0),defineSymbol(F,"ams","rel","↷","\\curvearrowright",!0),defineSymbol(F,"ams","rel","↻","\\circlearrowright",!0),defineSymbol(F,"ams","rel","↱","\\Rsh",!0),defineSymbol(F,"ams","rel","⇊","\\downdownarrows",!0),defineSymbol(F,"ams","rel","↾","\\upharpoonright",!0),defineSymbol(F,"ams","rel","⇂","\\downharpoonright",!0),defineSymbol(F,"ams","rel","⇝","\\rightsquigarrow",!0),defineSymbol(F,"ams","rel","⇝","\\leadsto"),defineSymbol(F,"ams","rel","⇛","\\Rrightarrow",!0),defineSymbol(F,"ams","rel","↾","\\restriction"),defineSymbol(F,R,Y,"‘","`"),defineSymbol(F,R,Y,"$","\\$"),defineSymbol(H,R,Y,"$","\\$"),defineSymbol(H,R,Y,"$","\\textdollar"),defineSymbol(F,R,Y,"%","\\%"),defineSymbol(H,R,Y,"%","\\%"),defineSymbol(F,R,Y,"_","\\_"),defineSymbol(H,R,Y,"_","\\_"),defineSymbol(H,R,Y,"_","\\textunderscore"),defineSymbol(F,R,Y,"∠","\\angle",!0),defineSymbol(F,R,Y,"∞","\\infty",!0),defineSymbol(F,R,Y,"′","\\prime"),defineSymbol(F,R,Y,"△","\\triangle"),defineSymbol(F,R,Y,"Γ","\\Gamma",!0),defineSymbol(F,R,Y,"Δ","\\Delta",!0),defineSymbol(F,R,Y,"Θ","\\Theta",!0),defineSymbol(F,R,Y,"Λ","\\Lambda",!0),defineSymbol(F,R,Y,"Ξ","\\Xi",!0),defineSymbol(F,R,Y,"Π","\\Pi",!0),defineSymbol(F,R,Y,"Σ","\\Sigma",!0),defineSymbol(F,R,Y,"Υ","\\Upsilon",!0),defineSymbol(F,R,Y,"Φ","\\Phi",!0),defineSymbol(F,R,Y,"Ψ","\\Psi",!0),defineSymbol(F,R,Y,"Ω","\\Omega",!0),defineSymbol(F,R,Y,"A","Α"),defineSymbol(F,R,Y,"B","Β"),defineSymbol(F,R,Y,"E","Ε"),defineSymbol(F,R,Y,"Z","Ζ"),defineSymbol(F,R,Y,"H","Η"),defineSymbol(F,R,Y,"I","Ι"),defineSymbol(F,R,Y,"K","Κ"),defineSymbol(F,R,Y,"M","Μ"),defineSymbol(F,R,Y,"N","Ν"),defineSymbol(F,R,Y,"O","Ο"),defineSymbol(F,R,Y,"P","Ρ"),defineSymbol(F,R,Y,"T","Τ"),defineSymbol(F,R,Y,"X","Χ"),defineSymbol(F,R,Y,"\xac","\\neg",!0),defineSymbol(F,R,Y,"\xac","\\lnot"),defineSymbol(F,R,Y,"⊤","\\top"),defineSymbol(F,R,Y,"⊥","\\bot"),defineSymbol(F,R,Y,"∅","\\emptyset"),defineSymbol(F,"ams",Y,"∅","\\varnothing"),defineSymbol(F,R,O,"α","\\alpha",!0),defineSymbol(F,R,O,"β","\\beta",!0),defineSymbol(F,R,O,"γ","\\gamma",!0),defineSymbol(F,R,O,"δ","\\delta",!0),defineSymbol(F,R,O,"ϵ","\\epsilon",!0),defineSymbol(F,R,O,"ζ","\\zeta",!0),defineSymbol(F,R,O,"η","\\eta",!0),defineSymbol(F,R,O,"θ","\\theta",!0),defineSymbol(F,R,O,"ι","\\iota",!0),defineSymbol(F,R,O,"κ","\\kappa",!0),defineSymbol(F,R,O,"λ","\\lambda",!0),defineSymbol(F,R,O,"μ","\\mu",!0),defineSymbol(F,R,O,"ν","\\nu",!0),defineSymbol(F,R,O,"ξ","\\xi",!0),defineSymbol(F,R,O,"ο","\\omicron",!0),defineSymbol(F,R,O,"π","\\pi",!0),defineSymbol(F,R,O,"ρ","\\rho",!0),defineSymbol(F,R,O,"σ","\\sigma",!0),defineSymbol(F,R,O,"τ","\\tau",!0),defineSymbol(F,R,O,"υ","\\upsilon",!0),defineSymbol(F,R,O,"ϕ","\\phi",!0),defineSymbol(F,R,O,"χ","\\chi",!0),defineSymbol(F,R,O,"ψ","\\psi",!0),defineSymbol(F,R,O,"ω","\\omega",!0),defineSymbol(F,R,O,"ε","\\varepsilon",!0),defineSymbol(F,R,O,"ϑ","\\vartheta",!0),defineSymbol(F,R,O,"ϖ","\\varpi",!0),defineSymbol(F,R,O,"ϱ","\\varrho",!0),defineSymbol(F,R,O,"ς","\\varsigma",!0),defineSymbol(F,R,O,"φ","\\varphi",!0),defineSymbol(F,R,"bin","∗","*",!0),defineSymbol(F,R,"bin","+","+"),defineSymbol(F,R,"bin","−","-",!0),defineSymbol(F,R,"bin","⋅","\\cdot",!0),defineSymbol(F,R,"bin","∘","\\circ",!0),defineSymbol(F,R,"bin","\xf7","\\div",!0),defineSymbol(F,R,"bin","\xb1","\\pm",!0),defineSymbol(F,R,"bin","\xd7","\\times",!0),defineSymbol(F,R,"bin","∩","\\cap",!0),defineSymbol(F,R,"bin","∪","\\cup",!0),defineSymbol(F,R,"bin","∖","\\setminus",!0),defineSymbol(F,R,"bin","∧","\\land"),defineSymbol(F,R,"bin","∨","\\lor"),defineSymbol(F,R,"bin","∧","\\wedge",!0),defineSymbol(F,R,"bin","∨","\\vee",!0),defineSymbol(F,R,Y,"√","\\surd"),defineSymbol(F,R,G,"⟨","\\langle",!0),defineSymbol(F,R,G,"∣","\\lvert"),defineSymbol(F,R,G,"∥","\\lVert"),defineSymbol(F,R,D,"?","?"),defineSymbol(F,R,D,"!","!"),defineSymbol(F,R,D,"⟩","\\rangle",!0),defineSymbol(F,R,D,"∣","\\rvert"),defineSymbol(F,R,D,"∥","\\rVert"),defineSymbol(F,R,"rel","=","="),defineSymbol(F,R,"rel",":",":"),defineSymbol(F,R,"rel","≈","\\approx",!0),defineSymbol(F,R,"rel","≅","\\cong",!0),defineSymbol(F,R,"rel","≥","\\ge"),defineSymbol(F,R,"rel","≥","\\geq",!0),defineSymbol(F,R,"rel","←","\\gets"),defineSymbol(F,R,"rel",">","\\gt",!0),defineSymbol(F,R,"rel","∈","\\in",!0),defineSymbol(F,R,"rel","","\\@not"),defineSymbol(F,R,"rel","⊂","\\subset",!0),defineSymbol(F,R,"rel","⊃","\\supset",!0),defineSymbol(F,R,"rel","⊆","\\subseteq",!0),defineSymbol(F,R,"rel","⊇","\\supseteq",!0),defineSymbol(F,"ams","rel","⊈","\\nsubseteq",!0),defineSymbol(F,"ams","rel","⊉","\\nsupseteq",!0),defineSymbol(F,R,"rel","⊨","\\models"),defineSymbol(F,R,"rel","←","\\leftarrow",!0),defineSymbol(F,R,"rel","≤","\\le"),defineSymbol(F,R,"rel","≤","\\leq",!0),defineSymbol(F,R,"rel","<","\\lt",!0),defineSymbol(F,R,"rel","→","\\rightarrow",!0),defineSymbol(F,R,"rel","→","\\to"),defineSymbol(F,"ams","rel","≱","\\ngeq",!0),defineSymbol(F,"ams","rel","≰","\\nleq",!0),defineSymbol(F,R,U,"\xa0","\\ "),defineSymbol(F,R,U,"\xa0","\\space"),defineSymbol(F,R,U,"\xa0","\\nobreakspace"),defineSymbol(H,R,U,"\xa0","\\ "),defineSymbol(H,R,U,"\xa0"," "),defineSymbol(H,R,U,"\xa0","\\space"),defineSymbol(H,R,U,"\xa0","\\nobreakspace"),defineSymbol(F,R,U,null,"\\nobreak"),defineSymbol(F,R,U,null,"\\allowbreak"),defineSymbol(F,R,V,",",","),defineSymbol(F,R,V,";",";"),defineSymbol(F,"ams","bin","⊼","\\barwedge",!0),defineSymbol(F,"ams","bin","⊻","\\veebar",!0),defineSymbol(F,R,"bin","⊙","\\odot",!0),defineSymbol(F,R,"bin","⊕","\\oplus",!0),defineSymbol(F,R,"bin","⊗","\\otimes",!0),defineSymbol(F,R,Y,"∂","\\partial",!0),defineSymbol(F,R,"bin","⊘","\\oslash",!0),defineSymbol(F,"ams","bin","⊚","\\circledcirc",!0),defineSymbol(F,"ams","bin","⊡","\\boxdot",!0),defineSymbol(F,R,"bin","△","\\bigtriangleup"),defineSymbol(F,R,"bin","▽","\\bigtriangledown"),defineSymbol(F,R,"bin","†","\\dagger"),defineSymbol(F,R,"bin","⋄","\\diamond"),defineSymbol(F,R,"bin","⋆","\\star"),defineSymbol(F,R,"bin","◃","\\triangleleft"),defineSymbol(F,R,"bin","▹","\\triangleright"),defineSymbol(F,R,G,"{","\\{"),defineSymbol(H,R,Y,"{","\\{"),defineSymbol(H,R,Y,"{","\\textbraceleft"),defineSymbol(F,R,D,"}","\\}"),defineSymbol(H,R,Y,"}","\\}"),defineSymbol(H,R,Y,"}","\\textbraceright"),defineSymbol(F,R,G,"{","\\lbrace"),defineSymbol(F,R,D,"}","\\rbrace"),defineSymbol(F,R,G,"[","\\lbrack",!0),defineSymbol(H,R,Y,"[","\\lbrack",!0),defineSymbol(F,R,D,"]","\\rbrack",!0),defineSymbol(H,R,Y,"]","\\rbrack",!0),defineSymbol(F,R,G,"(","\\lparen",!0),defineSymbol(F,R,D,")","\\rparen",!0),defineSymbol(H,R,Y,"<","\\textless",!0),defineSymbol(H,R,Y,">","\\textgreater",!0),defineSymbol(F,R,G,"⌊","\\lfloor",!0),defineSymbol(F,R,D,"⌋","\\rfloor",!0),defineSymbol(F,R,G,"⌈","\\lceil",!0),defineSymbol(F,R,D,"⌉","\\rceil",!0),defineSymbol(F,R,Y,"\\","\\backslash"),defineSymbol(F,R,Y,"∣","|"),defineSymbol(F,R,Y,"∣","\\vert"),defineSymbol(H,R,Y,"|","\\textbar",!0),defineSymbol(F,R,Y,"∥","\\|"),defineSymbol(F,R,Y,"∥","\\Vert"),defineSymbol(H,R,Y,"∥","\\textbardbl"),defineSymbol(H,R,Y,"~","\\textasciitilde"),defineSymbol(H,R,Y,"\\","\\textbackslash"),defineSymbol(H,R,Y,"^","\\textasciicircum"),defineSymbol(F,R,"rel","↑","\\uparrow",!0),defineSymbol(F,R,"rel","⇑","\\Uparrow",!0),defineSymbol(F,R,"rel","↓","\\downarrow",!0),defineSymbol(F,R,"rel","⇓","\\Downarrow",!0),defineSymbol(F,R,"rel","↕","\\updownarrow",!0),defineSymbol(F,R,"rel","⇕","\\Updownarrow",!0),defineSymbol(F,R,$,"∐","\\coprod"),defineSymbol(F,R,$,"⋁","\\bigvee"),defineSymbol(F,R,$,"⋀","\\bigwedge"),defineSymbol(F,R,$,"⨄","\\biguplus"),defineSymbol(F,R,$,"⋂","\\bigcap"),defineSymbol(F,R,$,"⋃","\\bigcup"),defineSymbol(F,R,$,"∫","\\int"),defineSymbol(F,R,$,"∫","\\intop"),defineSymbol(F,R,$,"∬","\\iint"),defineSymbol(F,R,$,"∭","\\iiint"),defineSymbol(F,R,$,"∏","\\prod"),defineSymbol(F,R,$,"∑","\\sum"),defineSymbol(F,R,$,"⨂","\\bigotimes"),defineSymbol(F,R,$,"⨁","\\bigoplus"),defineSymbol(F,R,$,"⨀","\\bigodot"),defineSymbol(F,R,$,"∮","\\oint"),defineSymbol(F,R,$,"∯","\\oiint"),defineSymbol(F,R,$,"∰","\\oiiint"),defineSymbol(F,R,$,"⨆","\\bigsqcup"),defineSymbol(F,R,$,"∫","\\smallint"),defineSymbol(H,R,L,"…","\\textellipsis"),defineSymbol(F,R,L,"…","\\mathellipsis"),defineSymbol(H,R,L,"…","\\ldots",!0),defineSymbol(F,R,L,"…","\\ldots",!0),defineSymbol(F,R,L,"⋯","\\@cdots",!0),defineSymbol(F,R,L,"⋱","\\ddots",!0),defineSymbol(F,R,Y,"⋮","\\varvdots"),defineSymbol(H,R,Y,"⋮","\\varvdots"),defineSymbol(F,R,I,"ˊ","\\acute"),defineSymbol(F,R,I,"ˋ","\\grave"),defineSymbol(F,R,I,"\xa8","\\ddot"),defineSymbol(F,R,I,"~","\\tilde"),defineSymbol(F,R,I,"ˉ","\\bar"),defineSymbol(F,R,I,"˘","\\breve"),defineSymbol(F,R,I,"ˇ","\\check"),defineSymbol(F,R,I,"^","\\hat"),defineSymbol(F,R,I,"⃗","\\vec"),defineSymbol(F,R,I,"˙","\\dot"),defineSymbol(F,R,I,"˚","\\mathring"),defineSymbol(F,R,O,"","\\@imath"),defineSymbol(F,R,O,"","\\@jmath"),defineSymbol(F,R,Y,"ı","ı"),defineSymbol(F,R,Y,"ȷ","ȷ"),defineSymbol(H,R,Y,"ı","\\i",!0),defineSymbol(H,R,Y,"ȷ","\\j",!0),defineSymbol(H,R,Y,"\xdf","\\ss",!0),defineSymbol(H,R,Y,"\xe6","\\ae",!0),defineSymbol(H,R,Y,"œ","\\oe",!0),defineSymbol(H,R,Y,"\xf8","\\o",!0),defineSymbol(H,R,Y,"\xc6","\\AE",!0),defineSymbol(H,R,Y,"Œ","\\OE",!0),defineSymbol(H,R,Y,"\xd8","\\O",!0),defineSymbol(H,R,I,"ˊ","\\'"),defineSymbol(H,R,I,"ˋ","\\`"),defineSymbol(H,R,I,"ˆ","\\^"),defineSymbol(H,R,I,"˜","\\~"),defineSymbol(H,R,I,"ˉ","\\="),defineSymbol(H,R,I,"˘","\\u"),defineSymbol(H,R,I,"˙","\\."),defineSymbol(H,R,I,"\xb8","\\c"),defineSymbol(H,R,I,"˚","\\r"),defineSymbol(H,R,I,"ˇ","\\v"),defineSymbol(H,R,I,"\xa8",'\\"'),defineSymbol(H,R,I,"˝","\\H"),defineSymbol(H,R,I,"◯","\\textcircled");var W={"--":!0,"---":!0,"``":!0,"''":!0};defineSymbol(H,R,Y,"–","--",!0),defineSymbol(H,R,Y,"–","\\textendash"),defineSymbol(H,R,Y,"—","---",!0),defineSymbol(H,R,Y,"—","\\textemdash"),defineSymbol(H,R,Y,"‘","`",!0),defineSymbol(H,R,Y,"‘","\\textquoteleft"),defineSymbol(H,R,Y,"’","'",!0),defineSymbol(H,R,Y,"’","\\textquoteright"),defineSymbol(H,R,Y,"“","``",!0),defineSymbol(H,R,Y,"“","\\textquotedblleft"),defineSymbol(H,R,Y,"”","''",!0),defineSymbol(H,R,Y,"”","\\textquotedblright"),defineSymbol(F,R,Y,"\xb0","\\degree",!0),defineSymbol(H,R,Y,"\xb0","\\degree"),defineSymbol(H,R,Y,"\xb0","\\textdegree",!0),defineSymbol(F,R,Y,"\xa3","\\pounds"),defineSymbol(F,R,Y,"\xa3","\\mathsterling",!0),defineSymbol(H,R,Y,"\xa3","\\pounds"),defineSymbol(H,R,Y,"\xa3","\\textsterling",!0),defineSymbol(F,"ams",Y,"✠","\\maltese"),defineSymbol(H,"ams",Y,"✠","\\maltese");for(var X='0123456789/@."',_=0;_<X.length;_++){var j=X.charAt(_);defineSymbol(F,R,Y,j,j)}for(var Z='0123456789!@*()-=+";:?/.,',K=0;K<Z.length;K++){var J=Z.charAt(K);defineSymbol(H,R,Y,J,J)}for(var Q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz",ee=0;ee<Q.length;ee++){var et=Q.charAt(ee);defineSymbol(F,R,O,et,et),defineSymbol(H,R,Y,et,et)}defineSymbol(F,"ams",Y,"C","ℂ"),defineSymbol(H,"ams",Y,"C","ℂ"),defineSymbol(F,"ams",Y,"H","ℍ"),defineSymbol(H,"ams",Y,"H","ℍ"),defineSymbol(F,"ams",Y,"N","ℕ"),defineSymbol(H,"ams",Y,"N","ℕ"),defineSymbol(F,"ams",Y,"P","ℙ"),defineSymbol(H,"ams",Y,"P","ℙ"),defineSymbol(F,"ams",Y,"Q","ℚ"),defineSymbol(H,"ams",Y,"Q","ℚ"),defineSymbol(F,"ams",Y,"R","ℝ"),defineSymbol(H,"ams",Y,"R","ℝ"),defineSymbol(F,"ams",Y,"Z","ℤ"),defineSymbol(H,"ams",Y,"Z","ℤ"),defineSymbol(F,R,O,"h","ℎ"),defineSymbol(H,R,O,"h","ℎ");for(var er="",en=0;en<Q.length;en++){var ei=Q.charAt(en);defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56320+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56372+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56424+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56580+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56684+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56736+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56788+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56840+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56944+en)),defineSymbol(H,R,Y,ei,er),en<26&&(defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56632+en)),defineSymbol(H,R,Y,ei,er),defineSymbol(F,R,O,ei,er=String.fromCharCode(55349,56476+en)),defineSymbol(H,R,Y,ei,er))}defineSymbol(F,R,O,"k",er=String.fromCharCode(55349,56668)),defineSymbol(H,R,Y,"k",er);for(var ea=0;ea<10;ea++){var eo=ea.toString();defineSymbol(F,R,O,eo,er=String.fromCharCode(55349,57294+ea)),defineSymbol(H,R,Y,eo,er),defineSymbol(F,R,O,eo,er=String.fromCharCode(55349,57314+ea)),defineSymbol(H,R,Y,eo,er),defineSymbol(F,R,O,eo,er=String.fromCharCode(55349,57324+ea)),defineSymbol(H,R,Y,eo,er),defineSymbol(F,R,O,eo,er=String.fromCharCode(55349,57334+ea)),defineSymbol(H,R,Y,eo,er)}for(var el="\xd0\xde\xfe",es=0;es<el.length;es++){var em=el.charAt(es);defineSymbol(F,R,O,em,em),defineSymbol(H,R,Y,em,em)}var eh=[["mathbf","textbf","Main-Bold"],["mathbf","textbf","Main-Bold"],["mathnormal","textit","Math-Italic"],["mathnormal","textit","Math-Italic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["boldsymbol","boldsymbol","Main-BoldItalic"],["mathscr","textscr","Script-Regular"],["","",""],["","",""],["","",""],["mathfrak","textfrak","Fraktur-Regular"],["mathfrak","textfrak","Fraktur-Regular"],["mathbb","textbb","AMS-Regular"],["mathbb","textbb","AMS-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathboldfrak","textboldfrak","Fraktur-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathitsf","textitsf","SansSerif-Italic"],["mathitsf","textitsf","SansSerif-Italic"],["","",""],["","",""],["mathtt","texttt","Typewriter-Regular"],["mathtt","texttt","Typewriter-Regular"]],ed=[["mathbf","textbf","Main-Bold"],["","",""],["mathsf","textsf","SansSerif-Regular"],["mathboldsf","textboldsf","SansSerif-Bold"],["mathtt","texttt","Typewriter-Regular"]],wideCharacterFont=function(e,t){var r=(e.charCodeAt(0)-55296)*1024+(e.charCodeAt(1)-56320)+65536,n="math"===t?0:1;if(119808<=r&&r<120484){var i=Math.floor((r-119808)/26);return[eh[i][2],eh[i][n]]}if(120782<=r&&r<=120831){var a=Math.floor((r-120782)/10);return[ed[a][2],ed[a][n]]}if(120485===r||120486===r)return[eh[0][2],eh[0][n]];if(120486<r&&r<120782)return["",""];throw new ParseError("Unsupported character: "+e)},lookupSymbol=function(e,t,r){return P[r][e]&&P[r][e].replace&&(e=P[r][e].replace),{value:e,metrics:getCharacterMetrics(e,t,r)}},makeSymbol=function(e,t,r,n,i){var a,o=lookupSymbol(e,t,r),l=o.metrics;if(e=o.value,l){var s=l.italic;("text"===r||n&&"mathit"===n.font)&&(s=0),a=new SymbolNode(e,l.height,l.depth,s,l.skew,l.width,i)}else"undefined"!=typeof console&&console.warn("No character metrics "+("for '"+e+"' in style '"+t)+"' and mode '"+r+"'"),a=new SymbolNode(e,0,0,0,0,0,i);if(n){a.maxFontSize=n.sizeMultiplier,n.style.isTight()&&a.classes.push("mtight");var m=n.getColor();m&&(a.style.color=m)}return a},canCombine=(e,t)=>{if(createClass(e.classes)!==createClass(t.classes)||e.skew!==t.skew||e.maxFontSize!==t.maxFontSize)return!1;if(1===e.classes.length){var r=e.classes[0];if("mbin"===r||"mord"===r)return!1}for(var n in e.style)if(e.style.hasOwnProperty(n)&&e.style[n]!==t.style[n])return!1;for(var i in t.style)if(t.style.hasOwnProperty(i)&&e.style[i]!==t.style[i])return!1;return!0},sizeElementFromChildren=function(e){for(var t=0,r=0,n=0,i=0;i<e.children.length;i++){var a=e.children[i];a.height>t&&(t=a.height),a.depth>r&&(r=a.depth),a.maxFontSize>n&&(n=a.maxFontSize)}e.height=t,e.depth=r,e.maxFontSize=n},makeSpan$2=function(e,t,r,n){var i=new Span(e,t,r,n);return sizeElementFromChildren(i),i},makeSvgSpan=(e,t,r,n)=>new Span(e,t,r,n),makeFragment=function(e){var t=new DocumentFragment(e);return sizeElementFromChildren(t),t},getVListChildrenAndDepth=function(e){if("individualShift"===e.positionType){for(var t,r=e.children,n=[r[0]],i=-r[0].shift-r[0].elem.depth,a=i,o=1;o<r.length;o++){var l=-r[o].shift-a-r[o].elem.depth,s=l-(r[o-1].elem.height+r[o-1].elem.depth);a+=l,n.push({type:"kern",size:s}),n.push(r[o])}return{children:n,depth:i}}if("top"===e.positionType){for(var m=e.positionData,h=0;h<e.children.length;h++){var d=e.children[h];m-="kern"===d.type?d.size:d.elem.height+d.elem.depth}t=m}else if("bottom"===e.positionType)t=-e.positionData;else{var c=e.children[0];if("elem"!==c.type)throw Error('First child must have type "elem".');if("shift"===e.positionType)t=-c.elem.depth-e.positionData;else if("firstBaseline"===e.positionType)t=-c.elem.depth;else throw Error("Invalid positionType "+e.positionType+".")}return{children:e.children,depth:t}},retrieveTextFontName=function(e,t,r){var n="";switch(e){case"amsrm":n="AMS";break;case"textrm":n="Main";break;case"textsf":n="SansSerif";break;case"texttt":n="Typewriter";break;default:n=e}return n+"-"+("textbf"===t&&"textit"===r?"BoldItalic":"textbf"===t?"Bold":"textit"===t?"Italic":"Regular")},ec={mathbf:{variant:"bold",fontName:"Main-Bold"},mathrm:{variant:"normal",fontName:"Main-Regular"},textit:{variant:"italic",fontName:"Main-Italic"},mathit:{variant:"italic",fontName:"Main-Italic"},mathnormal:{variant:"italic",fontName:"Math-Italic"},mathsfit:{variant:"sans-serif-italic",fontName:"SansSerif-Italic"},mathbb:{variant:"double-struck",fontName:"AMS-Regular"},mathcal:{variant:"script",fontName:"Caligraphic-Regular"},mathfrak:{variant:"fraktur",fontName:"Fraktur-Regular"},mathscr:{variant:"script",fontName:"Script-Regular"},mathsf:{variant:"sans-serif",fontName:"SansSerif-Regular"},mathtt:{variant:"monospace",fontName:"Typewriter-Regular"}},eu={vec:["vec",.471,.714],oiintSize1:["oiintSize1",.957,.499],oiintSize2:["oiintSize2",1.472,.659],oiiintSize1:["oiiintSize1",1.304,.499],oiiintSize2:["oiiintSize2",1.98,.659]},ep={fontMap:ec,makeSymbol,mathsym:function(e,t,r,n){return(void 0===n&&(n=[]),"boldsymbol"===r.font&&lookupSymbol(e,"Main-Bold",t).metrics)?makeSymbol(e,"Main-Bold",t,r,n.concat(["mathbf"])):"\\"===e||"main"===P[t][e].font?makeSymbol(e,"Main-Regular",t,r,n):makeSymbol(e,"AMS-Regular",t,r,n.concat(["amsrm"]))},makeSpan:makeSpan$2,makeSvgSpan,makeLineSpan:function(e,t,r){var n=makeSpan$2([e],[],t);return n.height=Math.max(r||t.fontMetrics().defaultRuleThickness,t.minRuleThickness),n.style.borderBottomWidth=makeEm(n.height),n.maxFontSize=1,n},makeAnchor:function(e,t,r,n){var i=new Anchor(e,t,r,n);return sizeElementFromChildren(i),i},makeFragment,wrapFragment:function(e,t){return e instanceof DocumentFragment?makeSpan$2([],[e],t):e},makeVList:function(e,t){for(var r,{children:n,depth:i}=getVListChildrenAndDepth(e),a=0,o=0;o<n.length;o++){var l=n[o];if("elem"===l.type){var s=l.elem;a=Math.max(a,s.maxFontSize,s.height)}}a+=2;var m=makeSpan$2(["pstrut"],[]);m.style.height=makeEm(a);for(var h=[],d=i,c=i,u=i,p=0;p<n.length;p++){var f=n[p];if("kern"===f.type)u+=f.size;else{var b=f.elem,g=makeSpan$2(f.wrapperClasses||[],[m,b],void 0,f.wrapperStyle||{});g.style.top=makeEm(-a-u-b.depth),f.marginLeft&&(g.style.marginLeft=f.marginLeft),f.marginRight&&(g.style.marginRight=f.marginRight),h.push(g),u+=b.height+b.depth}d=Math.min(d,u),c=Math.max(c,u)}var y=makeSpan$2(["vlist"],h);if(y.style.height=makeEm(c),d<0){var v=makeSpan$2([],[]),S=makeSpan$2(["vlist"],[v]);S.style.height=makeEm(-d);var x=makeSpan$2(["vlist-s"],[new SymbolNode("​")]);r=[makeSpan$2(["vlist-r"],[y,x]),makeSpan$2(["vlist-r"],[S])]}else r=[makeSpan$2(["vlist-r"],[y])];var w=makeSpan$2(["vlist-t"],r);return 2===r.length&&w.classes.push("vlist-t2"),w.height=c,w.depth=-d,w},makeOrd:function(e,t,r){var n=e.mode,i=e.text,a=["mord"],o="math"===n||"text"===n&&t.font,l=o?t.font:t.fontFamily,s="",m="";if(55349===i.charCodeAt(0)&&([s,m]=wideCharacterFont(i,n)),s.length>0)return makeSymbol(i,s,n,t,a.concat(m));if(l){if("boldsymbol"===l){var h,d,c="textord"!==r&&lookupSymbol(i,"Math-BoldItalic",n).metrics?{fontName:"Math-BoldItalic",fontClass:"boldsymbol"}:{fontName:"Main-Bold",fontClass:"mathbf"};h=c.fontName,d=[c.fontClass]}else o?(h=ec[l].fontName,d=[l]):(h=retrieveTextFontName(l,t.fontWeight,t.fontShape),d=[l,t.fontWeight,t.fontShape]);if(lookupSymbol(i,h,n).metrics)return makeSymbol(i,h,n,t,a.concat(d));if(W.hasOwnProperty(i)&&"Typewriter"===h.slice(0,10)){for(var u=[],p=0;p<i.length;p++)u.push(makeSymbol(i[p],h,n,t,a.concat(d)));return makeFragment(u)}}if("mathord"===r)return makeSymbol(i,"Math-Italic",n,t,a.concat(["mathnormal"]));if("textord"===r){var f=P[n][i]&&P[n][i].font;if("ams"===f)return makeSymbol(i,retrieveTextFontName("amsrm",t.fontWeight,t.fontShape),n,t,a.concat("amsrm",t.fontWeight,t.fontShape));if("main"===f||!f)return makeSymbol(i,retrieveTextFontName("textrm",t.fontWeight,t.fontShape),n,t,a.concat(t.fontWeight,t.fontShape));var b=retrieveTextFontName(f,t.fontWeight,t.fontShape);return makeSymbol(i,b,n,t,a.concat(b,t.fontWeight,t.fontShape))}throw Error("unexpected type: "+r+" in makeOrd")},makeGlue:(e,t)=>{var r=makeSpan$2(["mspace"],[],t),n=calculateSize(e,t);return r.style.marginRight=makeEm(n),r},staticSvg:function(e,t){var[r,n,i]=eu[e],a=new PathNode(r),o=makeSvgSpan(["overlay"],[new SvgNode([a],{width:makeEm(n),height:makeEm(i),style:"width:"+makeEm(n),viewBox:"0 0 "+1e3*n+" "+1e3*i,preserveAspectRatio:"xMinYMin"})],t);return o.height=i,o.style.height=makeEm(i),o.style.width=makeEm(n),o},svgData:eu,tryCombineChars:e=>{for(var t=0;t<e.length-1;t++){var r=e[t],n=e[t+1];r instanceof SymbolNode&&n instanceof SymbolNode&&canCombine(r,n)&&(r.text+=n.text,r.height=Math.max(r.height,n.height),r.depth=Math.max(r.depth,n.depth),r.italic=n.italic,e.splice(t+1,1),t--)}return e}},ef={number:3,unit:"mu"},eb={number:4,unit:"mu"},eg={number:5,unit:"mu"},ey={mord:{mop:ef,mbin:eb,mrel:eg,minner:ef},mop:{mord:ef,mop:ef,mrel:eg,minner:ef},mbin:{mord:eb,mop:eb,mopen:eb,minner:eb},mrel:{mord:eg,mop:eg,mopen:eg,minner:eg},mopen:{},mclose:{mop:ef,mbin:eb,mrel:eg,minner:ef},mpunct:{mord:ef,mop:ef,mrel:eg,mopen:ef,mclose:ef,mpunct:ef,minner:ef},minner:{mord:ef,mop:ef,mbin:eb,mrel:eg,mopen:ef,mpunct:ef,minner:ef}},ev={mord:{mop:ef},mop:{mord:ef,mop:ef},mbin:{},mrel:{},mopen:{},mclose:{mop:ef},mpunct:{},minner:{mop:ef}},eS={},ex={},ew={};function defineFunction(e){for(var{type:t,names:r,props:n,handler:i,htmlBuilder:a,mathmlBuilder:o}=e,l={type:t,numArgs:n.numArgs,argTypes:n.argTypes,allowedInArgument:!!n.allowedInArgument,allowedInText:!!n.allowedInText,allowedInMath:void 0===n.allowedInMath||n.allowedInMath,numOptionalArgs:n.numOptionalArgs||0,infix:!!n.infix,primitive:!!n.primitive,handler:i},s=0;s<r.length;++s)eS[r[s]]=l;t&&(a&&(ex[t]=a),o&&(ew[t]=o))}function defineFunctionBuilders(e){var{type:t,htmlBuilder:r,mathmlBuilder:n}=e;defineFunction({type:t,names:[],props:{numArgs:0},handler(){throw Error("Should never be called.")},htmlBuilder:r,mathmlBuilder:n})}var normalizeArgument=function(e){return"ordgroup"===e.type&&1===e.body.length?e.body[0]:e},ordargument=function(e){return"ordgroup"===e.type?e.body:[e]},ek=ep.makeSpan,eM=["leftmost","mbin","mopen","mrel","mop","mpunct"],ez=["rightmost","mrel","mclose","mpunct"],eT={display:g.DISPLAY,text:g.TEXT,script:g.SCRIPT,scriptscript:g.SCRIPTSCRIPT},eA={mord:"mord",mop:"mop",mbin:"mbin",mrel:"mrel",mopen:"mopen",mclose:"mclose",mpunct:"mpunct",minner:"minner"},buildExpression$1=function(e,t,r,n){void 0===n&&(n=[null,null]);for(var i=[],a=0;a<e.length;a++){var o=buildGroup$1(e[a],t);if(o instanceof DocumentFragment){var l=o.children;i.push(...l)}else i.push(o)}if(ep.tryCombineChars(i),!r)return i;var m=t;if(1===e.length){var h=e[0];"sizing"===h.type?m=t.havingSize(h.size):"styling"===h.type&&(m=t.havingStyle(eT[h.style]))}var d=ek([n[0]||"leftmost"],[],t),c=ek([n[1]||"rightmost"],[],t),u="root"===r;return traverseNonSpaceNodes(i,(e,t)=>{var r=t.classes[0],n=e.classes[0];"mbin"===r&&s.contains(ez,n)?t.classes[0]="mord":"mbin"===n&&s.contains(eM,r)&&(e.classes[0]="mord")},{node:d},c,u),traverseNonSpaceNodes(i,(e,t)=>{var r=getTypeOfDomTree(t),n=getTypeOfDomTree(e),i=r&&n?e.hasClass("mtight")?ev[r][n]:ey[r][n]:null;if(i)return ep.makeGlue(i,m)},{node:d},c,u),i},traverseNonSpaceNodes=function traverseNonSpaceNodes(e,t,r,n,i){n&&e.push(n);for(var a=0;a<e.length;a++){var o,l=e[a],s=checkPartialGroup(l);if(s){traverseNonSpaceNodes(s.children,t,r,null,i);continue}var m=!l.hasClass("mspace");if(m){var h=t(l,r.node);h&&(r.insertAfter?r.insertAfter(h):(e.unshift(h),a++))}m?r.node=l:i&&l.hasClass("newline")&&(r.node=ek(["leftmost"])),r.insertAfter=(o=a,t=>{e.splice(o+1,0,t),a++})}n&&e.pop()},checkPartialGroup=function(e){return e instanceof DocumentFragment||e instanceof Anchor||e instanceof Span&&e.hasClass("enclosing")?e:null},getOutermostNode=function getOutermostNode(e,t){var r=checkPartialGroup(e);if(r){var n=r.children;if(n.length){if("right"===t)return getOutermostNode(n[n.length-1],"right");if("left"===t)return getOutermostNode(n[0],"left")}}return e},getTypeOfDomTree=function(e,t){return e?(t&&(e=getOutermostNode(e,t)),eA[e.classes[0]]||null):null},makeNullDelimiter=function(e,t){var r=["nulldelimiter"].concat(e.baseSizingClasses());return ek(t.concat(r))},buildGroup$1=function(e,t,r){if(!e)return ek();if(ex[e.type]){var n=ex[e.type](e,t);if(r&&t.size!==r.size){n=ek(t.sizingClasses(r),[n],t);var i=t.sizeMultiplier/r.sizeMultiplier;n.height*=i,n.depth*=i}return n}throw new ParseError("Got group of unknown type: '"+e.type+"'")};function buildHTMLUnbreakable(e,t){var r=ek(["base"],e,t),n=ek(["strut"]);return n.style.height=makeEm(r.height+r.depth),r.depth&&(n.style.verticalAlign=makeEm(-r.depth)),r.children.unshift(n),r}function buildHTML(e,t){var r,n,i=null;1===e.length&&"tag"===e[0].type&&(i=e[0].tag,e=e[0].body);var a=buildExpression$1(e,t,"root");2===a.length&&a[1].hasClass("tag")&&(r=a.pop());for(var o=[],l=[],s=0;s<a.length;s++)if(l.push(a[s]),a[s].hasClass("mbin")||a[s].hasClass("mrel")||a[s].hasClass("allowbreak")){for(var m=!1;s<a.length-1&&a[s+1].hasClass("mspace")&&!a[s+1].hasClass("newline");)s++,l.push(a[s]),a[s].hasClass("nobreak")&&(m=!0);m||(o.push(buildHTMLUnbreakable(l,t)),l=[])}else a[s].hasClass("newline")&&(l.pop(),l.length>0&&(o.push(buildHTMLUnbreakable(l,t)),l=[]),o.push(a[s]));l.length>0&&o.push(buildHTMLUnbreakable(l,t)),i?((n=buildHTMLUnbreakable(buildExpression$1(i,t,!0))).classes=["tag"],o.push(n)):r&&o.push(r);var h=ek(["katex-html"],o);if(h.setAttribute("aria-hidden","true"),n){var d=n.children[0];d.style.height=makeEm(h.height+h.depth),h.depth&&(d.style.verticalAlign=makeEm(-h.depth))}return h}function newDocumentFragment(e){return new DocumentFragment(e)}let MathNode=class MathNode{constructor(e,t,r){this.type=void 0,this.attributes=void 0,this.children=void 0,this.classes=void 0,this.type=e,this.attributes={},this.children=t||[],this.classes=r||[]}setAttribute(e,t){this.attributes[e]=t}getAttribute(e){return this.attributes[e]}toNode(){var e=document.createElementNS("http://www.w3.org/1998/Math/MathML",this.type);for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&e.setAttribute(t,this.attributes[t]);this.classes.length>0&&(e.className=createClass(this.classes));for(var r=0;r<this.children.length;r++)if(this.children[r]instanceof TextNode&&this.children[r+1]instanceof TextNode){for(var n=this.children[r].toText()+this.children[++r].toText();this.children[r+1]instanceof TextNode;)n+=this.children[++r].toText();e.appendChild(new TextNode(n).toNode())}else e.appendChild(this.children[r].toNode());return e}toMarkup(){var e="<"+this.type;for(var t in this.attributes)Object.prototype.hasOwnProperty.call(this.attributes,t)&&(e+=" "+t+'="'+s.escape(this.attributes[t])+'"');this.classes.length>0&&(e+=' class ="'+s.escape(createClass(this.classes))+'"'),e+=">";for(var r=0;r<this.children.length;r++)e+=this.children[r].toMarkup();return e+("</"+this.type)+">"}toText(){return this.children.map(e=>e.toText()).join("")}};let TextNode=class TextNode{constructor(e){this.text=void 0,this.text=e}toNode(){return document.createTextNode(this.text)}toMarkup(){return s.escape(this.toText())}toText(){return this.text}};var eE={MathNode,TextNode,SpaceNode:class{constructor(e){this.width=void 0,this.character=void 0,this.width=e,e>=.05555&&e<=.05556?this.character=" ":e>=.1666&&e<=.1667?this.character=" ":e>=.2222&&e<=.2223?this.character=" ":e>=.2777&&e<=.2778?this.character="  ":e>=-.05556&&e<=-.05555?this.character=" ⁣":e>=-.1667&&e<=-.1666?this.character=" ⁣":e>=-.2223&&e<=-.2222?this.character=" ⁣":e>=-.2778&&e<=-.2777?this.character=" ⁣":this.character=null}toNode(){if(this.character)return document.createTextNode(this.character);var e=document.createElementNS("http://www.w3.org/1998/Math/MathML","mspace");return e.setAttribute("width",makeEm(this.width)),e}toMarkup(){return this.character?"<mtext>"+this.character+"</mtext>":'<mspace width="'+makeEm(this.width)+'"/>'}toText(){return this.character?this.character:" "}},newDocumentFragment},makeText=function(e,t,r){return P[t][e]&&P[t][e].replace&&55349!==e.charCodeAt(0)&&!(W.hasOwnProperty(e)&&r&&(r.fontFamily&&"tt"===r.fontFamily.slice(4,6)||r.font&&"tt"===r.font.slice(4,6)))&&(e=P[t][e].replace),new eE.TextNode(e)},makeRow=function(e){return 1===e.length?e[0]:new eE.MathNode("mrow",e)},getVariant=function(e,t){if("texttt"===t.fontFamily)return"monospace";if("textsf"===t.fontFamily)return"textit"===t.fontShape&&"textbf"===t.fontWeight?"sans-serif-bold-italic":"textit"===t.fontShape?"sans-serif-italic":"textbf"===t.fontWeight?"bold-sans-serif":"sans-serif";if("textit"===t.fontShape&&"textbf"===t.fontWeight)return"bold-italic";if("textit"===t.fontShape)return"italic";if("textbf"===t.fontWeight)return"bold";var r=t.font;if(!r||"mathnormal"===r)return null;var n=e.mode;if("mathit"===r)return"italic";if("boldsymbol"===r)return"textord"===e.type?"bold":"bold-italic";if("mathbf"===r)return"bold";if("mathbb"===r)return"double-struck";if("mathsfit"===r)return"sans-serif-italic";if("mathfrak"===r)return"fraktur";if("mathscr"===r||"mathcal"===r)return"script";else if("mathsf"===r)return"sans-serif";else if("mathtt"===r)return"monospace";var i=e.text;return s.contains(["\\imath","\\jmath"],i)?null:(P[n][i]&&P[n][i].replace&&(i=P[n][i].replace),getCharacterMetrics(i,ep.fontMap[r].fontName,n))?ep.fontMap[r].variant:null};function isNumberPunctuation(e){if(!e)return!1;if("mi"===e.type&&1===e.children.length){var t=e.children[0];return t instanceof TextNode&&"."===t.text}if("mo"!==e.type||1!==e.children.length||"true"!==e.getAttribute("separator")||"0em"!==e.getAttribute("lspace")||"0em"!==e.getAttribute("rspace"))return!1;var r=e.children[0];return r instanceof TextNode&&","===r.text}var buildExpression=function(e,t,r){if(1===e.length){var n,i=buildGroup(e[0],t);return r&&i instanceof MathNode&&"mo"===i.type&&(i.setAttribute("lspace","0em"),i.setAttribute("rspace","0em")),[i]}for(var a=[],o=0;o<e.length;o++){var l=buildGroup(e[o],t);if(l instanceof MathNode&&n instanceof MathNode){if("mtext"===l.type&&"mtext"===n.type&&l.getAttribute("mathvariant")===n.getAttribute("mathvariant")||"mn"===l.type&&"mn"===n.type){n.children.push(...l.children);continue}if(isNumberPunctuation(l)&&"mn"===n.type){n.children.push(...l.children);continue}if("mn"===l.type&&isNumberPunctuation(n))l.children=[...n.children,...l.children],a.pop();else if(("msup"===l.type||"msub"===l.type)&&l.children.length>=1&&("mn"===n.type||isNumberPunctuation(n))){var s=l.children[0];s instanceof MathNode&&"mn"===s.type&&(s.children=[...n.children,...s.children],a.pop())}else if("mi"===n.type&&1===n.children.length){var m=n.children[0];if(m instanceof TextNode&&"̸"===m.text&&("mo"===l.type||"mi"===l.type||"mn"===l.type)){var h=l.children[0];h instanceof TextNode&&h.text.length>0&&(h.text=h.text.slice(0,1)+"̸"+h.text.slice(1),a.pop())}}}a.push(l),n=l}return a},buildExpressionRow=function(e,t,r){return makeRow(buildExpression(e,t,r))},buildGroup=function(e,t){if(!e)return new eE.MathNode("mrow");if(ew[e.type])return ew[e.type](e,t);throw new ParseError("Got group of unknown type: '"+e.type+"'")};function buildMathML(e,t,r,n,i){var a,o=buildExpression(e,r);a=1===o.length&&o[0]instanceof MathNode&&s.contains(["mrow","mtable"],o[0].type)?o[0]:new eE.MathNode("mrow",o);var l=new eE.MathNode("annotation",[new eE.TextNode(t)]);l.setAttribute("encoding","application/x-tex");var m=new eE.MathNode("semantics",[a,l]),h=new eE.MathNode("math",[m]);return h.setAttribute("xmlns","http://www.w3.org/1998/Math/MathML"),n&&h.setAttribute("display","block"),ep.makeSpan([i?"katex":"katex-mathml"],[h])}var optionsFromSettings=function(e){return new Options({style:e.displayMode?g.DISPLAY:g.TEXT,maxSize:e.maxSize,minRuleThickness:e.minRuleThickness})},displayWrap=function(e,t){if(t.displayMode){var r=["katex-display"];t.leqno&&r.push("leqno"),t.fleqn&&r.push("fleqn"),e=ep.makeSpan(r,[e])}return e},buildTree=function(e,t,r){var n,i=optionsFromSettings(r);if("mathml"===r.output)return buildMathML(e,t,i,r.displayMode,!0);if("html"===r.output){var a=buildHTML(e,i);n=ep.makeSpan(["katex"],[a])}else{var o=buildMathML(e,t,i,r.displayMode,!1),l=buildHTML(e,i);n=ep.makeSpan(["katex"],[o,l])}return displayWrap(n,r)},buildHTMLTree=function(e,t,r){var n=buildHTML(e,optionsFromSettings(r));return displayWrap(ep.makeSpan(["katex"],[n]),r)},eB={widehat:"^",widecheck:"ˇ",widetilde:"~",utilde:"~",overleftarrow:"←",underleftarrow:"←",xleftarrow:"←",overrightarrow:"→",underrightarrow:"→",xrightarrow:"→",underbrace:"⏟",overbrace:"⏞",overgroup:"⏠",undergroup:"⏡",overleftrightarrow:"↔",underleftrightarrow:"↔",xleftrightarrow:"↔",Overrightarrow:"⇒",xRightarrow:"⇒",overleftharpoon:"↼",xleftharpoonup:"↼",overrightharpoon:"⇀",xrightharpoonup:"⇀",xLeftarrow:"⇐",xLeftrightarrow:"⇔",xhookleftarrow:"↩",xhookrightarrow:"↪",xmapsto:"↦",xrightharpoondown:"⇁",xleftharpoondown:"↽",xrightleftharpoons:"⇌",xleftrightharpoons:"⇋",xtwoheadleftarrow:"↞",xtwoheadrightarrow:"↠",xlongequal:"=",xtofrom:"⇄",xrightleftarrows:"⇄",xrightequilibrium:"⇌",xleftequilibrium:"⇋","\\cdrightarrow":"→","\\cdleftarrow":"←","\\cdlongequal":"="},eN={overrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],overleftarrow:[["leftarrow"],.888,522,"xMinYMin"],underrightarrow:[["rightarrow"],.888,522,"xMaxYMin"],underleftarrow:[["leftarrow"],.888,522,"xMinYMin"],xrightarrow:[["rightarrow"],1.469,522,"xMaxYMin"],"\\cdrightarrow":[["rightarrow"],3,522,"xMaxYMin"],xleftarrow:[["leftarrow"],1.469,522,"xMinYMin"],"\\cdleftarrow":[["leftarrow"],3,522,"xMinYMin"],Overrightarrow:[["doublerightarrow"],.888,560,"xMaxYMin"],xRightarrow:[["doublerightarrow"],1.526,560,"xMaxYMin"],xLeftarrow:[["doubleleftarrow"],1.526,560,"xMinYMin"],overleftharpoon:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoonup:[["leftharpoon"],.888,522,"xMinYMin"],xleftharpoondown:[["leftharpoondown"],.888,522,"xMinYMin"],overrightharpoon:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoonup:[["rightharpoon"],.888,522,"xMaxYMin"],xrightharpoondown:[["rightharpoondown"],.888,522,"xMaxYMin"],xlongequal:[["longequal"],.888,334,"xMinYMin"],"\\cdlongequal":[["longequal"],3,334,"xMinYMin"],xtwoheadleftarrow:[["twoheadleftarrow"],.888,334,"xMinYMin"],xtwoheadrightarrow:[["twoheadrightarrow"],.888,334,"xMaxYMin"],overleftrightarrow:[["leftarrow","rightarrow"],.888,522],overbrace:[["leftbrace","midbrace","rightbrace"],1.6,548],underbrace:[["leftbraceunder","midbraceunder","rightbraceunder"],1.6,548],underleftrightarrow:[["leftarrow","rightarrow"],.888,522],xleftrightarrow:[["leftarrow","rightarrow"],1.75,522],xLeftrightarrow:[["doubleleftarrow","doublerightarrow"],1.75,560],xrightleftharpoons:[["leftharpoondownplus","rightharpoonplus"],1.75,716],xleftrightharpoons:[["leftharpoonplus","rightharpoondownplus"],1.75,716],xhookleftarrow:[["leftarrow","righthook"],1.08,522],xhookrightarrow:[["lefthook","rightarrow"],1.08,522],overlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],underlinesegment:[["leftlinesegment","rightlinesegment"],.888,522],overgroup:[["leftgroup","rightgroup"],.888,342],undergroup:[["leftgroupunder","rightgroupunder"],.888,342],xmapsto:[["leftmapsto","rightarrow"],1.5,522],xtofrom:[["leftToFrom","rightToFrom"],1.75,528],xrightleftarrows:[["baraboveleftarrow","rightarrowabovebar"],1.75,901],xrightequilibrium:[["baraboveshortleftharpoon","rightharpoonaboveshortbar"],1.75,716],xleftequilibrium:[["shortbaraboveleftharpoon","shortrightharpoonabovebar"],1.75,716]},eC={encloseSpan:function(e,t,r,n,i){var a,o=e.height+e.depth+r+n;if(/fbox|color|angl/.test(t)){if(a=ep.makeSpan(["stretchy",t],[],i),"fbox"===t){var l=i.color&&i.getColor();l&&(a.style.borderColor=l)}}else{var s=[];/^[bx]cancel$/.test(t)&&s.push(new LineNode({x1:"0",y1:"0",x2:"100%",y2:"100%","stroke-width":"0.046em"})),/^x?cancel$/.test(t)&&s.push(new LineNode({x1:"0",y1:"100%",x2:"100%",y2:"0","stroke-width":"0.046em"}));var m=new SvgNode(s,{width:"100%",height:makeEm(o)});a=ep.makeSvgSpan([],[m],i)}return a.height=o,a.style.height=makeEm(o),a},mathMLnode:function(e){var t=new eE.MathNode("mo",[new eE.TextNode(eB[e.replace(/^\\/,"")])]);return t.setAttribute("stretchy","true"),t},svgSpan:function(e,t){var{span:r,minWidth:n,height:i}=function(){var r=4e5,n=e.label.slice(1);if(s.contains(["widehat","widecheck","widetilde","utilde"],n)){var i,a,o,l,m="ordgroup"===(l=e.base).type?l.body.length:1;if(m>5)"widehat"===n||"widecheck"===n?(i=420,r=2364,o=.42,a=n+"4"):(i=312,r=2340,o=.34,a="tilde4");else{var h=[1,1,2,2,3,3][m];"widehat"===n||"widecheck"===n?(r=[0,1062,2364,2364,2364][h],i=[0,239,300,360,420][h],o=[0,.24,.3,.3,.36,.42][h],a=n+h):(r=[0,600,1033,2339,2340][h],i=[0,260,286,306,312][h],o=[0,.26,.286,.3,.306,.34][h],a="tilde"+h)}var d=new PathNode(a),c=new SvgNode([d],{width:"100%",height:makeEm(o),viewBox:"0 0 "+r+" "+i,preserveAspectRatio:"none"});return{span:ep.makeSvgSpan([],[c],t),minWidth:0,height:o}}var u,p,f=[],b=eN[n],[g,y,v]=b,S=v/1e3,x=g.length;if(1===x)u=["hide-tail"],p=[b[3]];else if(2===x)u=["halfarrow-left","halfarrow-right"],p=["xMinYMin","xMaxYMin"];else if(3===x)u=["brace-left","brace-center","brace-right"],p=["xMinYMin","xMidYMin","xMaxYMin"];else throw Error("Correct katexImagesData or update code here to support\n                    "+x+" children.");for(var w=0;w<x;w++){var k=new PathNode(g[w]),M=new SvgNode([k],{width:"400em",height:makeEm(S),viewBox:"0 0 "+r+" "+v,preserveAspectRatio:p[w]+" slice"}),z=ep.makeSvgSpan([u[w]],[M],t);if(1===x)return{span:z,minWidth:y,height:S};z.style.height=makeEm(S),f.push(z)}return{span:ep.makeSpan(["stretchy"],f,t),minWidth:y,height:S}}();return r.height=i,r.style.height=makeEm(i),n>0&&(r.style.minWidth=makeEm(n)),r}};function assertNodeType(e,t){if(!e||e.type!==t)throw Error("Expected node of type "+t+", but got "+(e?"node of type "+e.type:String(e)));return e}function assertSymbolNodeType(e){var t=checkSymbolNodeType(e);if(!t)throw Error("Expected node of symbol group type, but got "+(e?"node of type "+e.type:String(e)));return t}function checkSymbolNodeType(e){return e&&("atom"===e.type||q.hasOwnProperty(e.type))?e:null}var htmlBuilder$a=(e,t)=>{e&&"supsub"===e.type?(l=(m=assertNodeType(e.base,"accent")).base,e.base=l,h=function(e){if(e instanceof Span)return e;throw Error("Expected span<HtmlDomNode> but got "+String(e)+".")}(buildGroup$1(e,t)),e.base=m):l=(m=assertNodeType(e,"accent")).base;var r=buildGroup$1(l,t.havingCrampedStyle()),n=m.isShifty&&s.isCharacterBox(l),i=0;n&&(i=assertSymbolDomNode(buildGroup$1(s.getBaseElem(l),t.havingCrampedStyle())).skew);var a="\\c"===m.label,o=a?r.height+r.depth:Math.min(r.height,t.fontMetrics().xHeight);if(m.isStretchy)d=eC.svgSpan(m,t),d=ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"elem",elem:d,wrapperClasses:["svg-align"],wrapperStyle:i>0?{width:"calc(100% - "+makeEm(2*i)+")",marginLeft:makeEm(2*i)}:void 0}]},t);else{"\\vec"===m.label?(c=ep.staticSvg("vec",t),u=ep.svgData.vec[1]):((c=assertSymbolDomNode(c=ep.makeOrd({mode:m.mode,text:m.label},t,"textord"))).italic=0,u=c.width,a&&(o+=c.depth)),d=ep.makeSpan(["accent-body"],[c]);var l,m,h,d,c,u,p="\\textcircled"===m.label;p&&(d.classes.push("accent-full"),o=r.height);var f=i;p||(f-=u/2),d.style.left=makeEm(f),"\\textcircled"===m.label&&(d.style.top=".2em"),d=ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:-o},{type:"elem",elem:d}]},t)}var b=ep.makeSpan(["mord","accent"],[d],t);return h?(h.children[0]=b,h.height=Math.max(b.height,h.height),h.classes[0]="mord",h):b},mathmlBuilder$9=(e,t)=>{var r=e.isStretchy?eC.mathMLnode(e.label):new eE.MathNode("mo",[makeText(e.label,e.mode)]),n=new eE.MathNode("mover",[buildGroup(e.base,t),r]);return n.setAttribute("accent","true"),n},eq=new RegExp(["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring"].map(e=>"\\"+e).join("|"));defineFunction({type:"accent",names:["\\acute","\\grave","\\ddot","\\tilde","\\bar","\\breve","\\check","\\hat","\\vec","\\dot","\\mathring","\\widecheck","\\widehat","\\widetilde","\\overrightarrow","\\overleftarrow","\\Overrightarrow","\\overleftrightarrow","\\overgroup","\\overlinesegment","\\overleftharpoon","\\overrightharpoon"],props:{numArgs:1},handler:(e,t)=>{var r=normalizeArgument(t[0]),n=!eq.test(e.funcName),i=!n||"\\widehat"===e.funcName||"\\widetilde"===e.funcName||"\\widecheck"===e.funcName;return{type:"accent",mode:e.parser.mode,label:e.funcName,isStretchy:n,isShifty:i,base:r}},htmlBuilder:htmlBuilder$a,mathmlBuilder:mathmlBuilder$9}),defineFunction({type:"accent",names:["\\'","\\`","\\^","\\~","\\=","\\u","\\.",'\\"',"\\c","\\r","\\H","\\v","\\textcircled"],props:{numArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["primitive"]},handler:(e,t)=>{var r=t[0],n=e.parser.mode;return"math"===n&&(e.parser.settings.reportNonstrict("mathVsTextAccents","LaTeX's accent "+e.funcName+" works only in text mode"),n="text"),{type:"accent",mode:n,label:e.funcName,isStretchy:!1,isShifty:!0,base:r}},htmlBuilder:htmlBuilder$a,mathmlBuilder:mathmlBuilder$9}),defineFunction({type:"accentUnder",names:["\\underleftarrow","\\underrightarrow","\\underleftrightarrow","\\undergroup","\\underlinesegment","\\utilde"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:n}=e,i=t[0];return{type:"accentUnder",mode:r.mode,label:n,base:i}},htmlBuilder:(e,t)=>{var r=buildGroup$1(e.base,t),n=eC.svgSpan(e,t),i="\\utilde"===e.label?.12:0,a=ep.makeVList({positionType:"top",positionData:r.height,children:[{type:"elem",elem:n,wrapperClasses:["svg-align"]},{type:"kern",size:i},{type:"elem",elem:r}]},t);return ep.makeSpan(["mord","accentunder"],[a],t)},mathmlBuilder:(e,t)=>{var r=eC.mathMLnode(e.label),n=new eE.MathNode("munder",[buildGroup(e.base,t),r]);return n.setAttribute("accentunder","true"),n}});var paddedNode=e=>{var t=new eE.MathNode("mpadded",e?[e]:[]);return t.setAttribute("width","+0.6em"),t.setAttribute("lspace","0.3em"),t};defineFunction({type:"xArrow",names:["\\xleftarrow","\\xrightarrow","\\xLeftarrow","\\xRightarrow","\\xleftrightarrow","\\xLeftrightarrow","\\xhookleftarrow","\\xhookrightarrow","\\xmapsto","\\xrightharpoondown","\\xrightharpoonup","\\xleftharpoondown","\\xleftharpoonup","\\xrightleftharpoons","\\xleftrightharpoons","\\xlongequal","\\xtwoheadrightarrow","\\xtwoheadleftarrow","\\xtofrom","\\xrightleftarrows","\\xrightequilibrium","\\xleftequilibrium","\\\\cdrightarrow","\\\\cdleftarrow","\\\\cdlongequal"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:n,funcName:i}=e;return{type:"xArrow",mode:n.mode,label:i,body:t[0],below:r[0]}},htmlBuilder(e,t){var r,n,i=t.style,a=t.havingStyle(i.sup()),o=ep.wrapFragment(buildGroup$1(e.body,a,t),t),l="\\x"===e.label.slice(0,2)?"x":"cd";o.classes.push(l+"-arrow-pad"),e.below&&(a=t.havingStyle(i.sub()),(r=ep.wrapFragment(buildGroup$1(e.below,a,t),t)).classes.push(l+"-arrow-pad"));var s=eC.svgSpan(e,t),m=-t.fontMetrics().axisHeight+.5*s.height,h=-t.fontMetrics().axisHeight-.5*s.height-.111;if((o.depth>.25||"\\xleftequilibrium"===e.label)&&(h-=o.depth),r){var d=-t.fontMetrics().axisHeight+r.height+.5*s.height+.111;n=ep.makeVList({positionType:"individualShift",children:[{type:"elem",elem:o,shift:h},{type:"elem",elem:s,shift:m},{type:"elem",elem:r,shift:d}]},t)}else n=ep.makeVList({positionType:"individualShift",children:[{type:"elem",elem:o,shift:h},{type:"elem",elem:s,shift:m}]},t);return n.children[0].children[0].children[1].classes.push("svg-align"),ep.makeSpan(["mrel","x-arrow"],[n],t)},mathmlBuilder(e,t){var r,n=eC.mathMLnode(e.label);if(n.setAttribute("minsize","x"===e.label.charAt(0)?"1.75em":"3.0em"),e.body){var i=paddedNode(buildGroup(e.body,t));if(e.below){var a=paddedNode(buildGroup(e.below,t));r=new eE.MathNode("munderover",[n,a,i])}else r=new eE.MathNode("mover",[n,i])}else if(e.below){var o=paddedNode(buildGroup(e.below,t));r=new eE.MathNode("munder",[n,o])}else r=paddedNode(),r=new eE.MathNode("mover",[n,r]);return r}});var eP=ep.makeSpan;function htmlBuilder$9(e,t){var r=buildExpression$1(e.body,t,!0);return eP([e.mclass],r,t)}function mathmlBuilder$8(e,t){var r,n=buildExpression(e.body,t);return"minner"===e.mclass?r=new eE.MathNode("mpadded",n):"mord"===e.mclass?e.isCharacterBox?(r=n[0]).type="mi":r=new eE.MathNode("mi",n):(e.isCharacterBox?(r=n[0]).type="mo":r=new eE.MathNode("mo",n),"mbin"===e.mclass?(r.attributes.lspace="0.22em",r.attributes.rspace="0.22em"):"mpunct"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0.17em"):"mopen"===e.mclass||"mclose"===e.mclass?(r.attributes.lspace="0em",r.attributes.rspace="0em"):"minner"===e.mclass&&(r.attributes.lspace="0.0556em",r.attributes.width="+0.1111em")),r}defineFunction({type:"mclass",names:["\\mathord","\\mathbin","\\mathrel","\\mathopen","\\mathclose","\\mathpunct","\\mathinner"],props:{numArgs:1,primitive:!0},handler(e,t){var{parser:r,funcName:n}=e,i=t[0];return{type:"mclass",mode:r.mode,mclass:"m"+n.slice(5),body:ordargument(i),isCharacterBox:s.isCharacterBox(i)}},htmlBuilder:htmlBuilder$9,mathmlBuilder:mathmlBuilder$8});var binrelClass=e=>{var t="ordgroup"===e.type&&e.body.length?e.body[0]:e;return"atom"===t.type&&("bin"===t.family||"rel"===t.family)?"m"+t.family:"mord"};defineFunction({type:"mclass",names:["\\@binrel"],props:{numArgs:2},handler(e,t){var{parser:r}=e;return{type:"mclass",mode:r.mode,mclass:binrelClass(t[0]),body:ordargument(t[1]),isCharacterBox:s.isCharacterBox(t[1])}}}),defineFunction({type:"mclass",names:["\\stackrel","\\overset","\\underset"],props:{numArgs:2},handler(e,t){var r,{parser:n,funcName:i}=e,a=t[1],o=t[0];r="\\stackrel"!==i?binrelClass(a):"mrel";var l={type:"op",mode:a.mode,limits:!0,alwaysHandleSupSub:!0,parentIsSupSub:!1,symbol:!1,suppressBaseShift:"\\stackrel"!==i,body:ordargument(a)},m={type:"supsub",mode:o.mode,base:l,sup:"\\underset"===i?null:o,sub:"\\underset"===i?o:null};return{type:"mclass",mode:n.mode,mclass:r,body:[m],isCharacterBox:s.isCharacterBox(m)}},htmlBuilder:htmlBuilder$9,mathmlBuilder:mathmlBuilder$8}),defineFunction({type:"pmb",names:["\\pmb"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"pmb",mode:r.mode,mclass:binrelClass(t[0]),body:ordargument(t[0])}},htmlBuilder(e,t){var r=buildExpression$1(e.body,t,!0),n=ep.makeSpan([e.mclass],r,t);return n.style.textShadow="0.02em 0.01em 0.04px",n},mathmlBuilder(e,t){var r=buildExpression(e.body,t),n=new eE.MathNode("mstyle",r);return n.setAttribute("style","text-shadow: 0.02em 0.01em 0.04px"),n}});var eF={">":"\\\\cdrightarrow","<":"\\\\cdleftarrow","=":"\\\\cdlongequal",A:"\\uparrow",V:"\\downarrow","|":"\\Vert",".":"no arrow"},newCell=()=>({type:"styling",body:[],mode:"math",style:"display"}),isStartOfArrow=e=>"textord"===e.type&&"@"===e.text,isLabelEnd=(e,t)=>("mathord"===e.type||"atom"===e.type)&&e.text===t;defineFunction({type:"cdlabel",names:["\\\\cdleft","\\\\cdright"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:n}=e;return{type:"cdlabel",mode:r.mode,side:n.slice(4),label:t[0]}},htmlBuilder(e,t){var r=t.havingStyle(t.style.sup()),n=ep.wrapFragment(buildGroup$1(e.label,r,t),t);return n.classes.push("cd-label-"+e.side),n.style.bottom=makeEm(.8-n.depth),n.height=0,n.depth=0,n},mathmlBuilder(e,t){var r=new eE.MathNode("mrow",[buildGroup(e.label,t)]);return(r=new eE.MathNode("mpadded",[r])).setAttribute("width","0"),"left"===e.side&&r.setAttribute("lspace","-1width"),r.setAttribute("voffset","0.7em"),(r=new eE.MathNode("mstyle",[r])).setAttribute("displaystyle","false"),r.setAttribute("scriptlevel","1"),r}}),defineFunction({type:"cdlabelparent",names:["\\\\cdparent"],props:{numArgs:1},handler(e,t){var{parser:r}=e;return{type:"cdlabelparent",mode:r.mode,fragment:t[0]}},htmlBuilder(e,t){var r=ep.wrapFragment(buildGroup$1(e.fragment,t),t);return r.classes.push("cd-vert-arrow"),r},mathmlBuilder:(e,t)=>new eE.MathNode("mrow",[buildGroup(e.fragment,t)])}),defineFunction({type:"textord",names:["\\@char"],props:{numArgs:1,allowedInText:!0},handler(e,t){for(var r,{parser:n}=e,i=assertNodeType(t[0],"ordgroup").body,a="",o=0;o<i.length;o++)a+=assertNodeType(i[o],"textord").text;var l=parseInt(a);if(isNaN(l))throw new ParseError("\\@char has non-numeric argument "+a);if(l<0||l>=1114111)throw new ParseError("\\@char with invalid code point "+a);return l<=65535?r=String.fromCharCode(l):(l-=65536,r=String.fromCharCode((l>>10)+55296,(1023&l)+56320)),{type:"textord",mode:n.mode,text:r}}});var htmlBuilder$8=(e,t)=>{var r=buildExpression$1(e.body,t.withColor(e.color),!1);return ep.makeFragment(r)},mathmlBuilder$7=(e,t)=>{var r=buildExpression(e.body,t.withColor(e.color)),n=new eE.MathNode("mstyle",r);return n.setAttribute("mathcolor",e.color),n};defineFunction({type:"color",names:["\\textcolor"],props:{numArgs:2,allowedInText:!0,argTypes:["color","original"]},handler(e,t){var{parser:r}=e,n=assertNodeType(t[0],"color-token").color,i=t[1];return{type:"color",mode:r.mode,color:n,body:ordargument(i)}},htmlBuilder:htmlBuilder$8,mathmlBuilder:mathmlBuilder$7}),defineFunction({type:"color",names:["\\color"],props:{numArgs:1,allowedInText:!0,argTypes:["color"]},handler(e,t){var{parser:r,breakOnTokenText:n}=e,i=assertNodeType(t[0],"color-token").color;r.gullet.macros.set("\\current@color",i);var a=r.parseExpression(!0,n);return{type:"color",mode:r.mode,color:i,body:a}},htmlBuilder:htmlBuilder$8,mathmlBuilder:mathmlBuilder$7}),defineFunction({type:"cr",names:["\\\\"],props:{numArgs:0,numOptionalArgs:0,allowedInText:!0},handler(e,t,r){var{parser:n}=e,i="["===n.gullet.future().text?n.parseSizeGroup(!0):null,a=!n.settings.displayMode||!n.settings.useStrictBehavior("newLineInDisplayMode","In LaTeX, \\\\ or \\newline does nothing in display mode");return{type:"cr",mode:n.mode,newLine:a,size:i&&assertNodeType(i,"size").value}},htmlBuilder(e,t){var r=ep.makeSpan(["mspace"],[],t);return e.newLine&&(r.classes.push("newline"),e.size&&(r.style.marginTop=makeEm(calculateSize(e.size,t)))),r},mathmlBuilder(e,t){var r=new eE.MathNode("mspace");return e.newLine&&(r.setAttribute("linebreak","newline"),e.size&&r.setAttribute("height",makeEm(calculateSize(e.size,t)))),r}});var eH={"\\global":"\\global","\\long":"\\\\globallong","\\\\globallong":"\\\\globallong","\\def":"\\gdef","\\gdef":"\\gdef","\\edef":"\\xdef","\\xdef":"\\xdef","\\let":"\\\\globallet","\\futurelet":"\\\\globalfuture"},checkControlSequence=e=>{var t=e.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(t))throw new ParseError("Expected a control sequence",e);return t},getRHS=e=>{var t=e.gullet.popToken();return"="===t.text&&" "===(t=e.gullet.popToken()).text&&(t=e.gullet.popToken()),t},letCommand=(e,t,r,n)=>{var i=e.gullet.macros.get(r.text);null==i&&(r.noexpand=!0,i={tokens:[r],numArgs:0,unexpandable:!e.gullet.isExpandable(r.text)}),e.gullet.macros.set(t,i,n)};defineFunction({type:"internal",names:["\\global","\\long","\\\\globallong"],props:{numArgs:0,allowedInText:!0},handler(e){var{parser:t,funcName:r}=e;t.consumeSpaces();var n=t.fetch();if(eH[n.text])return("\\global"===r||"\\\\globallong"===r)&&(n.text=eH[n.text]),assertNodeType(t.parseFunction(),"internal");throw new ParseError("Invalid token after macro prefix",n)}}),defineFunction({type:"internal",names:["\\def","\\gdef","\\edef","\\xdef"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var t,{parser:r,funcName:n}=e,i=r.gullet.popToken(),a=i.text;if(/^(?:[\\{}$&#^_]|EOF)$/.test(a))throw new ParseError("Expected a control sequence",i);for(var o=0,l=[[]];"{"!==r.gullet.future().text;)if("#"===(i=r.gullet.popToken()).text){if("{"===r.gullet.future().text){t=r.gullet.future(),l[o].push("{");break}if(i=r.gullet.popToken(),!/^[1-9]$/.test(i.text))throw new ParseError('Invalid argument number "'+i.text+'"');if(parseInt(i.text)!==o+1)throw new ParseError('Argument number "'+i.text+'" out of order');o++,l.push([])}else if("EOF"===i.text)throw new ParseError("Expected a macro definition");else l[o].push(i.text);var{tokens:s}=r.gullet.consumeArg();return t&&s.unshift(t),("\\edef"===n||"\\xdef"===n)&&(s=r.gullet.expandTokens(s)).reverse(),r.gullet.macros.set(a,{tokens:s,numArgs:o,delimiters:l},n===eH[n]),{type:"internal",mode:r.mode}}}),defineFunction({type:"internal",names:["\\let","\\\\globallet"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,n=checkControlSequence(t.gullet.popToken());t.gullet.consumeSpaces();var i=getRHS(t);return letCommand(t,n,i,"\\\\globallet"===r),{type:"internal",mode:t.mode}}}),defineFunction({type:"internal",names:["\\futurelet","\\\\globalfuture"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e){var{parser:t,funcName:r}=e,n=checkControlSequence(t.gullet.popToken()),i=t.gullet.popToken(),a=t.gullet.popToken();return letCommand(t,n,a,"\\\\globalfuture"===r),t.gullet.pushToken(a),t.gullet.pushToken(i),{type:"internal",mode:t.mode}}});var getMetrics=function(e,t,r){var n=getCharacterMetrics(P.math[e]&&P.math[e].replace||e,t,r);if(!n)throw Error("Unsupported symbol "+e+" and font size "+t+".");return n},styleWrap=function(e,t,r,n){var i=r.havingBaseStyle(t),a=ep.makeSpan(n.concat(i.sizingClasses(r)),[e],r),o=i.sizeMultiplier/r.sizeMultiplier;return a.height*=o,a.depth*=o,a.maxFontSize=i.sizeMultiplier,a},centerSpan=function(e,t,r){var n=t.havingBaseStyle(r),i=(1-t.sizeMultiplier/n.sizeMultiplier)*t.fontMetrics().axisHeight;e.classes.push("delimcenter"),e.style.top=makeEm(i),e.height-=i,e.depth+=i},makeSmallDelim=function(e,t,r,n,i,a){var o=styleWrap(ep.makeSymbol(e,"Main-Regular",i,n),t,n,a);return r&&centerSpan(o,n,t),o},makeLargeDelim=function(e,t,r,n,i,a){var o=ep.makeSymbol(e,"Size"+t+"-Regular",i,n),l=styleWrap(ep.makeSpan(["delimsizing","size"+t],[o],n),g.TEXT,n,a);return r&&centerSpan(l,n,g.TEXT),l},makeGlyphSpan=function(e,t,r){var n;return n="Size1-Regular"===t?"delim-size1":"delim-size4",{type:"elem",elem:ep.makeSpan(["delimsizinginner",n],[ep.makeSpan([],[ep.makeSymbol(e,t,r)])])}},makeInner=function(e,t,r){var n=x["Size4-Regular"][e.charCodeAt(0)]?x["Size4-Regular"][e.charCodeAt(0)][4]:x["Size1-Regular"][e.charCodeAt(0)][4],i=new PathNode("inner",innerPath(e,Math.round(1e3*t))),a=new SvgNode([i],{width:makeEm(n),height:makeEm(t),style:"width:"+makeEm(n),viewBox:"0 0 "+1e3*n+" "+Math.round(1e3*t),preserveAspectRatio:"xMinYMin"}),o=ep.makeSvgSpan([],[a],r);return o.height=t,o.style.height=makeEm(t),o.style.width=makeEm(n),{type:"elem",elem:o}},eR={type:"kern",size:-.008},eI=["|","\\lvert","\\rvert","\\vert"],eD=["\\|","\\lVert","\\rVert","\\Vert"],makeStackedDelim=function(e,t,r,n,i,a){var o,l,m,h,d="",c=0;o=m=h=e,l=null;var u="Size1-Regular";"\\uparrow"===e?m=h="⏐":"\\Uparrow"===e?m=h="‖":"\\downarrow"===e?o=m="⏐":"\\Downarrow"===e?o=m="‖":"\\updownarrow"===e?(o="\\uparrow",m="⏐",h="\\downarrow"):"\\Updownarrow"===e?(o="\\Uparrow",m="‖",h="\\Downarrow"):s.contains(eI,e)?(m="∣",d="vert",c=333):s.contains(eD,e)?(m="∥",d="doublevert",c=556):"["===e||"\\lbrack"===e?(o="⎡",m="⎢",h="⎣",u="Size4-Regular",d="lbrack",c=667):"]"===e||"\\rbrack"===e?(o="⎤",m="⎥",h="⎦",u="Size4-Regular",d="rbrack",c=667):"\\lfloor"===e||"⌊"===e?(m=o="⎢",h="⎣",u="Size4-Regular",d="lfloor",c=667):"\\lceil"===e||"⌈"===e?(o="⎡",m=h="⎢",u="Size4-Regular",d="lceil",c=667):"\\rfloor"===e||"⌋"===e?(m=o="⎥",h="⎦",u="Size4-Regular",d="rfloor",c=667):"\\rceil"===e||"⌉"===e?(o="⎤",m=h="⎥",u="Size4-Regular",d="rceil",c=667):"("===e||"\\lparen"===e?(o="⎛",m="⎜",h="⎝",u="Size4-Regular",d="lparen",c=875):")"===e||"\\rparen"===e?(o="⎞",m="⎟",h="⎠",u="Size4-Regular",d="rparen",c=875):"\\{"===e||"\\lbrace"===e?(o="⎧",l="⎨",h="⎩",m="⎪",u="Size4-Regular"):"\\}"===e||"\\rbrace"===e?(o="⎫",l="⎬",h="⎭",m="⎪",u="Size4-Regular"):"\\lgroup"===e||"⟮"===e?(o="⎧",h="⎩",m="⎪",u="Size4-Regular"):"\\rgroup"===e||"⟯"===e?(o="⎫",h="⎭",m="⎪",u="Size4-Regular"):"\\lmoustache"===e||"⎰"===e?(o="⎧",h="⎭",m="⎪",u="Size4-Regular"):("\\rmoustache"===e||"⎱"===e)&&(o="⎫",h="⎩",m="⎪",u="Size4-Regular");var p=getMetrics(o,u,i),f=p.height+p.depth,b=getMetrics(m,u,i),y=b.height+b.depth,v=getMetrics(h,u,i),S=v.height+v.depth,x=0,w=1;if(null!==l){var k=getMetrics(l,u,i);x=k.height+k.depth,w=2}var M=f+S+x,z=Math.max(0,Math.ceil((t-M)/(w*y))),T=M+z*w*y,A=n.fontMetrics().axisHeight;r&&(A*=n.sizeMultiplier);var E=T/2-A,B=[];if(d.length>0){var N=Math.round(1e3*T),C=tallDelim(d,Math.round(1e3*(T-f-S))),q=new PathNode(d,C),P=(c/1e3).toFixed(3)+"em",F=(N/1e3).toFixed(3)+"em",H=new SvgNode([q],{width:P,height:F,viewBox:"0 0 "+c+" "+N}),R=ep.makeSvgSpan([],[H],n);R.height=N/1e3,R.style.width=P,R.style.height=F,B.push({type:"elem",elem:R})}else{if(B.push(makeGlyphSpan(h,u,i)),B.push(eR),null===l)B.push(makeInner(m,T-f-S+.016,n));else{var I=(T-f-S-x)/2+.016;B.push(makeInner(m,I,n)),B.push(eR),B.push(makeGlyphSpan(l,u,i)),B.push(eR),B.push(makeInner(m,I,n))}B.push(eR),B.push(makeGlyphSpan(o,u,i))}var D=n.havingBaseStyle(g.TEXT),L=ep.makeVList({positionType:"bottom",positionData:E,children:B},D);return styleWrap(ep.makeSpan(["delimsizing","mult"],[L],D),g.TEXT,n,a)},sqrtSvg=function(e,t,r,n,i){var a=sqrtPath(e,n,r),o=new PathNode(e,a),l=new SvgNode([o],{width:"400em",height:makeEm(t),viewBox:"0 0 400000 "+r,preserveAspectRatio:"xMinYMin slice"});return ep.makeSvgSpan(["hide-tail"],[l],i)},eL=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","\\surd"],eO=["\\uparrow","\\downarrow","\\updownarrow","\\Uparrow","\\Downarrow","\\Updownarrow","|","\\|","\\vert","\\Vert","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱"],e$=["<",">","\\langle","\\rangle","/","\\backslash","\\lt","\\gt"],eG=[0,1.2,1.8,2.4,3],eV=[{type:"small",style:g.SCRIPTSCRIPT},{type:"small",style:g.SCRIPT},{type:"small",style:g.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4}],eU=[{type:"small",style:g.SCRIPTSCRIPT},{type:"small",style:g.SCRIPT},{type:"small",style:g.TEXT},{type:"stack"}],eY=[{type:"small",style:g.SCRIPTSCRIPT},{type:"small",style:g.SCRIPT},{type:"small",style:g.TEXT},{type:"large",size:1},{type:"large",size:2},{type:"large",size:3},{type:"large",size:4},{type:"stack"}],delimTypeToFont=function(e){if("small"===e.type)return"Main-Regular";if("large"===e.type)return"Size"+e.size+"-Regular";if("stack"===e.type)return"Size4-Regular";throw Error("Add support for delim type '"+e.type+"' here.")},traverseSequence=function(e,t,r,n){for(var i=Math.min(2,3-n.style.size),a=i;a<r.length&&"stack"!==r[a].type;a++){var o=getMetrics(e,delimTypeToFont(r[a]),"math"),l=o.height+o.depth;if("small"===r[a].type&&(l*=n.havingBaseStyle(r[a].style).sizeMultiplier),l>t)return r[a]}return r[r.length-1]},makeCustomSizedDelim=function(e,t,r,n,i,a){"<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle"),o=s.contains(e$,e)?eV:s.contains(eL,e)?eY:eU;var o,l=traverseSequence(e,t,o,n);return"small"===l.type?makeSmallDelim(e,l.style,r,n,i,a):"large"===l.type?makeLargeDelim(e,l.size,r,n,i,a):makeStackedDelim(e,t,r,n,i,a)},eW={sqrtImage:function(e,t){var r,n,i=t.havingBaseSizing(),a=traverseSequence("\\surd",e*i.sizeMultiplier,eY,i),o=i.sizeMultiplier,l=Math.max(0,t.minRuleThickness-t.fontMetrics().sqrtRuleThickness),s=0,m=0,h=0;return"small"===a.type?(h=1e3+1e3*l+80,e<1?o=1:e<1.4&&(o=.7),s=(1+l+.08)/o,m=(1+l)/o,(r=sqrtSvg("sqrtMain",s,h,l,t)).style.minWidth="0.853em",n=.833/o):"large"===a.type?(h=1080*eG[a.size],m=(eG[a.size]+l)/o,s=(eG[a.size]+l+.08)/o,(r=sqrtSvg("sqrtSize"+a.size,s,h,l,t)).style.minWidth="1.02em",n=1/o):(s=e+l+.08,m=e+l,(r=sqrtSvg("sqrtTall",s,h=Math.floor(1e3*e+l)+80,l,t)).style.minWidth="0.742em",n=1.056),r.height=m,r.style.height=makeEm(s),{span:r,advanceWidth:n,ruleWidth:(t.fontMetrics().sqrtRuleThickness+l)*o}},sizedDelim:function(e,t,r,n,i){if("<"===e||"\\lt"===e||"⟨"===e?e="\\langle":(">"===e||"\\gt"===e||"⟩"===e)&&(e="\\rangle"),s.contains(eL,e)||s.contains(e$,e))return makeLargeDelim(e,t,!1,r,n,i);if(s.contains(eO,e))return makeStackedDelim(e,eG[t],!1,r,n,i);throw new ParseError("Illegal delimiter: '"+e+"'")},sizeToMaxHeight:eG,customSizedDelim:makeCustomSizedDelim,leftRightDelim:function(e,t,r,n,i,a){var o=n.fontMetrics().axisHeight*n.sizeMultiplier,l=5/n.fontMetrics().ptPerEm,s=Math.max(t-o,r+o);return makeCustomSizedDelim(e,Math.max(s/500*901,2*s-l),!0,n,i,a)}},eX={"\\bigl":{mclass:"mopen",size:1},"\\Bigl":{mclass:"mopen",size:2},"\\biggl":{mclass:"mopen",size:3},"\\Biggl":{mclass:"mopen",size:4},"\\bigr":{mclass:"mclose",size:1},"\\Bigr":{mclass:"mclose",size:2},"\\biggr":{mclass:"mclose",size:3},"\\Biggr":{mclass:"mclose",size:4},"\\bigm":{mclass:"mrel",size:1},"\\Bigm":{mclass:"mrel",size:2},"\\biggm":{mclass:"mrel",size:3},"\\Biggm":{mclass:"mrel",size:4},"\\big":{mclass:"mord",size:1},"\\Big":{mclass:"mord",size:2},"\\bigg":{mclass:"mord",size:3},"\\Bigg":{mclass:"mord",size:4}},e_=["(","\\lparen",")","\\rparen","[","\\lbrack","]","\\rbrack","\\{","\\lbrace","\\}","\\rbrace","\\lfloor","\\rfloor","⌊","⌋","\\lceil","\\rceil","⌈","⌉","<",">","\\langle","⟨","\\rangle","⟩","\\lt","\\gt","\\lvert","\\rvert","\\lVert","\\rVert","\\lgroup","\\rgroup","⟮","⟯","\\lmoustache","\\rmoustache","⎰","⎱","/","\\backslash","|","\\vert","\\|","\\Vert","\\uparrow","\\Uparrow","\\downarrow","\\Downarrow","\\updownarrow","\\Updownarrow","."];function checkDelimiter(e,t){var r=checkSymbolNodeType(e);if(r&&s.contains(e_,r.text))return r;if(r)throw new ParseError("Invalid delimiter '"+r.text+"' after '"+t.funcName+"'",e);throw new ParseError("Invalid delimiter type '"+e.type+"'",e)}function assertParsed(e){if(!e.body)throw Error("Bug: The leftright ParseNode wasn't fully parsed.")}defineFunction({type:"delimsizing",names:["\\bigl","\\Bigl","\\biggl","\\Biggl","\\bigr","\\Bigr","\\biggr","\\Biggr","\\bigm","\\Bigm","\\biggm","\\Biggm","\\big","\\Big","\\bigg","\\Bigg"],props:{numArgs:1,argTypes:["primitive"]},handler:(e,t)=>{var r=checkDelimiter(t[0],e);return{type:"delimsizing",mode:e.parser.mode,size:eX[e.funcName].size,mclass:eX[e.funcName].mclass,delim:r.text}},htmlBuilder:(e,t)=>"."===e.delim?ep.makeSpan([e.mclass]):eW.sizedDelim(e.delim,e.size,t,e.mode,[e.mclass]),mathmlBuilder:e=>{var t=[];"."!==e.delim&&t.push(makeText(e.delim,e.mode));var r=new eE.MathNode("mo",t);"mopen"===e.mclass||"mclose"===e.mclass?r.setAttribute("fence","true"):r.setAttribute("fence","false"),r.setAttribute("stretchy","true");var n=makeEm(eW.sizeToMaxHeight[e.size]);return r.setAttribute("minsize",n),r.setAttribute("maxsize",n),r}}),defineFunction({type:"leftright-right",names:["\\right"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=e.parser.gullet.macros.get("\\current@color");if(r&&"string"!=typeof r)throw new ParseError("\\current@color set to non-string in \\right");return{type:"leftright-right",mode:e.parser.mode,delim:checkDelimiter(t[0],e).text,color:r}}}),defineFunction({type:"leftright",names:["\\left"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=checkDelimiter(t[0],e),n=e.parser;++n.leftrightDepth;var i=n.parseExpression(!1);--n.leftrightDepth,n.expect("\\right",!1);var a=assertNodeType(n.parseFunction(),"leftright-right");return{type:"leftright",mode:n.mode,body:i,left:r.text,right:a.delim,rightColor:a.color}},htmlBuilder:(e,t)=>{assertParsed(e);for(var r,n,i=buildExpression$1(e.body,t,!0,["mopen","mclose"]),a=0,o=0,l=!1,s=0;s<i.length;s++)i[s].isMiddle?l=!0:(a=Math.max(i[s].height,a),o=Math.max(i[s].depth,o));if(a*=t.sizeMultiplier,o*=t.sizeMultiplier,r="."===e.left?makeNullDelimiter(t,["mopen"]):eW.leftRightDelim(e.left,a,o,t,e.mode,["mopen"]),i.unshift(r),l)for(var m=1;m<i.length;m++){var h=i[m].isMiddle;h&&(i[m]=eW.leftRightDelim(h.delim,a,o,h.options,e.mode,[]))}if("."===e.right)n=makeNullDelimiter(t,["mclose"]);else{var d=e.rightColor?t.withColor(e.rightColor):t;n=eW.leftRightDelim(e.right,a,o,d,e.mode,["mclose"])}return i.push(n),ep.makeSpan(["minner"],i,t)},mathmlBuilder:(e,t)=>{assertParsed(e);var r=buildExpression(e.body,t);if("."!==e.left){var n=new eE.MathNode("mo",[makeText(e.left,e.mode)]);n.setAttribute("fence","true"),r.unshift(n)}if("."!==e.right){var i=new eE.MathNode("mo",[makeText(e.right,e.mode)]);i.setAttribute("fence","true"),e.rightColor&&i.setAttribute("mathcolor",e.rightColor),r.push(i)}return makeRow(r)}}),defineFunction({type:"middle",names:["\\middle"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var r=checkDelimiter(t[0],e);if(!e.parser.leftrightDepth)throw new ParseError("\\middle without preceding \\left",r);return{type:"middle",mode:e.parser.mode,delim:r.text}},htmlBuilder:(e,t)=>{var r;if("."===e.delim)r=makeNullDelimiter(t,[]);else{r=eW.sizedDelim(e.delim,1,t,e.mode,[]);var n={delim:e.delim,options:t};r.isMiddle=n}return r},mathmlBuilder:(e,t)=>{var r="\\vert"===e.delim||"|"===e.delim?makeText("|","text"):makeText(e.delim,e.mode),n=new eE.MathNode("mo",[r]);return n.setAttribute("fence","true"),n.setAttribute("lspace","0.05em"),n.setAttribute("rspace","0.05em"),n}});var htmlBuilder$7=(e,t)=>{var r,n,i=ep.wrapFragment(buildGroup$1(e.body,t),t),a=e.label.slice(1),o=t.sizeMultiplier,l=0,m=s.isCharacterBox(e.body);if("sout"===a)(r=ep.makeSpan(["stretchy","sout"])).height=t.fontMetrics().defaultRuleThickness/o,l=-.5*t.fontMetrics().xHeight;else if("phase"===a){var h=calculateSize({number:.6,unit:"pt"},t),d=calculateSize({number:.35,unit:"ex"},t);o/=t.havingBaseSizing().sizeMultiplier;var c=i.height+i.depth+h+d;i.style.paddingLeft=makeEm(c/2+h);var u=Math.floor(1e3*c*o),p="M400000 "+u+" H0 L"+u/2+" 0 l65 45 L145 "+(u-80)+" H400000z",f=new SvgNode([new PathNode("phase",p)],{width:"400em",height:makeEm(u/1e3),viewBox:"0 0 400000 "+u,preserveAspectRatio:"xMinYMin slice"});(r=ep.makeSvgSpan(["hide-tail"],[f],t)).style.height=makeEm(c),l=i.depth+h+d}else{/cancel/.test(a)?m||i.classes.push("cancel-pad"):"angl"===a?i.classes.push("anglpad"):i.classes.push("boxpad");var b=0,g=0,y=0;/box/.test(a)?(y=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness),g=b=t.fontMetrics().fboxsep+("colorbox"===a?0:y)):"angl"===a?(b=4*(y=Math.max(t.fontMetrics().defaultRuleThickness,t.minRuleThickness)),g=Math.max(0,.25-i.depth)):g=b=m?.2:0,r=eC.encloseSpan(i,a,b,g,t),/fbox|boxed|fcolorbox/.test(a)?(r.style.borderStyle="solid",r.style.borderWidth=makeEm(y)):"angl"===a&&.049!==y&&(r.style.borderTopWidth=makeEm(y),r.style.borderRightWidth=makeEm(y)),l=i.depth+g,e.backgroundColor&&(r.style.backgroundColor=e.backgroundColor,e.borderColor&&(r.style.borderColor=e.borderColor))}if(e.backgroundColor)n=ep.makeVList({positionType:"individualShift",children:[{type:"elem",elem:r,shift:l},{type:"elem",elem:i,shift:0}]},t);else{var v=/cancel|phase/.test(a)?["svg-align"]:[];n=ep.makeVList({positionType:"individualShift",children:[{type:"elem",elem:i,shift:0},{type:"elem",elem:r,shift:l,wrapperClasses:v}]},t)}return(/cancel/.test(a)&&(n.height=i.height,n.depth=i.depth),/cancel/.test(a)&&!m)?ep.makeSpan(["mord","cancel-lap"],[n],t):ep.makeSpan(["mord"],[n],t)},mathmlBuilder$6=(e,t)=>{var r=0,n=new eE.MathNode(e.label.indexOf("colorbox")>-1?"mpadded":"menclose",[buildGroup(e.body,t)]);switch(e.label){case"\\cancel":n.setAttribute("notation","updiagonalstrike");break;case"\\bcancel":n.setAttribute("notation","downdiagonalstrike");break;case"\\phase":n.setAttribute("notation","phasorangle");break;case"\\sout":n.setAttribute("notation","horizontalstrike");break;case"\\fbox":n.setAttribute("notation","box");break;case"\\angl":n.setAttribute("notation","actuarial");break;case"\\fcolorbox":case"\\colorbox":if(r=t.fontMetrics().fboxsep*t.fontMetrics().ptPerEm,n.setAttribute("width","+"+2*r+"pt"),n.setAttribute("height","+"+2*r+"pt"),n.setAttribute("lspace",r+"pt"),n.setAttribute("voffset",r+"pt"),"\\fcolorbox"===e.label){var i=Math.max(t.fontMetrics().fboxrule,t.minRuleThickness);n.setAttribute("style","border: "+i+"em solid "+String(e.borderColor))}break;case"\\xcancel":n.setAttribute("notation","updiagonalstrike downdiagonalstrike")}return e.backgroundColor&&n.setAttribute("mathbackground",e.backgroundColor),n};defineFunction({type:"enclose",names:["\\colorbox"],props:{numArgs:2,allowedInText:!0,argTypes:["color","text"]},handler(e,t,r){var{parser:n,funcName:i}=e,a=assertNodeType(t[0],"color-token").color,o=t[1];return{type:"enclose",mode:n.mode,label:i,backgroundColor:a,body:o}},htmlBuilder:htmlBuilder$7,mathmlBuilder:mathmlBuilder$6}),defineFunction({type:"enclose",names:["\\fcolorbox"],props:{numArgs:3,allowedInText:!0,argTypes:["color","color","text"]},handler(e,t,r){var{parser:n,funcName:i}=e,a=assertNodeType(t[0],"color-token").color,o=assertNodeType(t[1],"color-token").color,l=t[2];return{type:"enclose",mode:n.mode,label:i,backgroundColor:o,borderColor:a,body:l}},htmlBuilder:htmlBuilder$7,mathmlBuilder:mathmlBuilder$6}),defineFunction({type:"enclose",names:["\\fbox"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\fbox",body:t[0]}}}),defineFunction({type:"enclose",names:["\\cancel","\\bcancel","\\xcancel","\\sout","\\phase"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:n}=e,i=t[0];return{type:"enclose",mode:r.mode,label:n,body:i}},htmlBuilder:htmlBuilder$7,mathmlBuilder:mathmlBuilder$6}),defineFunction({type:"enclose",names:["\\angl"],props:{numArgs:1,argTypes:["hbox"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"enclose",mode:r.mode,label:"\\angl",body:t[0]}}});var ej={};function defineEnvironment(e){for(var{type:t,names:r,props:n,handler:i,htmlBuilder:a,mathmlBuilder:o}=e,l={type:t,numArgs:n.numArgs||0,allowedInText:!1,numOptionalArgs:0,handler:i},s=0;s<r.length;++s)ej[r[s]]=l;a&&(ex[t]=a),o&&(ew[t]=o)}var eZ={};function getHLines(e){var t=[];e.consumeSpaces();var r=e.fetch().text;for("\\relax"===r&&(e.consume(),e.consumeSpaces(),r=e.fetch().text);"\\hline"===r||"\\hdashline"===r;)e.consume(),t.push("\\hdashline"===r),e.consumeSpaces(),r=e.fetch().text;return t}var validateAmsEnvironmentContext=e=>{if(!e.parser.settings.displayMode)throw new ParseError("{"+e.envName+"} can be used only in display mode.")};function getAutoTag(e){if(-1===e.indexOf("ed"))return -1===e.indexOf("*")}function parseArray(e,t,r){var{hskipBeforeAndAfter:n,addJot:i,cols:a,arraystretch:o,colSeparationType:l,autoTag:s,singleRow:m,emptySingleRow:h,maxNumCols:d,leqno:c}=t;if(e.gullet.beginGroup(),m||e.gullet.macros.set("\\cr","\\\\\\relax"),!o){var u=e.gullet.expandMacroAsText("\\arraystretch");if(null==u)o=1;else if(!(o=parseFloat(u))||o<0)throw new ParseError("Invalid \\arraystretch: "+u)}e.gullet.beginGroup();var p=[],f=[p],b=[],g=[],y=null!=s?[]:void 0;function beginRow(){s&&e.gullet.macros.set("\\@eqnsw","1",!0)}function endRow(){y&&(e.gullet.macros.get("\\df@tag")?(y.push(e.subparse([new Token("\\df@tag")])),e.gullet.macros.set("\\df@tag",void 0,!0)):y.push(!!s&&"1"===e.gullet.macros.get("\\@eqnsw")))}for(beginRow(),g.push(getHLines(e));;){var v=e.parseExpression(!1,m?"\\end":"\\\\");e.gullet.endGroup(),e.gullet.beginGroup(),v={type:"ordgroup",mode:e.mode,body:v},r&&(v={type:"styling",mode:e.mode,style:r,body:[v]}),p.push(v);var S=e.fetch().text;if("&"===S){if(d&&p.length===d){if(m||l)throw new ParseError("Too many tab characters: &",e.nextToken);e.settings.reportNonstrict("textEnv","Too few columns specified in the {array} column argument.")}e.consume()}else if("\\end"===S){endRow(),1===p.length&&"styling"===v.type&&0===v.body[0].body.length&&(f.length>1||!h)&&f.pop(),g.length<f.length+1&&g.push([]);break}else if("\\\\"===S){e.consume();var x=void 0;" "!==e.gullet.future().text&&(x=e.parseSizeGroup(!0)),b.push(x?x.value:null),endRow(),g.push(getHLines(e)),p=[],f.push(p),beginRow()}else throw new ParseError("Expected & or \\\\ or \\cr or \\end",e.nextToken)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:e.mode,addJot:i,arraystretch:o,body:f,cols:a,rowGaps:b,hskipBeforeAndAfter:n,hLinesBeforeRow:g,colSeparationType:l,tags:y,leqno:c}}function dCellStyle(e){return"d"===e.slice(0,1)?"display":"text"}var htmlBuilder$6=function(e,t){var r,n,i,a,o=e.body.length,l=e.hLinesBeforeRow,m=0,h=Array(o),d=[],c=Math.max(t.fontMetrics().arrayRuleWidth,t.minRuleThickness),u=1/t.fontMetrics().ptPerEm,p=5*u;e.colSeparationType&&"small"===e.colSeparationType&&(p=.2778*(t.havingStyle(g.SCRIPT).sizeMultiplier/t.sizeMultiplier));var f="CD"===e.colSeparationType?calculateSize({number:3,unit:"ex"},t):12*u,b=3*u,y=e.arraystretch*f,v=.7*y,S=.3*y,x=0;function setHLinePos(e){for(var t=0;t<e.length;++t)t>0&&(x+=.25),d.push({pos:x,isDashed:e[t]})}for(setHLinePos(l[0]),r=0;r<e.body.length;++r){var w=e.body[r],k=v,M=S;m<w.length&&(m=w.length);var z=Array(w.length);for(n=0;n<w.length;++n){var T=buildGroup$1(w[n],t);M<T.depth&&(M=T.depth),k<T.height&&(k=T.height),z[n]=T}var A=e.rowGaps[r],E=0;A&&(E=calculateSize(A,t))>0&&(M<(E+=S)&&(M=E),E=0),e.addJot&&(M+=b),z.height=k,z.depth=M,x+=k,z.pos=x,x+=M+E,h[r]=z,setHLinePos(l[r+1])}var B=x/2+t.fontMetrics().axisHeight,N=e.cols||[],C=[],q=[];if(e.tags&&e.tags.some(e=>e))for(r=0;r<o;++r){var P=h[r],F=P.pos-B,H=e.tags[r],R=void 0;(R=!0===H?ep.makeSpan(["eqn-num"],[],t):!1===H?ep.makeSpan([],[],t):ep.makeSpan([],buildExpression$1(H,t,!0),t)).depth=P.depth,R.height=P.height,q.push({type:"elem",elem:R,shift:F})}for(n=0,a=0;n<m||a<N.length;++n,++a){for(var I=N[a]||{},D=!0;"separator"===I.type;){if(D||((i=ep.makeSpan(["arraycolsep"],[])).style.width=makeEm(t.fontMetrics().doubleRuleSep),C.push(i)),"|"===I.separator||":"===I.separator){var L="|"===I.separator?"solid":"dashed",O=ep.makeSpan(["vertical-separator"],[],t);O.style.height=makeEm(x),O.style.borderRightWidth=makeEm(c),O.style.borderRightStyle=L,O.style.margin="0 "+makeEm(-c/2);var $=x-B;$&&(O.style.verticalAlign=makeEm(-$)),C.push(O)}else throw new ParseError("Invalid separator type: "+I.separator);I=N[++a]||{},D=!1}if(!(n>=m)){var G=void 0;(n>0||e.hskipBeforeAndAfter)&&0!==(G=s.deflt(I.pregap,p))&&((i=ep.makeSpan(["arraycolsep"],[])).style.width=makeEm(G),C.push(i));var V=[];for(r=0;r<o;++r){var U=h[r],Y=U[n];if(Y){var W=U.pos-B;Y.depth=U.depth,Y.height=U.height,V.push({type:"elem",elem:Y,shift:W})}}V=ep.makeVList({positionType:"individualShift",children:V},t),V=ep.makeSpan(["col-align-"+(I.align||"c")],[V]),C.push(V),(n<m-1||e.hskipBeforeAndAfter)&&0!==(G=s.deflt(I.postgap,p))&&((i=ep.makeSpan(["arraycolsep"],[])).style.width=makeEm(G),C.push(i))}}if(h=ep.makeSpan(["mtable"],C),d.length>0){for(var X=ep.makeLineSpan("hline",t,c),_=ep.makeLineSpan("hdashline",t,c),j=[{type:"elem",elem:h,shift:0}];d.length>0;){var Z=d.pop(),K=Z.pos-B;Z.isDashed?j.push({type:"elem",elem:_,shift:K}):j.push({type:"elem",elem:X,shift:K})}h=ep.makeVList({positionType:"individualShift",children:j},t)}if(0===q.length)return ep.makeSpan(["mord"],[h],t);var J=ep.makeVList({positionType:"individualShift",children:q},t);return J=ep.makeSpan(["tag"],[J],t),ep.makeFragment([h,J])},eK={c:"center ",l:"left ",r:"right "},mathmlBuilder$5=function(e,t){for(var r=[],n=new eE.MathNode("mtd",[],["mtr-glue"]),i=new eE.MathNode("mtd",[],["mml-eqn-num"]),a=0;a<e.body.length;a++){for(var o=e.body[a],l=[],s=0;s<o.length;s++)l.push(new eE.MathNode("mtd",[buildGroup(o[s],t)]));e.tags&&e.tags[a]&&(l.unshift(n),l.push(n),e.leqno?l.unshift(i):l.push(i)),r.push(new eE.MathNode("mtr",l))}var m=new eE.MathNode("mtable",r),h=.5===e.arraystretch?.1:.16+e.arraystretch-1+(e.addJot?.09:0);m.setAttribute("rowspacing",makeEm(h));var d="",c="";if(e.cols&&e.cols.length>0){var u=e.cols,p="",f=!1,b=0,g=u.length;"separator"===u[0].type&&(d+="top ",b=1),"separator"===u[u.length-1].type&&(d+="bottom ",g-=1);for(var y=b;y<g;y++)"align"===u[y].type?(c+=eK[u[y].align],f&&(p+="none "),f=!0):"separator"===u[y].type&&f&&(p+="|"===u[y].separator?"solid ":"dashed ",f=!1);m.setAttribute("columnalign",c.trim()),/[sd]/.test(p)&&m.setAttribute("columnlines",p.trim())}if("align"===e.colSeparationType){for(var v=e.cols||[],S="",x=1;x<v.length;x++)S+=x%2?"0em ":"1em ";m.setAttribute("columnspacing",S.trim())}else"alignat"===e.colSeparationType||"gather"===e.colSeparationType?m.setAttribute("columnspacing","0em"):"small"===e.colSeparationType?m.setAttribute("columnspacing","0.2778em"):"CD"===e.colSeparationType?m.setAttribute("columnspacing","0.5em"):m.setAttribute("columnspacing","1em");var w="",k=e.hLinesBeforeRow;d+=(k[0].length>0?"left ":"")+(k[k.length-1].length>0?"right ":"");for(var M=1;M<k.length-1;M++)w+=0===k[M].length?"none ":k[M][0]?"dashed ":"solid ";return/[sd]/.test(w)&&m.setAttribute("rowlines",w.trim()),""!==d&&(m=new eE.MathNode("menclose",[m])).setAttribute("notation",d.trim()),e.arraystretch&&e.arraystretch<1&&(m=new eE.MathNode("mstyle",[m])).setAttribute("scriptlevel","1"),m},alignedHandler=function(e,t){-1===e.envName.indexOf("ed")&&validateAmsEnvironmentContext(e);var r,n=[],i=e.envName.indexOf("at")>-1?"alignat":"align",a="split"===e.envName,o=parseArray(e.parser,{cols:n,addJot:!0,autoTag:a?void 0:getAutoTag(e.envName),emptySingleRow:!0,colSeparationType:i,maxNumCols:a?2:void 0,leqno:e.parser.settings.leqno},"display"),l=0,s={type:"ordgroup",mode:e.mode,body:[]};if(t[0]&&"ordgroup"===t[0].type){for(var m="",h=0;h<t[0].body.length;h++)m+=assertNodeType(t[0].body[h],"textord").text;l=2*(r=Number(m))}var d=!l;o.body.forEach(function(e){for(var t=1;t<e.length;t+=2){var n=assertNodeType(e[t],"styling");assertNodeType(n.body[0],"ordgroup").body.unshift(s)}if(d)l<e.length&&(l=e.length);else{var i=e.length/2;if(r<i)throw new ParseError("Too many math in a row: expected "+r+", but got "+i,e[0])}});for(var c=0;c<l;++c){var u="r",p=0;c%2==1?u="l":c>0&&d&&(p=1),n[c]={type:"align",align:u,pregap:p,postgap:0}}return o.colSeparationType=d?"align":"alignat",o};defineEnvironment({type:"array",names:["array","darray"],props:{numArgs:1},handler(e,t){var r=(checkSymbolNodeType(t[0])?[t[0]]:assertNodeType(t[0],"ordgroup").body).map(function(e){var t=assertSymbolNodeType(e).text;if(-1!=="lcr".indexOf(t))return{type:"align",align:t};if("|"===t)return{type:"separator",separator:"|"};if(":"===t)return{type:"separator",separator:":"};throw new ParseError("Unknown column alignment: "+t,e)}),n={cols:r,hskipBeforeAndAfter:!0,maxNumCols:r.length};return parseArray(e.parser,n,dCellStyle(e.envName))},htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["matrix","pmatrix","bmatrix","Bmatrix","vmatrix","Vmatrix","matrix*","pmatrix*","bmatrix*","Bmatrix*","vmatrix*","Vmatrix*"],props:{numArgs:0},handler(e){var t={matrix:null,pmatrix:["(",")"],bmatrix:["[","]"],Bmatrix:["\\{","\\}"],vmatrix:["|","|"],Vmatrix:["\\Vert","\\Vert"]}[e.envName.replace("*","")],r="c",n={hskipBeforeAndAfter:!1,cols:[{type:"align",align:r}]};if("*"===e.envName.charAt(e.envName.length-1)){var i=e.parser;if(i.consumeSpaces(),"["===i.fetch().text){if(i.consume(),i.consumeSpaces(),r=i.fetch().text,-1==="lcr".indexOf(r))throw new ParseError("Expected l or c or r",i.nextToken);i.consume(),i.consumeSpaces(),i.expect("]"),i.consume(),n.cols=[{type:"align",align:r}]}}var a=parseArray(e.parser,n,dCellStyle(e.envName)),o=Math.max(0,...a.body.map(e=>e.length));return a.cols=Array(o).fill({type:"align",align:r}),t?{type:"leftright",mode:e.mode,body:[a],left:t[0],right:t[1],rightColor:void 0}:a},htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["smallmatrix"],props:{numArgs:0},handler(e){var t=parseArray(e.parser,{arraystretch:.5},"script");return t.colSeparationType="small",t},htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["subarray"],props:{numArgs:1},handler(e,t){var r=(checkSymbolNodeType(t[0])?[t[0]]:assertNodeType(t[0],"ordgroup").body).map(function(e){var t=assertSymbolNodeType(e).text;if(-1!=="lc".indexOf(t))return{type:"align",align:t};throw new ParseError("Unknown column alignment: "+t,e)});if(r.length>1)throw new ParseError("{subarray} can contain only one column");var n={cols:r,hskipBeforeAndAfter:!1,arraystretch:.5};if((n=parseArray(e.parser,n,"script")).body.length>0&&n.body[0].length>1)throw new ParseError("{subarray} can contain only one column");return n},htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["cases","dcases","rcases","drcases"],props:{numArgs:0},handler(e){var t=parseArray(e.parser,{arraystretch:1.2,cols:[{type:"align",align:"l",pregap:0,postgap:1},{type:"align",align:"l",pregap:0,postgap:0}]},dCellStyle(e.envName));return{type:"leftright",mode:e.mode,body:[t],left:e.envName.indexOf("r")>-1?".":"\\{",right:e.envName.indexOf("r")>-1?"\\}":".",rightColor:void 0}},htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["align","align*","aligned","split"],props:{numArgs:0},handler:alignedHandler,htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["gathered","gather","gather*"],props:{numArgs:0},handler(e){s.contains(["gather","gather*"],e.envName)&&validateAmsEnvironmentContext(e);var t={cols:[{type:"align",align:"c"}],addJot:!0,colSeparationType:"gather",autoTag:getAutoTag(e.envName),emptySingleRow:!0,leqno:e.parser.settings.leqno};return parseArray(e.parser,t,"display")},htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["alignat","alignat*","alignedat"],props:{numArgs:1},handler:alignedHandler,htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["equation","equation*"],props:{numArgs:0},handler(e){validateAmsEnvironmentContext(e);var t={autoTag:getAutoTag(e.envName),emptySingleRow:!0,singleRow:!0,maxNumCols:1,leqno:e.parser.settings.leqno};return parseArray(e.parser,t,"display")},htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),defineEnvironment({type:"array",names:["CD"],props:{numArgs:0},handler:e=>(validateAmsEnvironmentContext(e),function(e){var t=[];for(e.gullet.beginGroup(),e.gullet.macros.set("\\cr","\\\\\\relax"),e.gullet.beginGroup();;){t.push(e.parseExpression(!1,"\\\\")),e.gullet.endGroup(),e.gullet.beginGroup();var r=e.fetch().text;if("&"===r||"\\\\"===r)e.consume();else if("\\end"===r){0===t[t.length-1].length&&t.pop();break}else throw new ParseError("Expected \\\\ or \\cr or \\end",e.nextToken)}for(var n=[],i=[n],a=0;a<t.length;a++){for(var o=t[a],l=newCell(),s=0;s<o.length;s++)if(isStartOfArrow(o[s])){n.push(l);var m=assertSymbolNodeType(o[s+=1]).text,h=[,,];if(h[0]={type:"ordgroup",mode:"math",body:[]},h[1]={type:"ordgroup",mode:"math",body:[]},"=|.".indexOf(m)>-1);else if("<>AV".indexOf(m)>-1)for(var d=0;d<2;d++){for(var c=!0,u=s+1;u<o.length;u++){if(isLabelEnd(o[u],m)){c=!1,s=u;break}if(isStartOfArrow(o[u]))throw new ParseError("Missing a "+m+" character to complete a CD arrow.",o[u]);h[d].body.push(o[u])}if(c)throw new ParseError("Missing a "+m+" character to complete a CD arrow.",o[s])}else throw new ParseError('Expected one of "<>AV=|." after @',o[s]);var p={type:"styling",body:[function(e,t,r){var n=eF[e];switch(n){case"\\\\cdrightarrow":case"\\\\cdleftarrow":return r.callFunction(n,[t[0]],[t[1]]);case"\\uparrow":case"\\downarrow":var i=r.callFunction("\\\\cdleft",[t[0]],[]),a=r.callFunction("\\Big",[{type:"atom",text:n,mode:"math",family:"rel"}],[]),o=r.callFunction("\\\\cdright",[t[1]],[]);return r.callFunction("\\\\cdparent",[{type:"ordgroup",mode:"math",body:[i,a,o]}],[]);case"\\\\cdlongequal":return r.callFunction("\\\\cdlongequal",[],[]);case"\\Vert":return r.callFunction("\\Big",[{type:"textord",text:"\\Vert",mode:"math"}],[]);default:return{type:"textord",text:" ",mode:"math"}}}(m,h,e)],mode:"math",style:"display"};n.push(p),l=newCell()}else l.body.push(o[s]);a%2==0?n.push(l):n.shift(),n=[],i.push(n)}return e.gullet.endGroup(),e.gullet.endGroup(),{type:"array",mode:"math",body:i,arraystretch:1,addJot:!0,rowGaps:[null],cols:Array(i[0].length).fill({type:"align",align:"c",pregap:.25,postgap:.25}),colSeparationType:"CD",hLinesBeforeRow:Array(i.length+1).fill([])}}(e.parser)),htmlBuilder:htmlBuilder$6,mathmlBuilder:mathmlBuilder$5}),eZ["\\nonumber"]="\\gdef\\@eqnsw{0}",eZ["\\notag"]="\\nonumber",defineFunction({type:"text",names:["\\hline","\\hdashline"],props:{numArgs:0,allowedInText:!0,allowedInMath:!0},handler(e,t){throw new ParseError(e.funcName+" valid only within array environment")}}),defineFunction({type:"environment",names:["\\begin","\\end"],props:{numArgs:1,argTypes:["text"]},handler(e,t){var{parser:r,funcName:n}=e,i=t[0];if("ordgroup"!==i.type)throw new ParseError("Invalid environment name",i);for(var a="",o=0;o<i.body.length;++o)a+=assertNodeType(i.body[o],"textord").text;if("\\begin"===n){if(!ej.hasOwnProperty(a))throw new ParseError("No such environment: "+a,i);var l=ej[a],{args:s,optArgs:m}=r.parseArguments("\\begin{"+a+"}",l),h={mode:r.mode,envName:a,parser:r},d=l.handler(h,s,m);r.expect("\\end",!1);var c=r.nextToken,u=assertNodeType(r.parseFunction(),"environment");if(u.name!==a)throw new ParseError("Mismatch: \\begin{"+a+"} matched by \\end{"+u.name+"}",c);return d}return{type:"environment",mode:r.mode,name:a,nameGroup:i}}});var htmlBuilder$5=(e,t)=>{var r=e.font,n=t.withFont(r);return buildGroup$1(e.body,n)},mathmlBuilder$4=(e,t)=>{var r=e.font,n=t.withFont(r);return buildGroup(e.body,n)},eJ={"\\Bbb":"\\mathbb","\\bold":"\\mathbf","\\frak":"\\mathfrak","\\bm":"\\boldsymbol"};defineFunction({type:"font",names:["\\mathrm","\\mathit","\\mathbf","\\mathnormal","\\mathsfit","\\mathbb","\\mathcal","\\mathfrak","\\mathscr","\\mathsf","\\mathtt","\\Bbb","\\bold","\\frak"],props:{numArgs:1,allowedInArgument:!0},handler:(e,t)=>{var{parser:r,funcName:n}=e,i=normalizeArgument(t[0]),a=n;return a in eJ&&(a=eJ[a]),{type:"font",mode:r.mode,font:a.slice(1),body:i}},htmlBuilder:htmlBuilder$5,mathmlBuilder:mathmlBuilder$4}),defineFunction({type:"mclass",names:["\\boldsymbol","\\bm"],props:{numArgs:1},handler:(e,t)=>{var{parser:r}=e,n=t[0],i=s.isCharacterBox(n);return{type:"mclass",mode:r.mode,mclass:binrelClass(n),body:[{type:"font",mode:r.mode,font:"boldsymbol",body:n}],isCharacterBox:i}}}),defineFunction({type:"font",names:["\\rm","\\sf","\\tt","\\bf","\\it","\\cal"],props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:n,breakOnTokenText:i}=e,{mode:a}=r,o=r.parseExpression(!0,i);return{type:"font",mode:a,font:"math"+n.slice(1),body:{type:"ordgroup",mode:r.mode,body:o}}},htmlBuilder:htmlBuilder$5,mathmlBuilder:mathmlBuilder$4});var adjustStyle=(e,t)=>{var r=t;return"display"===e?r=r.id>=g.SCRIPT.id?r.text():g.DISPLAY:"text"===e&&r.size===g.DISPLAY.size?r=g.TEXT:"script"===e?r=g.SCRIPT:"scriptscript"===e&&(r=g.SCRIPTSCRIPT),r},htmlBuilder$4=(e,t)=>{var r,n,i,a,o,l,s,m,h,d,c,u=adjustStyle(e.size,t.style),p=u.fracNum(),f=u.fracDen();r=t.havingStyle(p);var b=buildGroup$1(e.numer,r,t);if(e.continued){var y=8.5/t.fontMetrics().ptPerEm,v=3.5/t.fontMetrics().ptPerEm;b.height=b.height<y?y:b.height,b.depth=b.depth<v?v:b.depth}r=t.havingStyle(f);var S=buildGroup$1(e.denom,r,t);if(e.hasBarLine?(e.barSize?(i=calculateSize(e.barSize,t),n=ep.makeLineSpan("frac-line",t,i)):n=ep.makeLineSpan("frac-line",t),i=n.height,a=n.height):(n=null,i=0,a=t.fontMetrics().defaultRuleThickness),u.size===g.DISPLAY.size||"display"===e.size?(o=t.fontMetrics().num1,l=i>0?3*a:7*a,s=t.fontMetrics().denom1):(i>0?(o=t.fontMetrics().num2,l=a):(o=t.fontMetrics().num3,l=3*a),s=t.fontMetrics().denom2),n){var x=t.fontMetrics().axisHeight;o-b.depth-(x+.5*i)<l&&(o+=l-(o-b.depth-(x+.5*i))),x-.5*i-(S.height-s)<l&&(s+=l-(x-.5*i-(S.height-s)));var w=-(x-.5*i);m=ep.makeVList({positionType:"individualShift",children:[{type:"elem",elem:S,shift:s},{type:"elem",elem:n,shift:w},{type:"elem",elem:b,shift:-o}]},t)}else{var k=o-b.depth-(S.height-s);k<l&&(o+=.5*(l-k),s+=.5*(l-k)),m=ep.makeVList({positionType:"individualShift",children:[{type:"elem",elem:S,shift:s},{type:"elem",elem:b,shift:-o}]},t)}return r=t.havingStyle(u),m.height*=r.sizeMultiplier/t.sizeMultiplier,m.depth*=r.sizeMultiplier/t.sizeMultiplier,h=u.size===g.DISPLAY.size?t.fontMetrics().delim1:u.size===g.SCRIPTSCRIPT.size?t.havingStyle(g.SCRIPT).fontMetrics().delim2:t.fontMetrics().delim2,d=null==e.leftDelim?makeNullDelimiter(t,["mopen"]):eW.customSizedDelim(e.leftDelim,h,!0,t.havingStyle(u),e.mode,["mopen"]),c=e.continued?ep.makeSpan([]):null==e.rightDelim?makeNullDelimiter(t,["mclose"]):eW.customSizedDelim(e.rightDelim,h,!0,t.havingStyle(u),e.mode,["mclose"]),ep.makeSpan(["mord"].concat(r.sizingClasses(t)),[d,ep.makeSpan(["mfrac"],[m]),c],t)},mathmlBuilder$3=(e,t)=>{var r=new eE.MathNode("mfrac",[buildGroup(e.numer,t),buildGroup(e.denom,t)]);if(e.hasBarLine){if(e.barSize){var n=calculateSize(e.barSize,t);r.setAttribute("linethickness",makeEm(n))}}else r.setAttribute("linethickness","0px");var i=adjustStyle(e.size,t.style);if(i.size!==t.style.size){r=new eE.MathNode("mstyle",[r]);var a=i.size===g.DISPLAY.size?"true":"false";r.setAttribute("displaystyle",a),r.setAttribute("scriptlevel","0")}if(null!=e.leftDelim||null!=e.rightDelim){var o=[];if(null!=e.leftDelim){var l=new eE.MathNode("mo",[new eE.TextNode(e.leftDelim.replace("\\",""))]);l.setAttribute("fence","true"),o.push(l)}if(o.push(r),null!=e.rightDelim){var s=new eE.MathNode("mo",[new eE.TextNode(e.rightDelim.replace("\\",""))]);s.setAttribute("fence","true"),o.push(s)}return makeRow(o)}return r};defineFunction({type:"genfrac",names:["\\dfrac","\\frac","\\tfrac","\\dbinom","\\binom","\\tbinom","\\\\atopfrac","\\\\bracefrac","\\\\brackfrac"],props:{numArgs:2,allowedInArgument:!0},handler:(e,t)=>{var r,{parser:n,funcName:i}=e,a=t[0],o=t[1],l=null,s=null,m="auto";switch(i){case"\\dfrac":case"\\frac":case"\\tfrac":r=!0;break;case"\\\\atopfrac":r=!1;break;case"\\dbinom":case"\\binom":case"\\tbinom":r=!1,l="(",s=")";break;case"\\\\bracefrac":r=!1,l="\\{",s="\\}";break;case"\\\\brackfrac":r=!1,l="[",s="]";break;default:throw Error("Unrecognized genfrac command")}switch(i){case"\\dfrac":case"\\dbinom":m="display";break;case"\\tfrac":case"\\tbinom":m="text"}return{type:"genfrac",mode:n.mode,continued:!1,numer:a,denom:o,hasBarLine:r,leftDelim:l,rightDelim:s,size:m,barSize:null}},htmlBuilder:htmlBuilder$4,mathmlBuilder:mathmlBuilder$3}),defineFunction({type:"genfrac",names:["\\cfrac"],props:{numArgs:2},handler:(e,t)=>{var{parser:r,funcName:n}=e,i=t[0],a=t[1];return{type:"genfrac",mode:r.mode,continued:!0,numer:i,denom:a,hasBarLine:!0,leftDelim:null,rightDelim:null,size:"display",barSize:null}}}),defineFunction({type:"infix",names:["\\over","\\choose","\\atop","\\brace","\\brack"],props:{numArgs:0,infix:!0},handler(e){var t,{parser:r,funcName:n,token:i}=e;switch(n){case"\\over":t="\\frac";break;case"\\choose":t="\\binom";break;case"\\atop":t="\\\\atopfrac";break;case"\\brace":t="\\\\bracefrac";break;case"\\brack":t="\\\\brackfrac";break;default:throw Error("Unrecognized infix genfrac command")}return{type:"infix",mode:r.mode,replaceWith:t,token:i}}});var eQ=["display","text","script","scriptscript"],delimFromValue=function(e){var t=null;return e.length>0&&(t="."===(t=e)?null:t),t};defineFunction({type:"genfrac",names:["\\genfrac"],props:{numArgs:6,allowedInArgument:!0,argTypes:["math","math","size","text","math","math"]},handler(e,t){var r,{parser:n}=e,i=t[4],a=t[5],o=normalizeArgument(t[0]),l="atom"===o.type&&"open"===o.family?delimFromValue(o.text):null,s=normalizeArgument(t[1]),m="atom"===s.type&&"close"===s.family?delimFromValue(s.text):null,h=assertNodeType(t[2],"size"),d=null;r=!!h.isBlank||(d=h.value).number>0;var c="auto",u=t[3];return"ordgroup"===u.type?u.body.length>0&&(c=eQ[Number(assertNodeType(u.body[0],"textord").text)]):c=eQ[Number((u=assertNodeType(u,"textord")).text)],{type:"genfrac",mode:n.mode,numer:i,denom:a,continued:!1,hasBarLine:r,barSize:d,leftDelim:l,rightDelim:m,size:c}},htmlBuilder:htmlBuilder$4,mathmlBuilder:mathmlBuilder$3}),defineFunction({type:"infix",names:["\\above"],props:{numArgs:1,argTypes:["size"],infix:!0},handler(e,t){var{parser:r,funcName:n,token:i}=e;return{type:"infix",mode:r.mode,replaceWith:"\\\\abovefrac",size:assertNodeType(t[0],"size").value,token:i}}}),defineFunction({type:"genfrac",names:["\\\\abovefrac"],props:{numArgs:3,argTypes:["math","size","math"]},handler:(e,t)=>{var{parser:r,funcName:n}=e,i=t[0],a=assert(assertNodeType(t[1],"infix").size),o=t[2],l=a.number>0;return{type:"genfrac",mode:r.mode,numer:i,denom:o,continued:!1,hasBarLine:l,barSize:a,leftDelim:null,rightDelim:null,size:"auto"}},htmlBuilder:htmlBuilder$4,mathmlBuilder:mathmlBuilder$3});var htmlBuilder$3=(e,t)=>{var r,n,i,a=t.style;"supsub"===e.type?(r=e.sup?buildGroup$1(e.sup,t.havingStyle(a.sup()),t):buildGroup$1(e.sub,t.havingStyle(a.sub()),t),n=assertNodeType(e.base,"horizBrace")):n=assertNodeType(e,"horizBrace");var o=buildGroup$1(n.base,t.havingBaseStyle(g.DISPLAY)),l=eC.svgSpan(n,t);if(n.isOver?(i=ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:o},{type:"kern",size:.1},{type:"elem",elem:l}]},t)).children[0].children[0].children[1].classes.push("svg-align"):(i=ep.makeVList({positionType:"bottom",positionData:o.depth+.1+l.height,children:[{type:"elem",elem:l},{type:"kern",size:.1},{type:"elem",elem:o}]},t)).children[0].children[0].children[0].classes.push("svg-align"),r){var s=ep.makeSpan(["mord",n.isOver?"mover":"munder"],[i],t);i=n.isOver?ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:s},{type:"kern",size:.2},{type:"elem",elem:r}]},t):ep.makeVList({positionType:"bottom",positionData:s.depth+.2+r.height+r.depth,children:[{type:"elem",elem:r},{type:"kern",size:.2},{type:"elem",elem:s}]},t)}return ep.makeSpan(["mord",n.isOver?"mover":"munder"],[i],t)};defineFunction({type:"horizBrace",names:["\\overbrace","\\underbrace"],props:{numArgs:1},handler(e,t){var{parser:r,funcName:n}=e;return{type:"horizBrace",mode:r.mode,label:n,isOver:/^\\over/.test(n),base:t[0]}},htmlBuilder:htmlBuilder$3,mathmlBuilder:(e,t)=>{var r=eC.mathMLnode(e.label);return new eE.MathNode(e.isOver?"mover":"munder",[buildGroup(e.base,t),r])}}),defineFunction({type:"href",names:["\\href"],props:{numArgs:2,argTypes:["url","original"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,n=t[1],i=assertNodeType(t[0],"url").url;return r.settings.isTrusted({command:"\\href",url:i})?{type:"href",mode:r.mode,href:i,body:ordargument(n)}:r.formatUnsupportedCmd("\\href")},htmlBuilder:(e,t)=>{var r=buildExpression$1(e.body,t,!1);return ep.makeAnchor(e.href,[],r,t)},mathmlBuilder:(e,t)=>{var r=buildExpressionRow(e.body,t);return r instanceof MathNode||(r=new MathNode("mrow",[r])),r.setAttribute("href",e.href),r}}),defineFunction({type:"href",names:["\\url"],props:{numArgs:1,argTypes:["url"],allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,n=assertNodeType(t[0],"url").url;if(!r.settings.isTrusted({command:"\\url",url:n}))return r.formatUnsupportedCmd("\\url");for(var i=[],a=0;a<n.length;a++){var o=n[a];"~"===o&&(o="\\textasciitilde"),i.push({type:"textord",mode:"text",text:o})}var l={type:"text",mode:r.mode,font:"\\texttt",body:i};return{type:"href",mode:r.mode,href:n,body:ordargument(l)}}}),defineFunction({type:"hbox",names:["\\hbox"],props:{numArgs:1,argTypes:["text"],allowedInText:!0,primitive:!0},handler(e,t){var{parser:r}=e;return{type:"hbox",mode:r.mode,body:ordargument(t[0])}},htmlBuilder(e,t){var r=buildExpression$1(e.body,t,!1);return ep.makeFragment(r)},mathmlBuilder:(e,t)=>new eE.MathNode("mrow",buildExpression(e.body,t))}),defineFunction({type:"html",names:["\\htmlClass","\\htmlId","\\htmlStyle","\\htmlData"],props:{numArgs:2,argTypes:["raw","original"],allowedInText:!0},handler:(e,t)=>{var r,{parser:n,funcName:i,token:a}=e,o=assertNodeType(t[0],"raw").string,l=t[1];n.settings.strict&&n.settings.reportNonstrict("htmlExtension","HTML extension is disabled on strict mode");var s={};switch(i){case"\\htmlClass":s.class=o,r={command:"\\htmlClass",class:o};break;case"\\htmlId":s.id=o,r={command:"\\htmlId",id:o};break;case"\\htmlStyle":s.style=o,r={command:"\\htmlStyle",style:o};break;case"\\htmlData":for(var m=o.split(","),h=0;h<m.length;h++){var d=m[h].split("=");if(2!==d.length)throw new ParseError("Error parsing key-value for \\htmlData");s["data-"+d[0].trim()]=d[1].trim()}r={command:"\\htmlData",attributes:s};break;default:throw Error("Unrecognized html command")}return n.settings.isTrusted(r)?{type:"html",mode:n.mode,attributes:s,body:ordargument(l)}:n.formatUnsupportedCmd(i)},htmlBuilder:(e,t)=>{var r=buildExpression$1(e.body,t,!1),n=["enclosing"];e.attributes.class&&n.push(...e.attributes.class.trim().split(/\s+/));var i=ep.makeSpan(n,r,t);for(var a in e.attributes)"class"!==a&&e.attributes.hasOwnProperty(a)&&i.setAttribute(a,e.attributes[a]);return i},mathmlBuilder:(e,t)=>buildExpressionRow(e.body,t)}),defineFunction({type:"htmlmathml",names:["\\html@mathml"],props:{numArgs:2,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"htmlmathml",mode:r.mode,html:ordargument(t[0]),mathml:ordargument(t[1])}},htmlBuilder:(e,t)=>{var r=buildExpression$1(e.html,t,!1);return ep.makeFragment(r)},mathmlBuilder:(e,t)=>buildExpressionRow(e.mathml,t)});var sizeData=function(e){if(/^[-+]? *(\d+(\.\d*)?|\.\d+)$/.test(e))return{number:+e,unit:"bp"};var t=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(e);if(!t)throw new ParseError("Invalid size: '"+e+"' in \\includegraphics");var r={number:+(t[1]+t[2]),unit:t[3]};if(!validUnit(r))throw new ParseError("Invalid unit: '"+r.unit+"' in \\includegraphics.");return r};defineFunction({type:"includegraphics",names:["\\includegraphics"],props:{numArgs:1,numOptionalArgs:1,argTypes:["raw","url"],allowedInText:!1},handler:(e,t,r)=>{var{parser:n}=e,i={number:0,unit:"em"},a={number:.9,unit:"em"},o={number:0,unit:"em"},l="";if(r[0])for(var s=assertNodeType(r[0],"raw").string.split(","),m=0;m<s.length;m++){var h=s[m].split("=");if(2===h.length){var d=h[1].trim();switch(h[0].trim()){case"alt":l=d;break;case"width":i=sizeData(d);break;case"height":a=sizeData(d);break;case"totalheight":o=sizeData(d);break;default:throw new ParseError("Invalid key: '"+h[0]+"' in \\includegraphics.")}}}var c=assertNodeType(t[0],"url").url;return(""===l&&(l=(l=(l=c).replace(/^.*[\\/]/,"")).substring(0,l.lastIndexOf("."))),n.settings.isTrusted({command:"\\includegraphics",url:c}))?{type:"includegraphics",mode:n.mode,alt:l,width:i,height:a,totalheight:o,src:c}:n.formatUnsupportedCmd("\\includegraphics")},htmlBuilder:(e,t)=>{var r=calculateSize(e.height,t),n=0;e.totalheight.number>0&&(n=calculateSize(e.totalheight,t)-r);var i=0;e.width.number>0&&(i=calculateSize(e.width,t));var a={height:makeEm(r+n)};i>0&&(a.width=makeEm(i)),n>0&&(a.verticalAlign=makeEm(-n));var o=new Img(e.src,e.alt,a);return o.height=r,o.depth=n,o},mathmlBuilder:(e,t)=>{var r=new eE.MathNode("mglyph",[]);r.setAttribute("alt",e.alt);var n=calculateSize(e.height,t),i=0;if(e.totalheight.number>0&&(i=calculateSize(e.totalheight,t)-n,r.setAttribute("valign",makeEm(-i))),r.setAttribute("height",makeEm(n+i)),e.width.number>0){var a=calculateSize(e.width,t);r.setAttribute("width",makeEm(a))}return r.setAttribute("src",e.src),r}}),defineFunction({type:"kern",names:["\\kern","\\mkern","\\hskip","\\mskip"],props:{numArgs:1,argTypes:["size"],primitive:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:n}=e,i=assertNodeType(t[0],"size");if(r.settings.strict){var a="m"===n[1],o="mu"===i.value.unit;a?(o||r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" supports only mu units, not "+i.value.unit+" units"),"math"!==r.mode&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" works only in math mode")):o&&r.settings.reportNonstrict("mathVsTextUnits","LaTeX's "+n+" doesn't support mu units")}return{type:"kern",mode:r.mode,dimension:i.value}},htmlBuilder:(e,t)=>ep.makeGlue(e.dimension,t),mathmlBuilder(e,t){var r=calculateSize(e.dimension,t);return new eE.SpaceNode(r)}}),defineFunction({type:"lap",names:["\\mathllap","\\mathrlap","\\mathclap"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r,funcName:n}=e,i=t[0];return{type:"lap",mode:r.mode,alignment:n.slice(5),body:i}},htmlBuilder:(e,t)=>{"clap"===e.alignment?(r=ep.makeSpan([],[buildGroup$1(e.body,t)]),r=ep.makeSpan(["inner"],[r],t)):r=ep.makeSpan(["inner"],[buildGroup$1(e.body,t)]);var r,n=ep.makeSpan(["fix"],[]),i=ep.makeSpan([e.alignment],[r,n],t),a=ep.makeSpan(["strut"]);return a.style.height=makeEm(i.height+i.depth),i.depth&&(a.style.verticalAlign=makeEm(-i.depth)),i.children.unshift(a),i=ep.makeSpan(["thinbox"],[i],t),ep.makeSpan(["mord","vbox"],[i],t)},mathmlBuilder:(e,t)=>{var r=new eE.MathNode("mpadded",[buildGroup(e.body,t)]);if("rlap"!==e.alignment){var n="llap"===e.alignment?"-1":"-0.5";r.setAttribute("lspace",n+"width")}return r.setAttribute("width","0px"),r}}),defineFunction({type:"styling",names:["\\(","$"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){var{funcName:r,parser:n}=e,i=n.mode;n.switchMode("math");var a="\\("===r?"\\)":"$",o=n.parseExpression(!1,a);return n.expect(a),n.switchMode(i),{type:"styling",mode:n.mode,style:"text",body:o}}}),defineFunction({type:"text",names:["\\)","\\]"],props:{numArgs:0,allowedInText:!0,allowedInMath:!1},handler(e,t){throw new ParseError("Mismatched "+e.funcName)}});var chooseMathStyle=(e,t)=>{switch(t.style.size){case g.DISPLAY.size:return e.display;case g.TEXT.size:return e.text;case g.SCRIPT.size:return e.script;case g.SCRIPTSCRIPT.size:return e.scriptscript;default:return e.text}};defineFunction({type:"mathchoice",names:["\\mathchoice"],props:{numArgs:4,primitive:!0},handler:(e,t)=>{var{parser:r}=e;return{type:"mathchoice",mode:r.mode,display:ordargument(t[0]),text:ordargument(t[1]),script:ordargument(t[2]),scriptscript:ordargument(t[3])}},htmlBuilder:(e,t)=>{var r=buildExpression$1(chooseMathStyle(e,t),t,!1);return ep.makeFragment(r)},mathmlBuilder:(e,t)=>buildExpressionRow(chooseMathStyle(e,t),t)});var assembleSupSub=(e,t,r,n,i,a,o)=>{e=ep.makeSpan([],[e]);var l,m,h,d=r&&s.isCharacterBox(r);if(t){var c=buildGroup$1(t,n.havingStyle(i.sup()),n);m={elem:c,kern:Math.max(n.fontMetrics().bigOpSpacing1,n.fontMetrics().bigOpSpacing3-c.depth)}}if(r){var u=buildGroup$1(r,n.havingStyle(i.sub()),n);l={elem:u,kern:Math.max(n.fontMetrics().bigOpSpacing2,n.fontMetrics().bigOpSpacing4-u.height)}}if(m&&l){var p=n.fontMetrics().bigOpSpacing5+l.elem.height+l.elem.depth+l.kern+e.depth+o;h=ep.makeVList({positionType:"bottom",positionData:p,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:l.elem,marginLeft:makeEm(-a)},{type:"kern",size:l.kern},{type:"elem",elem:e},{type:"kern",size:m.kern},{type:"elem",elem:m.elem,marginLeft:makeEm(a)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}else if(l){var f=e.height-o;h=ep.makeVList({positionType:"top",positionData:f,children:[{type:"kern",size:n.fontMetrics().bigOpSpacing5},{type:"elem",elem:l.elem,marginLeft:makeEm(-a)},{type:"kern",size:l.kern},{type:"elem",elem:e}]},n)}else{if(!m)return e;var b=e.depth+o;h=ep.makeVList({positionType:"bottom",positionData:b,children:[{type:"elem",elem:e},{type:"kern",size:m.kern},{type:"elem",elem:m.elem,marginLeft:makeEm(a)},{type:"kern",size:n.fontMetrics().bigOpSpacing5}]},n)}var g=[h];if(l&&0!==a&&!d){var y=ep.makeSpan(["mspace"],[],n);y.style.marginRight=makeEm(a),g.unshift(y)}return ep.makeSpan(["mop","op-limits"],g,n)},e0=["\\smallint"],htmlBuilder$2=(e,t)=>{var r,n,i,a,o=!1;"supsub"===e.type?(r=e.sup,n=e.sub,i=assertNodeType(e.base,"op"),o=!0):i=assertNodeType(e,"op");var l=t.style,m=!1;if(l.size===g.DISPLAY.size&&i.symbol&&!s.contains(e0,i.name)&&(m=!0),i.symbol){var h=m?"Size2-Regular":"Size1-Regular",d="";if(("\\oiint"===i.name||"\\oiiint"===i.name)&&(d=i.name.slice(1),i.name="oiint"===d?"\\iint":"\\iiint"),a=ep.makeSymbol(i.name,h,"math",t,["mop","op-symbol",m?"large-op":"small-op"]),d.length>0){var c=a.italic,u=ep.staticSvg(d+"Size"+(m?"2":"1"),t);a=ep.makeVList({positionType:"individualShift",children:[{type:"elem",elem:a,shift:0},{type:"elem",elem:u,shift:m?.08:0}]},t),i.name="\\"+d,a.classes.unshift("mop"),a.italic=c}}else if(i.body){var p=buildExpression$1(i.body,t,!0);1===p.length&&p[0]instanceof SymbolNode?(a=p[0]).classes[0]="mop":a=ep.makeSpan(["mop"],p,t)}else{for(var f=[],b=1;b<i.name.length;b++)f.push(ep.mathsym(i.name[b],i.mode,t));a=ep.makeSpan(["mop"],f,t)}var y=0,v=0;return((a instanceof SymbolNode||"\\oiint"===i.name||"\\oiiint"===i.name)&&!i.suppressBaseShift&&(y=(a.height-a.depth)/2-t.fontMetrics().axisHeight,v=a.italic),o)?assembleSupSub(a,r,n,t,l,v,y):(y&&(a.style.position="relative",a.style.top=makeEm(y)),a)},mathmlBuilder$1=(e,t)=>{var r;if(e.symbol)r=new MathNode("mo",[makeText(e.name,e.mode)]),s.contains(e0,e.name)&&r.setAttribute("largeop","false");else if(e.body)r=new MathNode("mo",buildExpression(e.body,t));else{r=new MathNode("mi",[new TextNode(e.name.slice(1))]);var n=new MathNode("mo",[makeText("⁡","text")]);r=e.parentIsSupSub?new MathNode("mrow",[r,n]):newDocumentFragment([r,n])}return r},e1={"∏":"\\prod","∐":"\\coprod","∑":"\\sum","⋀":"\\bigwedge","⋁":"\\bigvee","⋂":"\\bigcap","⋃":"\\bigcup","⨀":"\\bigodot","⨁":"\\bigoplus","⨂":"\\bigotimes","⨄":"\\biguplus","⨆":"\\bigsqcup"};defineFunction({type:"op",names:["\\coprod","\\bigvee","\\bigwedge","\\biguplus","\\bigcap","\\bigcup","\\intop","\\prod","\\sum","\\bigotimes","\\bigoplus","\\bigodot","\\bigsqcup","\\smallint","∏","∐","∑","⋀","⋁","⋂","⋃","⨀","⨁","⨂","⨄","⨆"],props:{numArgs:0},handler:(e,t)=>{var{parser:r,funcName:n}=e,i=n;return 1===i.length&&(i=e1[i]),{type:"op",mode:r.mode,limits:!0,parentIsSupSub:!1,symbol:!0,name:i}},htmlBuilder:htmlBuilder$2,mathmlBuilder:mathmlBuilder$1}),defineFunction({type:"op",names:["\\mathop"],props:{numArgs:1,primitive:!0},handler:(e,t)=>{var{parser:r}=e,n=t[0];return{type:"op",mode:r.mode,limits:!1,parentIsSupSub:!1,symbol:!1,body:ordargument(n)}},htmlBuilder:htmlBuilder$2,mathmlBuilder:mathmlBuilder$1});var e4={"∫":"\\int","∬":"\\iint","∭":"\\iiint","∮":"\\oint","∯":"\\oiint","∰":"\\oiiint"};defineFunction({type:"op",names:["\\arcsin","\\arccos","\\arctan","\\arctg","\\arcctg","\\arg","\\ch","\\cos","\\cosec","\\cosh","\\cot","\\cotg","\\coth","\\csc","\\ctg","\\cth","\\deg","\\dim","\\exp","\\hom","\\ker","\\lg","\\ln","\\log","\\sec","\\sin","\\sinh","\\sh","\\tan","\\tanh","\\tg","\\th"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:htmlBuilder$2,mathmlBuilder:mathmlBuilder$1}),defineFunction({type:"op",names:["\\det","\\gcd","\\inf","\\lim","\\max","\\min","\\Pr","\\sup"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e;return{type:"op",mode:t.mode,limits:!0,parentIsSupSub:!1,symbol:!1,name:r}},htmlBuilder:htmlBuilder$2,mathmlBuilder:mathmlBuilder$1}),defineFunction({type:"op",names:["\\int","\\iint","\\iiint","\\oint","\\oiint","\\oiiint","∫","∬","∭","∮","∯","∰"],props:{numArgs:0},handler(e){var{parser:t,funcName:r}=e,n=r;return 1===n.length&&(n=e4[n]),{type:"op",mode:t.mode,limits:!1,parentIsSupSub:!1,symbol:!0,name:n}},htmlBuilder:htmlBuilder$2,mathmlBuilder:mathmlBuilder$1});var htmlBuilder$1=(e,t)=>{var r,n,i,a,o=!1;if("supsub"===e.type?(r=e.sup,n=e.sub,i=assertNodeType(e.base,"operatorname"),o=!0):i=assertNodeType(e,"operatorname"),i.body.length>0){for(var l=buildExpression$1(i.body.map(e=>{var t=e.text;return"string"==typeof t?{type:"textord",mode:e.mode,text:t}:e}),t.withFont("mathrm"),!0),s=0;s<l.length;s++){var m=l[s];m instanceof SymbolNode&&(m.text=m.text.replace(/\u2212/,"-").replace(/\u2217/,"*"))}a=ep.makeSpan(["mop"],l,t)}else a=ep.makeSpan(["mop"],[],t);return o?assembleSupSub(a,r,n,t,t.style,0,0):a};function sizingGroup(e,t,r){for(var n=buildExpression$1(e,t,!1),i=t.sizeMultiplier/r.sizeMultiplier,a=0;a<n.length;a++){var o=n[a].classes.indexOf("sizing");o<0?Array.prototype.push.apply(n[a].classes,t.sizingClasses(r)):n[a].classes[o+1]==="reset-size"+t.size&&(n[a].classes[o+1]="reset-size"+r.size),n[a].height*=i,n[a].depth*=i}return ep.makeFragment(n)}defineFunction({type:"operatorname",names:["\\operatorname@","\\operatornamewithlimits"],props:{numArgs:1},handler:(e,t)=>{var{parser:r,funcName:n}=e,i=t[0];return{type:"operatorname",mode:r.mode,body:ordargument(i),alwaysHandleSupSub:"\\operatornamewithlimits"===n,limits:!1,parentIsSupSub:!1}},htmlBuilder:htmlBuilder$1,mathmlBuilder:(e,t)=>{for(var r=buildExpression(e.body,t.withFont("mathrm")),n=!0,i=0;i<r.length;i++){var a=r[i];if(a instanceof eE.SpaceNode);else if(a instanceof eE.MathNode)switch(a.type){case"mi":case"mn":case"ms":case"mspace":case"mtext":break;case"mo":var o=a.children[0];1===a.children.length&&o instanceof eE.TextNode?o.text=o.text.replace(/\u2212/,"-").replace(/\u2217/,"*"):n=!1;break;default:n=!1}else n=!1}if(n){var l=r.map(e=>e.toText()).join("");r=[new eE.TextNode(l)]}var s=new eE.MathNode("mi",r);s.setAttribute("mathvariant","normal");var m=new eE.MathNode("mo",[makeText("⁡","text")]);return e.parentIsSupSub?new eE.MathNode("mrow",[s,m]):eE.newDocumentFragment([s,m])}}),eZ["\\operatorname"]="\\@ifstar\\operatornamewithlimits\\operatorname@",defineFunctionBuilders({type:"ordgroup",htmlBuilder:(e,t)=>e.semisimple?ep.makeFragment(buildExpression$1(e.body,t,!1)):ep.makeSpan(["mord"],buildExpression$1(e.body,t,!0),t),mathmlBuilder:(e,t)=>buildExpressionRow(e.body,t,!0)}),defineFunction({type:"overline",names:["\\overline"],props:{numArgs:1},handler(e,t){var{parser:r}=e,n=t[0];return{type:"overline",mode:r.mode,body:n}},htmlBuilder(e,t){var r=buildGroup$1(e.body,t.havingCrampedStyle()),n=ep.makeLineSpan("overline-line",t),i=t.fontMetrics().defaultRuleThickness,a=ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r},{type:"kern",size:3*i},{type:"elem",elem:n},{type:"kern",size:i}]},t);return ep.makeSpan(["mord","overline"],[a],t)},mathmlBuilder(e,t){var r=new eE.MathNode("mo",[new eE.TextNode("‾")]);r.setAttribute("stretchy","true");var n=new eE.MathNode("mover",[buildGroup(e.body,t),r]);return n.setAttribute("accent","true"),n}}),defineFunction({type:"phantom",names:["\\phantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,n=t[0];return{type:"phantom",mode:r.mode,body:ordargument(n)}},htmlBuilder:(e,t)=>{var r=buildExpression$1(e.body,t.withPhantom(),!1);return ep.makeFragment(r)},mathmlBuilder:(e,t)=>{var r=buildExpression(e.body,t);return new eE.MathNode("mphantom",r)}}),defineFunction({type:"hphantom",names:["\\hphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,n=t[0];return{type:"hphantom",mode:r.mode,body:n}},htmlBuilder:(e,t)=>{var r=ep.makeSpan([],[buildGroup$1(e.body,t.withPhantom())]);if(r.height=0,r.depth=0,r.children)for(var n=0;n<r.children.length;n++)r.children[n].height=0,r.children[n].depth=0;return r=ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t),ep.makeSpan(["mord"],[r],t)},mathmlBuilder:(e,t)=>{var r=buildExpression(ordargument(e.body),t),n=new eE.MathNode("mphantom",r),i=new eE.MathNode("mpadded",[n]);return i.setAttribute("height","0px"),i.setAttribute("depth","0px"),i}}),defineFunction({type:"vphantom",names:["\\vphantom"],props:{numArgs:1,allowedInText:!0},handler:(e,t)=>{var{parser:r}=e,n=t[0];return{type:"vphantom",mode:r.mode,body:n}},htmlBuilder:(e,t)=>{var r=ep.makeSpan(["inner"],[buildGroup$1(e.body,t.withPhantom())]),n=ep.makeSpan(["fix"],[]);return ep.makeSpan(["mord","rlap"],[r,n],t)},mathmlBuilder:(e,t)=>{var r=buildExpression(ordargument(e.body),t),n=new eE.MathNode("mphantom",r),i=new eE.MathNode("mpadded",[n]);return i.setAttribute("width","0px"),i}}),defineFunction({type:"raisebox",names:["\\raisebox"],props:{numArgs:2,argTypes:["size","hbox"],allowedInText:!0},handler(e,t){var{parser:r}=e,n=assertNodeType(t[0],"size").value,i=t[1];return{type:"raisebox",mode:r.mode,dy:n,body:i}},htmlBuilder(e,t){var r=buildGroup$1(e.body,t),n=calculateSize(e.dy,t);return ep.makeVList({positionType:"shift",positionData:-n,children:[{type:"elem",elem:r}]},t)},mathmlBuilder(e,t){var r=new eE.MathNode("mpadded",[buildGroup(e.body,t)]),n=e.dy.number+e.dy.unit;return r.setAttribute("voffset",n),r}}),defineFunction({type:"internal",names:["\\relax"],props:{numArgs:0,allowedInText:!0,allowedInArgument:!0},handler(e){var{parser:t}=e;return{type:"internal",mode:t.mode}}}),defineFunction({type:"rule",names:["\\rule"],props:{numArgs:2,numOptionalArgs:1,allowedInText:!0,allowedInMath:!0,argTypes:["size","size","size"]},handler(e,t,r){var{parser:n}=e,i=r[0],a=assertNodeType(t[0],"size"),o=assertNodeType(t[1],"size");return{type:"rule",mode:n.mode,shift:i&&assertNodeType(i,"size").value,width:a.value,height:o.value}},htmlBuilder(e,t){var r=ep.makeSpan(["mord","rule"],[],t),n=calculateSize(e.width,t),i=calculateSize(e.height,t),a=e.shift?calculateSize(e.shift,t):0;return r.style.borderRightWidth=makeEm(n),r.style.borderTopWidth=makeEm(i),r.style.bottom=makeEm(a),r.width=n,r.height=i+a,r.depth=-a,r.maxFontSize=1.125*i*t.sizeMultiplier,r},mathmlBuilder(e,t){var r=calculateSize(e.width,t),n=calculateSize(e.height,t),i=e.shift?calculateSize(e.shift,t):0,a=t.color&&t.getColor()||"black",o=new eE.MathNode("mspace");o.setAttribute("mathbackground",a),o.setAttribute("width",makeEm(r)),o.setAttribute("height",makeEm(n));var l=new eE.MathNode("mpadded",[o]);return i>=0?l.setAttribute("height",makeEm(i)):(l.setAttribute("height",makeEm(i)),l.setAttribute("depth",makeEm(-i))),l.setAttribute("voffset",makeEm(i)),l}});var e5=["\\tiny","\\sixptsize","\\scriptsize","\\footnotesize","\\small","\\normalsize","\\large","\\Large","\\LARGE","\\huge","\\Huge"];defineFunction({type:"sizing",names:e5,props:{numArgs:0,allowedInText:!0},handler:(e,t)=>{var{breakOnTokenText:r,funcName:n,parser:i}=e,a=i.parseExpression(!1,r);return{type:"sizing",mode:i.mode,size:e5.indexOf(n)+1,body:a}},htmlBuilder:(e,t)=>{var r=t.havingSize(e.size);return sizingGroup(e.body,r,t)},mathmlBuilder:(e,t)=>{var r=t.havingSize(e.size),n=buildExpression(e.body,r),i=new eE.MathNode("mstyle",n);return i.setAttribute("mathsize",makeEm(r.sizeMultiplier)),i}}),defineFunction({type:"smash",names:["\\smash"],props:{numArgs:1,numOptionalArgs:1,allowedInText:!0},handler:(e,t,r)=>{var{parser:n}=e,i=!1,a=!1,o=r[0]&&assertNodeType(r[0],"ordgroup");if(o)for(var l="",s=0;s<o.body.length;++s)if("t"===(l=o.body[s].text))i=!0;else if("b"===l)a=!0;else{i=!1,a=!1;break}else i=!0,a=!0;var m=t[0];return{type:"smash",mode:n.mode,body:m,smashHeight:i,smashDepth:a}},htmlBuilder:(e,t)=>{var r=ep.makeSpan([],[buildGroup$1(e.body,t)]);if(!e.smashHeight&&!e.smashDepth)return r;if(e.smashHeight&&(r.height=0,r.children))for(var n=0;n<r.children.length;n++)r.children[n].height=0;if(e.smashDepth&&(r.depth=0,r.children))for(var i=0;i<r.children.length;i++)r.children[i].depth=0;var a=ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r}]},t);return ep.makeSpan(["mord"],[a],t)},mathmlBuilder:(e,t)=>{var r=new eE.MathNode("mpadded",[buildGroup(e.body,t)]);return e.smashHeight&&r.setAttribute("height","0px"),e.smashDepth&&r.setAttribute("depth","0px"),r}}),defineFunction({type:"sqrt",names:["\\sqrt"],props:{numArgs:1,numOptionalArgs:1},handler(e,t,r){var{parser:n}=e,i=r[0],a=t[0];return{type:"sqrt",mode:n.mode,body:a,index:i}},htmlBuilder(e,t){var r=buildGroup$1(e.body,t.havingCrampedStyle());0===r.height&&(r.height=t.fontMetrics().xHeight),r=ep.wrapFragment(r,t);var n=t.fontMetrics().defaultRuleThickness,i=n;t.style.id<g.TEXT.id&&(i=t.fontMetrics().xHeight);var a=n+i/4,o=r.height+r.depth+a+n,{span:l,ruleWidth:s,advanceWidth:m}=eW.sqrtImage(o,t),h=l.height-s;h>r.height+r.depth+a&&(a=(a+h-r.height-r.depth)/2);var d=l.height-r.height-a-s;r.style.paddingLeft=makeEm(m);var c=ep.makeVList({positionType:"firstBaseline",children:[{type:"elem",elem:r,wrapperClasses:["svg-align"]},{type:"kern",size:-(r.height+d)},{type:"elem",elem:l},{type:"kern",size:s}]},t);if(!e.index)return ep.makeSpan(["mord","sqrt"],[c],t);var u=t.havingStyle(g.SCRIPTSCRIPT),p=buildGroup$1(e.index,u,t),f=.6*(c.height-c.depth),b=ep.makeVList({positionType:"shift",positionData:-f,children:[{type:"elem",elem:p}]},t),y=ep.makeSpan(["root"],[b]);return ep.makeSpan(["mord","sqrt"],[y,c],t)},mathmlBuilder(e,t){var{body:r,index:n}=e;return n?new eE.MathNode("mroot",[buildGroup(r,t),buildGroup(n,t)]):new eE.MathNode("msqrt",[buildGroup(r,t)])}});var e6={display:g.DISPLAY,text:g.TEXT,script:g.SCRIPT,scriptscript:g.SCRIPTSCRIPT};defineFunction({type:"styling",names:["\\displaystyle","\\textstyle","\\scriptstyle","\\scriptscriptstyle"],props:{numArgs:0,allowedInText:!0,primitive:!0},handler(e,t){var{breakOnTokenText:r,funcName:n,parser:i}=e,a=i.parseExpression(!0,r),o=n.slice(1,n.length-5);return{type:"styling",mode:i.mode,style:o,body:a}},htmlBuilder(e,t){var r=e6[e.style],n=t.havingStyle(r).withFont("");return sizingGroup(e.body,n,t)},mathmlBuilder(e,t){var r=e6[e.style],n=t.havingStyle(r),i=buildExpression(e.body,n),a=new eE.MathNode("mstyle",i),o={display:["0","true"],text:["0","false"],script:["1","false"],scriptscript:["2","false"]}[e.style];return a.setAttribute("scriptlevel",o[0]),a.setAttribute("displaystyle",o[1]),a}});var htmlBuilderDelegate=function(e,t){var r=e.base;return r?"op"===r.type?r.limits&&(t.style.size===g.DISPLAY.size||r.alwaysHandleSupSub)?htmlBuilder$2:null:"operatorname"===r.type?r.alwaysHandleSupSub&&(t.style.size===g.DISPLAY.size||r.limits)?htmlBuilder$1:null:"accent"===r.type?s.isCharacterBox(r.base)?htmlBuilder$a:null:"horizBrace"===r.type?!e.sub===r.isOver?htmlBuilder$3:null:null:null};defineFunctionBuilders({type:"supsub",htmlBuilder(e,t){var r,n,i,a,o=htmlBuilderDelegate(e,t);if(o)return o(e,t);var{base:l,sup:m,sub:h}=e,d=buildGroup$1(l,t),c=t.fontMetrics(),u=0,p=0,f=l&&s.isCharacterBox(l);if(m){var b=t.havingStyle(t.style.sup());r=buildGroup$1(m,b,t),f||(u=d.height-b.fontMetrics().supDrop*b.sizeMultiplier/t.sizeMultiplier)}if(h){var y=t.havingStyle(t.style.sub());n=buildGroup$1(h,y,t),f||(p=d.depth+y.fontMetrics().subDrop*y.sizeMultiplier/t.sizeMultiplier)}i=t.style===g.DISPLAY?c.sup1:t.style.cramped?c.sup3:c.sup2;var v=t.sizeMultiplier,S=makeEm(.5/c.ptPerEm/v),x=null;if(n){var w=e.base&&"op"===e.base.type&&e.base.name&&("\\oiint"===e.base.name||"\\oiiint"===e.base.name);(d instanceof SymbolNode||w)&&(x=makeEm(-d.italic))}if(r&&n){u=Math.max(u,i,r.depth+.25*c.xHeight),p=Math.max(p,c.sub2);var k=4*c.defaultRuleThickness;if(u-r.depth-(n.height-p)<k){p=k-(u-r.depth)+n.height;var M=.8*c.xHeight-(u-r.depth);M>0&&(u+=M,p-=M)}var z=[{type:"elem",elem:n,shift:p,marginRight:S,marginLeft:x},{type:"elem",elem:r,shift:-u,marginRight:S}];a=ep.makeVList({positionType:"individualShift",children:z},t)}else if(n){p=Math.max(p,c.sub1,n.height-.8*c.xHeight);var T=[{type:"elem",elem:n,marginLeft:x,marginRight:S}];a=ep.makeVList({positionType:"shift",positionData:p,children:T},t)}else if(r)u=Math.max(u,i,r.depth+.25*c.xHeight),a=ep.makeVList({positionType:"shift",positionData:-u,children:[{type:"elem",elem:r,marginRight:S}]},t);else throw Error("supsub must have either sup or sub.");var A=getTypeOfDomTree(d,"right")||"mord";return ep.makeSpan([A],[d,ep.makeSpan(["msupsub"],[a])],t)},mathmlBuilder(e,t){var r,n,i=!1;e.base&&"horizBrace"===e.base.type&&!!e.sup===e.base.isOver&&(i=!0,r=e.base.isOver),e.base&&("op"===e.base.type||"operatorname"===e.base.type)&&(e.base.parentIsSupSub=!0);var a=[buildGroup(e.base,t)];if(e.sub&&a.push(buildGroup(e.sub,t)),e.sup&&a.push(buildGroup(e.sup,t)),i)n=r?"mover":"munder";else if(e.sub){if(e.sup){var o=e.base;n=o&&"op"===o.type&&o.limits&&t.style===g.DISPLAY?"munderover":o&&"operatorname"===o.type&&o.alwaysHandleSupSub&&(t.style===g.DISPLAY||o.limits)?"munderover":"msubsup"}else{var l=e.base;n=l&&"op"===l.type&&l.limits&&(t.style===g.DISPLAY||l.alwaysHandleSupSub)?"munder":l&&"operatorname"===l.type&&l.alwaysHandleSupSub&&(l.limits||t.style===g.DISPLAY)?"munder":"msub"}}else{var s=e.base;n=s&&"op"===s.type&&s.limits&&(t.style===g.DISPLAY||s.alwaysHandleSupSub)?"mover":s&&"operatorname"===s.type&&s.alwaysHandleSupSub&&(s.limits||t.style===g.DISPLAY)?"mover":"msup"}return new eE.MathNode(n,a)}}),defineFunctionBuilders({type:"atom",htmlBuilder:(e,t)=>ep.mathsym(e.text,e.mode,t,["m"+e.family]),mathmlBuilder(e,t){var r=new eE.MathNode("mo",[makeText(e.text,e.mode)]);if("bin"===e.family){var n=getVariant(e,t);"bold-italic"===n&&r.setAttribute("mathvariant",n)}else"punct"===e.family?r.setAttribute("separator","true"):("open"===e.family||"close"===e.family)&&r.setAttribute("stretchy","false");return r}});var e7={mi:"italic",mn:"normal",mtext:"normal"};defineFunctionBuilders({type:"mathord",htmlBuilder:(e,t)=>ep.makeOrd(e,t,"mathord"),mathmlBuilder(e,t){var r=new eE.MathNode("mi",[makeText(e.text,e.mode,t)]),n=getVariant(e,t)||"italic";return n!==e7[r.type]&&r.setAttribute("mathvariant",n),r}}),defineFunctionBuilders({type:"textord",htmlBuilder:(e,t)=>ep.makeOrd(e,t,"textord"),mathmlBuilder(e,t){var r,n=makeText(e.text,e.mode,t),i=getVariant(e,t)||"normal";return i!==e7[(r="text"===e.mode?new eE.MathNode("mtext",[n]):/[0-9]/.test(e.text)?new eE.MathNode("mn",[n]):"\\prime"===e.text?new eE.MathNode("mo",[n]):new eE.MathNode("mi",[n])).type]&&r.setAttribute("mathvariant",i),r}});var e3={"\\nobreak":"nobreak","\\allowbreak":"allowbreak"},e8={" ":{},"\\ ":{},"~":{className:"nobreak"},"\\space":{},"\\nobreakspace":{className:"nobreak"}};defineFunctionBuilders({type:"spacing",htmlBuilder(e,t){if(e8.hasOwnProperty(e.text)){var r=e8[e.text].className||"";if("text"!==e.mode)return ep.makeSpan(["mspace",r],[ep.mathsym(e.text,e.mode,t)],t);var n=ep.makeOrd(e,t,"textord");return n.classes.push(r),n}if(e3.hasOwnProperty(e.text))return ep.makeSpan(["mspace",e3[e.text]],[],t);throw new ParseError('Unknown type of space "'+e.text+'"')},mathmlBuilder(e,t){var r;if(e8.hasOwnProperty(e.text))r=new eE.MathNode("mtext",[new eE.TextNode("\xa0")]);else if(e3.hasOwnProperty(e.text))return new eE.MathNode("mspace");else throw new ParseError('Unknown type of space "'+e.text+'"');return r}});var pad=()=>{var e=new eE.MathNode("mtd",[]);return e.setAttribute("width","50%"),e};defineFunctionBuilders({type:"tag",mathmlBuilder(e,t){var r=new eE.MathNode("mtable",[new eE.MathNode("mtr",[pad(),new eE.MathNode("mtd",[buildExpressionRow(e.body,t)]),pad(),new eE.MathNode("mtd",[buildExpressionRow(e.tag,t)])])]);return r.setAttribute("width","100%"),r}});var e2={"\\text":void 0,"\\textrm":"textrm","\\textsf":"textsf","\\texttt":"texttt","\\textnormal":"textrm"},e9={"\\textbf":"textbf","\\textmd":"textmd"},te={"\\textit":"textit","\\textup":"textup"},optionsWithFont=(e,t)=>{var r=e.font;return r?e2[r]?t.withTextFontFamily(e2[r]):e9[r]?t.withTextFontWeight(e9[r]):"\\emph"===r?"textit"===t.fontShape?t.withTextFontShape("textup"):t.withTextFontShape("textit"):t.withTextFontShape(te[r]):t};defineFunction({type:"text",names:["\\text","\\textrm","\\textsf","\\texttt","\\textnormal","\\textbf","\\textmd","\\textit","\\textup","\\emph"],props:{numArgs:1,argTypes:["text"],allowedInArgument:!0,allowedInText:!0},handler(e,t){var{parser:r,funcName:n}=e,i=t[0];return{type:"text",mode:r.mode,body:ordargument(i),font:n}},htmlBuilder(e,t){var r=optionsWithFont(e,t),n=buildExpression$1(e.body,r,!0);return ep.makeSpan(["mord","text"],n,r)},mathmlBuilder(e,t){var r=optionsWithFont(e,t);return buildExpressionRow(e.body,r)}}),defineFunction({type:"underline",names:["\\underline"],props:{numArgs:1,allowedInText:!0},handler(e,t){var{parser:r}=e;return{type:"underline",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=buildGroup$1(e.body,t),n=ep.makeLineSpan("underline-line",t),i=t.fontMetrics().defaultRuleThickness,a=ep.makeVList({positionType:"top",positionData:r.height,children:[{type:"kern",size:i},{type:"elem",elem:n},{type:"kern",size:3*i},{type:"elem",elem:r}]},t);return ep.makeSpan(["mord","underline"],[a],t)},mathmlBuilder(e,t){var r=new eE.MathNode("mo",[new eE.TextNode("‾")]);r.setAttribute("stretchy","true");var n=new eE.MathNode("munder",[buildGroup(e.body,t),r]);return n.setAttribute("accentunder","true"),n}}),defineFunction({type:"vcenter",names:["\\vcenter"],props:{numArgs:1,argTypes:["original"],allowedInText:!1},handler(e,t){var{parser:r}=e;return{type:"vcenter",mode:r.mode,body:t[0]}},htmlBuilder(e,t){var r=buildGroup$1(e.body,t),n=t.fontMetrics().axisHeight,i=.5*(r.height-n-(r.depth+n));return ep.makeVList({positionType:"shift",positionData:i,children:[{type:"elem",elem:r}]},t)},mathmlBuilder:(e,t)=>new eE.MathNode("mpadded",[buildGroup(e.body,t)],["vcenter"])}),defineFunction({type:"verb",names:["\\verb"],props:{numArgs:0,allowedInText:!0},handler(e,t,r){throw new ParseError("\\verb ended by end of line instead of matching delimiter")},htmlBuilder(e,t){for(var r=makeVerb(e),n=[],i=t.havingStyle(t.style.text()),a=0;a<r.length;a++){var o=r[a];"~"===o&&(o="\\textasciitilde"),n.push(ep.makeSymbol(o,"Typewriter-Regular",e.mode,i,["mord","texttt"]))}return ep.makeSpan(["mord","text"].concat(i.sizingClasses(t)),ep.tryCombineChars(n),i)},mathmlBuilder(e,t){var r=new eE.TextNode(makeVerb(e)),n=new eE.MathNode("mtext",[r]);return n.setAttribute("mathvariant","monospace"),n}});var makeVerb=e=>e.body.replace(/ /g,e.star?"␣":"\xa0"),tt="[ \r\n	]",tr="[̀-ͯ]",tn=RegExp(tr+"+$"),ti="("+tt+"+)|\\\\(\n|[ \r	]+\n?)[ \r	]*|([!-\\[\\]-‧‪-퟿豈-￿]"+tr+"*|[\uD800-\uDBFF][\uDC00-\uDFFF]"+tr+"*|\\\\verb\\*([^]).*?\\4|\\\\verb([^*a-zA-Z]).*?\\5|(\\\\[a-zA-Z@]+)"+tt+"*|\\\\[^\uD800-\uDFFF])";let Lexer=class Lexer{constructor(e,t){this.input=void 0,this.settings=void 0,this.tokenRegex=void 0,this.catcodes=void 0,this.input=e,this.settings=t,this.tokenRegex=RegExp(ti,"g"),this.catcodes={"%":14,"~":13}}setCatcode(e,t){this.catcodes[e]=t}lex(){var e=this.input,t=this.tokenRegex.lastIndex;if(t===e.length)return new Token("EOF",new SourceLocation(this,t,t));var r=this.tokenRegex.exec(e);if(null===r||r.index!==t)throw new ParseError("Unexpected character: '"+e[t]+"'",new Token(e[t],new SourceLocation(this,t,t+1)));var n=r[6]||r[3]||(r[2]?"\\ ":" ");if(14===this.catcodes[n]){var i=e.indexOf("\n",this.tokenRegex.lastIndex);return -1===i?(this.tokenRegex.lastIndex=e.length,this.settings.reportNonstrict("commentAtEnd","% comment has no terminating newline; LaTeX would fail because of commenting the end of math mode (e.g. $)")):this.tokenRegex.lastIndex=i+1,this.lex()}return new Token(n,new SourceLocation(this,t,this.tokenRegex.lastIndex))}};let Namespace=class Namespace{constructor(e,t){void 0===e&&(e={}),void 0===t&&(t={}),this.current=void 0,this.builtins=void 0,this.undefStack=void 0,this.current=t,this.builtins=e,this.undefStack=[]}beginGroup(){this.undefStack.push({})}endGroup(){if(0===this.undefStack.length)throw new ParseError("Unbalanced namespace destruction: attempt to pop global namespace; please report this as a bug");var e=this.undefStack.pop();for(var t in e)e.hasOwnProperty(t)&&(null==e[t]?delete this.current[t]:this.current[t]=e[t])}endGroups(){for(;this.undefStack.length>0;)this.endGroup()}has(e){return this.current.hasOwnProperty(e)||this.builtins.hasOwnProperty(e)}get(e){return this.current.hasOwnProperty(e)?this.current[e]:this.builtins[e]}set(e,t,r){if(void 0===r&&(r=!1),r){for(var n=0;n<this.undefStack.length;n++)delete this.undefStack[n][e];this.undefStack.length>0&&(this.undefStack[this.undefStack.length-1][e]=t)}else{var i=this.undefStack[this.undefStack.length-1];i&&!i.hasOwnProperty(e)&&(i[e]=this.current[e])}null==t?delete this.current[e]:this.current[e]=t}};eZ["\\noexpand"]=function(e){var t=e.popToken();return e.isExpandable(t.text)&&(t.noexpand=!0,t.treatAsRelax=!0),{tokens:[t],numArgs:0}},eZ["\\expandafter"]=function(e){var t=e.popToken();return e.expandOnce(!0),{tokens:[t],numArgs:0}},eZ["\\@firstoftwo"]=function(e){return{tokens:e.consumeArgs(2)[0],numArgs:0}},eZ["\\@secondoftwo"]=function(e){return{tokens:e.consumeArgs(2)[1],numArgs:0}},eZ["\\@ifnextchar"]=function(e){var t=e.consumeArgs(3);e.consumeSpaces();var r=e.future();return 1===t[0].length&&t[0][0].text===r.text?{tokens:t[1],numArgs:0}:{tokens:t[2],numArgs:0}},eZ["\\@ifstar"]="\\@ifnextchar *{\\@firstoftwo{#1}}",eZ["\\TextOrMath"]=function(e){var t=e.consumeArgs(2);return"text"===e.mode?{tokens:t[0],numArgs:0}:{tokens:t[1],numArgs:0}};var ta={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,a:10,A:10,b:11,B:11,c:12,C:12,d:13,D:13,e:14,E:14,f:15,F:15};eZ["\\char"]=function(e){var t,r,n=e.popToken(),i="";if("'"===n.text)t=8,n=e.popToken();else if('"'===n.text)t=16,n=e.popToken();else if("`"===n.text){if("\\"===(n=e.popToken()).text[0])i=n.text.charCodeAt(1);else if("EOF"===n.text)throw new ParseError("\\char` missing argument");else i=n.text.charCodeAt(0)}else t=10;if(t){if(null==(i=ta[n.text])||i>=t)throw new ParseError("Invalid base-"+t+" digit "+n.text);for(;null!=(r=ta[e.future().text])&&r<t;)i*=t,i+=r,e.popToken()}return"\\@char{"+i+"}"};var newcommand=(e,t,r,n)=>{var i=e.consumeArg().tokens;if(1!==i.length)throw new ParseError("\\newcommand's first argument must be a macro name");var a=i[0].text,o=e.isDefined(a);if(o&&!t)throw new ParseError("\\newcommand{"+a+"} attempting to redefine "+a+"; use \\renewcommand");if(!o&&!r)throw new ParseError("\\renewcommand{"+a+"} when command "+a+" does not yet exist; use \\newcommand");var l=0;if(1===(i=e.consumeArg().tokens).length&&"["===i[0].text){for(var s="",m=e.expandNextToken();"]"!==m.text&&"EOF"!==m.text;)s+=m.text,m=e.expandNextToken();if(!s.match(/^\s*[0-9]+\s*$/))throw new ParseError("Invalid number of arguments: "+s);l=parseInt(s),i=e.consumeArg().tokens}return o&&n||e.macros.set(a,{tokens:i,numArgs:l}),""};eZ["\\newcommand"]=e=>newcommand(e,!1,!0,!1),eZ["\\renewcommand"]=e=>newcommand(e,!0,!1,!1),eZ["\\providecommand"]=e=>newcommand(e,!0,!0,!0),eZ["\\message"]=e=>(console.log(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),eZ["\\errmessage"]=e=>(console.error(e.consumeArgs(1)[0].reverse().map(e=>e.text).join("")),""),eZ["\\show"]=e=>{var t=e.popToken(),r=t.text;return console.log(t,e.macros.get(r),eS[r],P.math[r],P.text[r]),""},eZ["\\bgroup"]="{",eZ["\\egroup"]="}",eZ["~"]="\\nobreakspace",eZ["\\lq"]="`",eZ["\\rq"]="'",eZ["\\aa"]="\\r a",eZ["\\AA"]="\\r A",eZ["\\textcopyright"]="\\html@mathml{\\textcircled{c}}{\\char`\xa9}",eZ["\\copyright"]="\\TextOrMath{\\textcopyright}{\\text{\\textcopyright}}",eZ["\\textregistered"]="\\html@mathml{\\textcircled{\\scriptsize R}}{\\char`\xae}",eZ["ℬ"]="\\mathscr{B}",eZ["ℰ"]="\\mathscr{E}",eZ["ℱ"]="\\mathscr{F}",eZ["ℋ"]="\\mathscr{H}",eZ["ℐ"]="\\mathscr{I}",eZ["ℒ"]="\\mathscr{L}",eZ["ℳ"]="\\mathscr{M}",eZ["ℛ"]="\\mathscr{R}",eZ["ℭ"]="\\mathfrak{C}",eZ["ℌ"]="\\mathfrak{H}",eZ["ℨ"]="\\mathfrak{Z}",eZ["\\Bbbk"]="\\Bbb{k}",eZ["\xb7"]="\\cdotp",eZ["\\llap"]="\\mathllap{\\textrm{#1}}",eZ["\\rlap"]="\\mathrlap{\\textrm{#1}}",eZ["\\clap"]="\\mathclap{\\textrm{#1}}",eZ["\\mathstrut"]="\\vphantom{(}",eZ["\\underbar"]="\\underline{\\text{#1}}",eZ["\\not"]='\\html@mathml{\\mathrel{\\mathrlap\\@not}}{\\char"338}',eZ["\\neq"]="\\html@mathml{\\mathrel{\\not=}}{\\mathrel{\\char`≠}}",eZ["\\ne"]="\\neq",eZ["≠"]="\\neq",eZ["\\notin"]="\\html@mathml{\\mathrel{{\\in}\\mathllap{/\\mskip1mu}}}{\\mathrel{\\char`∉}}",eZ["∉"]="\\notin",eZ["≘"]="\\html@mathml{\\mathrel{=\\kern{-1em}\\raisebox{0.4em}{$\\scriptsize\\frown$}}}{\\mathrel{\\char`≘}}",eZ["≙"]="\\html@mathml{\\stackrel{\\tiny\\wedge}{=}}{\\mathrel{\\char`≘}}",eZ["≚"]="\\html@mathml{\\stackrel{\\tiny\\vee}{=}}{\\mathrel{\\char`≚}}",eZ["≛"]="\\html@mathml{\\stackrel{\\scriptsize\\star}{=}}{\\mathrel{\\char`≛}}",eZ["≝"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{def}}{=}}{\\mathrel{\\char`≝}}",eZ["≞"]="\\html@mathml{\\stackrel{\\tiny\\mathrm{m}}{=}}{\\mathrel{\\char`≞}}",eZ["≟"]="\\html@mathml{\\stackrel{\\tiny?}{=}}{\\mathrel{\\char`≟}}",eZ["⟂"]="\\perp",eZ["‼"]="\\mathclose{!\\mkern-0.8mu!}",eZ["∌"]="\\notni",eZ["⌜"]="\\ulcorner",eZ["⌝"]="\\urcorner",eZ["⌞"]="\\llcorner",eZ["⌟"]="\\lrcorner",eZ["\xa9"]="\\copyright",eZ["\xae"]="\\textregistered",eZ["️"]="\\textregistered",eZ["\\ulcorner"]='\\html@mathml{\\@ulcorner}{\\mathop{\\char"231c}}',eZ["\\urcorner"]='\\html@mathml{\\@urcorner}{\\mathop{\\char"231d}}',eZ["\\llcorner"]='\\html@mathml{\\@llcorner}{\\mathop{\\char"231e}}',eZ["\\lrcorner"]='\\html@mathml{\\@lrcorner}{\\mathop{\\char"231f}}',eZ["\\vdots"]="{\\varvdots\\rule{0pt}{15pt}}",eZ["⋮"]="\\vdots",eZ["\\varGamma"]="\\mathit{\\Gamma}",eZ["\\varDelta"]="\\mathit{\\Delta}",eZ["\\varTheta"]="\\mathit{\\Theta}",eZ["\\varLambda"]="\\mathit{\\Lambda}",eZ["\\varXi"]="\\mathit{\\Xi}",eZ["\\varPi"]="\\mathit{\\Pi}",eZ["\\varSigma"]="\\mathit{\\Sigma}",eZ["\\varUpsilon"]="\\mathit{\\Upsilon}",eZ["\\varPhi"]="\\mathit{\\Phi}",eZ["\\varPsi"]="\\mathit{\\Psi}",eZ["\\varOmega"]="\\mathit{\\Omega}",eZ["\\substack"]="\\begin{subarray}{c}#1\\end{subarray}",eZ["\\colon"]="\\nobreak\\mskip2mu\\mathpunct{}\\mathchoice{\\mkern-3mu}{\\mkern-3mu}{}{}{:}\\mskip6mu\\relax",eZ["\\boxed"]="\\fbox{$\\displaystyle{#1}$}",eZ["\\iff"]="\\DOTSB\\;\\Longleftrightarrow\\;",eZ["\\implies"]="\\DOTSB\\;\\Longrightarrow\\;",eZ["\\impliedby"]="\\DOTSB\\;\\Longleftarrow\\;",eZ["\\dddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ...}}{#1}}",eZ["\\ddddot"]="{\\overset{\\raisebox{-0.1ex}{\\normalsize ....}}{#1}}";var to={",":"\\dotsc","\\not":"\\dotsb","+":"\\dotsb","=":"\\dotsb","<":"\\dotsb",">":"\\dotsb","-":"\\dotsb","*":"\\dotsb",":":"\\dotsb","\\DOTSB":"\\dotsb","\\coprod":"\\dotsb","\\bigvee":"\\dotsb","\\bigwedge":"\\dotsb","\\biguplus":"\\dotsb","\\bigcap":"\\dotsb","\\bigcup":"\\dotsb","\\prod":"\\dotsb","\\sum":"\\dotsb","\\bigotimes":"\\dotsb","\\bigoplus":"\\dotsb","\\bigodot":"\\dotsb","\\bigsqcup":"\\dotsb","\\And":"\\dotsb","\\longrightarrow":"\\dotsb","\\Longrightarrow":"\\dotsb","\\longleftarrow":"\\dotsb","\\Longleftarrow":"\\dotsb","\\longleftrightarrow":"\\dotsb","\\Longleftrightarrow":"\\dotsb","\\mapsto":"\\dotsb","\\longmapsto":"\\dotsb","\\hookrightarrow":"\\dotsb","\\doteq":"\\dotsb","\\mathbin":"\\dotsb","\\mathrel":"\\dotsb","\\relbar":"\\dotsb","\\Relbar":"\\dotsb","\\xrightarrow":"\\dotsb","\\xleftarrow":"\\dotsb","\\DOTSI":"\\dotsi","\\int":"\\dotsi","\\oint":"\\dotsi","\\iint":"\\dotsi","\\iiint":"\\dotsi","\\iiiint":"\\dotsi","\\idotsint":"\\dotsi","\\DOTSX":"\\dotsx"};eZ["\\dots"]=function(e){var t="\\dotso",r=e.expandAfterFuture().text;return r in to?t=to[r]:"\\not"===r.slice(0,4)?t="\\dotsb":r in P.math&&s.contains(["bin","rel"],P.math[r].group)&&(t="\\dotsb"),t};var tl={")":!0,"]":!0,"\\rbrack":!0,"\\}":!0,"\\rbrace":!0,"\\rangle":!0,"\\rceil":!0,"\\rfloor":!0,"\\rgroup":!0,"\\rmoustache":!0,"\\right":!0,"\\bigr":!0,"\\biggr":!0,"\\Bigr":!0,"\\Biggr":!0,$:!0,";":!0,".":!0,",":!0};eZ["\\dotso"]=function(e){return e.future().text in tl?"\\ldots\\,":"\\ldots"},eZ["\\dotsc"]=function(e){var t=e.future().text;return t in tl&&","!==t?"\\ldots\\,":"\\ldots"},eZ["\\cdots"]=function(e){return e.future().text in tl?"\\@cdots\\,":"\\@cdots"},eZ["\\dotsb"]="\\cdots",eZ["\\dotsm"]="\\cdots",eZ["\\dotsi"]="\\!\\cdots",eZ["\\dotsx"]="\\ldots\\,",eZ["\\DOTSI"]="\\relax",eZ["\\DOTSB"]="\\relax",eZ["\\DOTSX"]="\\relax",eZ["\\tmspace"]="\\TextOrMath{\\kern#1#3}{\\mskip#1#2}\\relax",eZ["\\,"]="\\tmspace+{3mu}{.1667em}",eZ["\\thinspace"]="\\,",eZ["\\>"]="\\mskip{4mu}",eZ["\\:"]="\\tmspace+{4mu}{.2222em}",eZ["\\medspace"]="\\:",eZ["\\;"]="\\tmspace+{5mu}{.2777em}",eZ["\\thickspace"]="\\;",eZ["\\!"]="\\tmspace-{3mu}{.1667em}",eZ["\\negthinspace"]="\\!",eZ["\\negmedspace"]="\\tmspace-{4mu}{.2222em}",eZ["\\negthickspace"]="\\tmspace-{5mu}{.277em}",eZ["\\enspace"]="\\kern.5em ",eZ["\\enskip"]="\\hskip.5em\\relax",eZ["\\quad"]="\\hskip1em\\relax",eZ["\\qquad"]="\\hskip2em\\relax",eZ["\\tag"]="\\@ifstar\\tag@literal\\tag@paren",eZ["\\tag@paren"]="\\tag@literal{({#1})}",eZ["\\tag@literal"]=e=>{if(e.macros.get("\\df@tag"))throw new ParseError("Multiple \\tag");return"\\gdef\\df@tag{\\text{#1}}"},eZ["\\bmod"]="\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}\\mathbin{\\rm mod}\\mathchoice{\\mskip1mu}{\\mskip1mu}{\\mskip5mu}{\\mskip5mu}",eZ["\\pod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern8mu}{\\mkern8mu}{\\mkern8mu}(#1)",eZ["\\pmod"]="\\pod{{\\rm mod}\\mkern6mu#1}",eZ["\\mod"]="\\allowbreak\\mathchoice{\\mkern18mu}{\\mkern12mu}{\\mkern12mu}{\\mkern12mu}{\\rm mod}\\,\\,#1",eZ["\\newline"]="\\\\\\relax",eZ["\\TeX"]="\\textrm{\\html@mathml{T\\kern-.1667em\\raisebox{-.5ex}{E}\\kern-.125emX}{TeX}}";var ts=makeEm(x["Main-Regular"][84][1]-.7*x["Main-Regular"][65][1]);eZ["\\LaTeX"]="\\textrm{\\html@mathml{L\\kern-.36em\\raisebox{"+ts+"}{\\scriptstyle A}\\kern-.15em\\TeX}{LaTeX}}",eZ["\\KaTeX"]="\\textrm{\\html@mathml{K\\kern-.17em\\raisebox{"+ts+"}{\\scriptstyle A}\\kern-.15em\\TeX}{KaTeX}}",eZ["\\hspace"]="\\@ifstar\\@hspacer\\@hspace",eZ["\\@hspace"]="\\hskip #1\\relax",eZ["\\@hspacer"]="\\rule{0pt}{0pt}\\hskip #1\\relax",eZ["\\ordinarycolon"]=":",eZ["\\vcentcolon"]="\\mathrel{\\mathop\\ordinarycolon}",eZ["\\dblcolon"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-.9mu}\\vcentcolon}}{\\mathop{\\char"2237}}',eZ["\\coloneqq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2254}}',eZ["\\Coloneqq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}=}}{\\mathop{\\char"2237\\char"3d}}',eZ["\\coloneq"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"3a\\char"2212}}',eZ["\\Coloneq"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\mathrel{-}}}{\\mathop{\\char"2237\\char"2212}}',eZ["\\eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2255}}',eZ["\\Eqqcolon"]='\\html@mathml{\\mathrel{=\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"3d\\char"2237}}',eZ["\\eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\vcentcolon}}{\\mathop{\\char"2239}}',eZ["\\Eqcolon"]='\\html@mathml{\\mathrel{\\mathrel{-}\\mathrel{\\mkern-1.2mu}\\dblcolon}}{\\mathop{\\char"2212\\char"2237}}',eZ["\\colonapprox"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"3a\\char"2248}}',eZ["\\Colonapprox"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\approx}}{\\mathop{\\char"2237\\char"2248}}',eZ["\\colonsim"]='\\html@mathml{\\mathrel{\\vcentcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"3a\\char"223c}}',eZ["\\Colonsim"]='\\html@mathml{\\mathrel{\\dblcolon\\mathrel{\\mkern-1.2mu}\\sim}}{\\mathop{\\char"2237\\char"223c}}',eZ["∷"]="\\dblcolon",eZ["∹"]="\\eqcolon",eZ["≔"]="\\coloneqq",eZ["≕"]="\\eqqcolon",eZ["⩴"]="\\Coloneqq",eZ["\\ratio"]="\\vcentcolon",eZ["\\coloncolon"]="\\dblcolon",eZ["\\colonequals"]="\\coloneqq",eZ["\\coloncolonequals"]="\\Coloneqq",eZ["\\equalscolon"]="\\eqqcolon",eZ["\\equalscoloncolon"]="\\Eqqcolon",eZ["\\colonminus"]="\\coloneq",eZ["\\coloncolonminus"]="\\Coloneq",eZ["\\minuscolon"]="\\eqcolon",eZ["\\minuscoloncolon"]="\\Eqcolon",eZ["\\coloncolonapprox"]="\\Colonapprox",eZ["\\coloncolonsim"]="\\Colonsim",eZ["\\simcolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\vcentcolon}",eZ["\\simcoloncolon"]="\\mathrel{\\sim\\mathrel{\\mkern-1.2mu}\\dblcolon}",eZ["\\approxcolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\vcentcolon}",eZ["\\approxcoloncolon"]="\\mathrel{\\approx\\mathrel{\\mkern-1.2mu}\\dblcolon}",eZ["\\notni"]="\\html@mathml{\\not\\ni}{\\mathrel{\\char`∌}}",eZ["\\limsup"]="\\DOTSB\\operatorname*{lim\\,sup}",eZ["\\liminf"]="\\DOTSB\\operatorname*{lim\\,inf}",eZ["\\injlim"]="\\DOTSB\\operatorname*{inj\\,lim}",eZ["\\projlim"]="\\DOTSB\\operatorname*{proj\\,lim}",eZ["\\varlimsup"]="\\DOTSB\\operatorname*{\\overline{lim}}",eZ["\\varliminf"]="\\DOTSB\\operatorname*{\\underline{lim}}",eZ["\\varinjlim"]="\\DOTSB\\operatorname*{\\underrightarrow{lim}}",eZ["\\varprojlim"]="\\DOTSB\\operatorname*{\\underleftarrow{lim}}",eZ["\\gvertneqq"]="\\html@mathml{\\@gvertneqq}{≩}",eZ["\\lvertneqq"]="\\html@mathml{\\@lvertneqq}{≨}",eZ["\\ngeqq"]="\\html@mathml{\\@ngeqq}{≱}",eZ["\\ngeqslant"]="\\html@mathml{\\@ngeqslant}{≱}",eZ["\\nleqq"]="\\html@mathml{\\@nleqq}{≰}",eZ["\\nleqslant"]="\\html@mathml{\\@nleqslant}{≰}",eZ["\\nshortmid"]="\\html@mathml{\\@nshortmid}{∤}",eZ["\\nshortparallel"]="\\html@mathml{\\@nshortparallel}{∦}",eZ["\\nsubseteqq"]="\\html@mathml{\\@nsubseteqq}{⊈}",eZ["\\nsupseteqq"]="\\html@mathml{\\@nsupseteqq}{⊉}",eZ["\\varsubsetneq"]="\\html@mathml{\\@varsubsetneq}{⊊}",eZ["\\varsubsetneqq"]="\\html@mathml{\\@varsubsetneqq}{⫋}",eZ["\\varsupsetneq"]="\\html@mathml{\\@varsupsetneq}{⊋}",eZ["\\varsupsetneqq"]="\\html@mathml{\\@varsupsetneqq}{⫌}",eZ["\\imath"]="\\html@mathml{\\@imath}{ı}",eZ["\\jmath"]="\\html@mathml{\\@jmath}{ȷ}",eZ["\\llbracket"]="\\html@mathml{\\mathopen{[\\mkern-3.2mu[}}{\\mathopen{\\char`⟦}}",eZ["\\rrbracket"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu]}}{\\mathclose{\\char`⟧}}",eZ["⟦"]="\\llbracket",eZ["⟧"]="\\rrbracket",eZ["\\lBrace"]="\\html@mathml{\\mathopen{\\{\\mkern-3.2mu[}}{\\mathopen{\\char`⦃}}",eZ["\\rBrace"]="\\html@mathml{\\mathclose{]\\mkern-3.2mu\\}}}{\\mathclose{\\char`⦄}}",eZ["⦃"]="\\lBrace",eZ["⦄"]="\\rBrace",eZ["\\minuso"]="\\mathbin{\\html@mathml{{\\mathrlap{\\mathchoice{\\kern{0.145em}}{\\kern{0.145em}}{\\kern{0.1015em}}{\\kern{0.0725em}}\\circ}{-}}}{\\char`⦵}}",eZ["⦵"]="\\minuso",eZ["\\darr"]="\\downarrow",eZ["\\dArr"]="\\Downarrow",eZ["\\Darr"]="\\Downarrow",eZ["\\lang"]="\\langle",eZ["\\rang"]="\\rangle",eZ["\\uarr"]="\\uparrow",eZ["\\uArr"]="\\Uparrow",eZ["\\Uarr"]="\\Uparrow",eZ["\\N"]="\\mathbb{N}",eZ["\\R"]="\\mathbb{R}",eZ["\\Z"]="\\mathbb{Z}",eZ["\\alef"]="\\aleph",eZ["\\alefsym"]="\\aleph",eZ["\\Alpha"]="\\mathrm{A}",eZ["\\Beta"]="\\mathrm{B}",eZ["\\bull"]="\\bullet",eZ["\\Chi"]="\\mathrm{X}",eZ["\\clubs"]="\\clubsuit",eZ["\\cnums"]="\\mathbb{C}",eZ["\\Complex"]="\\mathbb{C}",eZ["\\Dagger"]="\\ddagger",eZ["\\diamonds"]="\\diamondsuit",eZ["\\empty"]="\\emptyset",eZ["\\Epsilon"]="\\mathrm{E}",eZ["\\Eta"]="\\mathrm{H}",eZ["\\exist"]="\\exists",eZ["\\harr"]="\\leftrightarrow",eZ["\\hArr"]="\\Leftrightarrow",eZ["\\Harr"]="\\Leftrightarrow",eZ["\\hearts"]="\\heartsuit",eZ["\\image"]="\\Im",eZ["\\infin"]="\\infty",eZ["\\Iota"]="\\mathrm{I}",eZ["\\isin"]="\\in",eZ["\\Kappa"]="\\mathrm{K}",eZ["\\larr"]="\\leftarrow",eZ["\\lArr"]="\\Leftarrow",eZ["\\Larr"]="\\Leftarrow",eZ["\\lrarr"]="\\leftrightarrow",eZ["\\lrArr"]="\\Leftrightarrow",eZ["\\Lrarr"]="\\Leftrightarrow",eZ["\\Mu"]="\\mathrm{M}",eZ["\\natnums"]="\\mathbb{N}",eZ["\\Nu"]="\\mathrm{N}",eZ["\\Omicron"]="\\mathrm{O}",eZ["\\plusmn"]="\\pm",eZ["\\rarr"]="\\rightarrow",eZ["\\rArr"]="\\Rightarrow",eZ["\\Rarr"]="\\Rightarrow",eZ["\\real"]="\\Re",eZ["\\reals"]="\\mathbb{R}",eZ["\\Reals"]="\\mathbb{R}",eZ["\\Rho"]="\\mathrm{P}",eZ["\\sdot"]="\\cdot",eZ["\\sect"]="\\S",eZ["\\spades"]="\\spadesuit",eZ["\\sub"]="\\subset",eZ["\\sube"]="\\subseteq",eZ["\\supe"]="\\supseteq",eZ["\\Tau"]="\\mathrm{T}",eZ["\\thetasym"]="\\vartheta",eZ["\\weierp"]="\\wp",eZ["\\Zeta"]="\\mathrm{Z}",eZ["\\argmin"]="\\DOTSB\\operatorname*{arg\\,min}",eZ["\\argmax"]="\\DOTSB\\operatorname*{arg\\,max}",eZ["\\plim"]="\\DOTSB\\mathop{\\operatorname{plim}}\\limits",eZ["\\bra"]="\\mathinner{\\langle{#1}|}",eZ["\\ket"]="\\mathinner{|{#1}\\rangle}",eZ["\\braket"]="\\mathinner{\\langle{#1}\\rangle}",eZ["\\Bra"]="\\left\\langle#1\\right|",eZ["\\Ket"]="\\left|#1\\right\\rangle";var braketHelper=e=>t=>{var r=t.consumeArg().tokens,n=t.consumeArg().tokens,i=t.consumeArg().tokens,a=t.consumeArg().tokens,o=t.macros.get("|"),l=t.macros.get("\\|");t.macros.beginGroup();var midMacro=t=>r=>{e&&(r.macros.set("|",o),i.length&&r.macros.set("\\|",l));var a=t;return!t&&i.length&&"|"===r.future().text&&(r.popToken(),a=!0),{tokens:a?i:n,numArgs:0}};t.macros.set("|",midMacro(!1)),i.length&&t.macros.set("\\|",midMacro(!0));var s=t.consumeArg().tokens,m=t.expandTokens([...a,...s,...r]);return t.macros.endGroup(),{tokens:m.reverse(),numArgs:0}};n=braketHelper(!1),eZ["\\bra@ket"]=n,i=braketHelper(!0),eZ["\\bra@set"]=i,eZ["\\Braket"]="\\bra@ket{\\left\\langle}{\\,\\middle\\vert\\,}{\\,\\middle\\vert\\,}{\\right\\rangle}",eZ["\\Set"]="\\bra@set{\\left\\{\\:}{\\;\\middle\\vert\\;}{\\;\\middle\\Vert\\;}{\\:\\right\\}}",eZ["\\set"]="\\bra@set{\\{\\,}{\\mid}{}{\\,\\}}",eZ["\\angln"]="{\\angl n}",eZ["\\blue"]="\\textcolor{##6495ed}{#1}",eZ["\\orange"]="\\textcolor{##ffa500}{#1}",eZ["\\pink"]="\\textcolor{##ff00af}{#1}",eZ["\\red"]="\\textcolor{##df0030}{#1}",eZ["\\green"]="\\textcolor{##28ae7b}{#1}",eZ["\\gray"]="\\textcolor{gray}{#1}",eZ["\\purple"]="\\textcolor{##9d38bd}{#1}",eZ["\\blueA"]="\\textcolor{##ccfaff}{#1}",eZ["\\blueB"]="\\textcolor{##80f6ff}{#1}",eZ["\\blueC"]="\\textcolor{##63d9ea}{#1}",eZ["\\blueD"]="\\textcolor{##11accd}{#1}",eZ["\\blueE"]="\\textcolor{##0c7f99}{#1}",eZ["\\tealA"]="\\textcolor{##94fff5}{#1}",eZ["\\tealB"]="\\textcolor{##26edd5}{#1}",eZ["\\tealC"]="\\textcolor{##01d1c1}{#1}",eZ["\\tealD"]="\\textcolor{##01a995}{#1}",eZ["\\tealE"]="\\textcolor{##208170}{#1}",eZ["\\greenA"]="\\textcolor{##b6ffb0}{#1}",eZ["\\greenB"]="\\textcolor{##8af281}{#1}",eZ["\\greenC"]="\\textcolor{##74cf70}{#1}",eZ["\\greenD"]="\\textcolor{##1fab54}{#1}",eZ["\\greenE"]="\\textcolor{##0d923f}{#1}",eZ["\\goldA"]="\\textcolor{##ffd0a9}{#1}",eZ["\\goldB"]="\\textcolor{##ffbb71}{#1}",eZ["\\goldC"]="\\textcolor{##ff9c39}{#1}",eZ["\\goldD"]="\\textcolor{##e07d10}{#1}",eZ["\\goldE"]="\\textcolor{##a75a05}{#1}",eZ["\\redA"]="\\textcolor{##fca9a9}{#1}",eZ["\\redB"]="\\textcolor{##ff8482}{#1}",eZ["\\redC"]="\\textcolor{##f9685d}{#1}",eZ["\\redD"]="\\textcolor{##e84d39}{#1}",eZ["\\redE"]="\\textcolor{##bc2612}{#1}",eZ["\\maroonA"]="\\textcolor{##ffbde0}{#1}",eZ["\\maroonB"]="\\textcolor{##ff92c6}{#1}",eZ["\\maroonC"]="\\textcolor{##ed5fa6}{#1}",eZ["\\maroonD"]="\\textcolor{##ca337c}{#1}",eZ["\\maroonE"]="\\textcolor{##9e034e}{#1}",eZ["\\purpleA"]="\\textcolor{##ddd7ff}{#1}",eZ["\\purpleB"]="\\textcolor{##c6b9fc}{#1}",eZ["\\purpleC"]="\\textcolor{##aa87ff}{#1}",eZ["\\purpleD"]="\\textcolor{##7854ab}{#1}",eZ["\\purpleE"]="\\textcolor{##543b78}{#1}",eZ["\\mintA"]="\\textcolor{##f5f9e8}{#1}",eZ["\\mintB"]="\\textcolor{##edf2df}{#1}",eZ["\\mintC"]="\\textcolor{##e0e5cc}{#1}",eZ["\\grayA"]="\\textcolor{##f6f7f7}{#1}",eZ["\\grayB"]="\\textcolor{##f0f1f2}{#1}",eZ["\\grayC"]="\\textcolor{##e3e5e6}{#1}",eZ["\\grayD"]="\\textcolor{##d6d8da}{#1}",eZ["\\grayE"]="\\textcolor{##babec2}{#1}",eZ["\\grayF"]="\\textcolor{##888d93}{#1}",eZ["\\grayG"]="\\textcolor{##626569}{#1}",eZ["\\grayH"]="\\textcolor{##3b3e40}{#1}",eZ["\\grayI"]="\\textcolor{##21242c}{#1}",eZ["\\kaBlue"]="\\textcolor{##314453}{#1}",eZ["\\kaGreen"]="\\textcolor{##71B307}{#1}";var tm={"^":!0,_:!0,"\\limits":!0,"\\nolimits":!0};let MacroExpander=class MacroExpander{constructor(e,t,r){this.settings=void 0,this.expansionCount=void 0,this.lexer=void 0,this.macros=void 0,this.stack=void 0,this.mode=void 0,this.settings=t,this.expansionCount=0,this.feed(e),this.macros=new Namespace(eZ,t.macros),this.mode=r,this.stack=[]}feed(e){this.lexer=new Lexer(e,this.settings)}switchMode(e){this.mode=e}beginGroup(){this.macros.beginGroup()}endGroup(){this.macros.endGroup()}endGroups(){this.macros.endGroups()}future(){return 0===this.stack.length&&this.pushToken(this.lexer.lex()),this.stack[this.stack.length-1]}popToken(){return this.future(),this.stack.pop()}pushToken(e){this.stack.push(e)}pushTokens(e){this.stack.push(...e)}scanArgument(e){var t,r,n;if(e){if(this.consumeSpaces(),"["!==this.future().text)return null;t=this.popToken(),{tokens:n,end:r}=this.consumeArg(["]"])}else({tokens:n,start:t,end:r}=this.consumeArg());return this.pushToken(new Token("EOF",r.loc)),this.pushTokens(n),t.range(r,"")}consumeSpaces(){for(;;)if(" "===this.future().text)this.stack.pop();else break}consumeArg(e){var t,r=[],n=e&&e.length>0;n||this.consumeSpaces();var i=this.future(),a=0,o=0;do{if(t=this.popToken(),r.push(t),"{"===t.text)++a;else if("}"===t.text){if(-1==--a)throw new ParseError("Extra }",t)}else if("EOF"===t.text)throw new ParseError("Unexpected end of input in a macro argument, expected '"+(e&&n?e[o]:"}")+"'",t);if(e&&n){if((0===a||1===a&&"{"===e[o])&&t.text===e[o]){if(++o===e.length){r.splice(-o,o);break}}else o=0}}while(0!==a||n);return"{"===i.text&&"}"===r[r.length-1].text&&(r.pop(),r.shift()),r.reverse(),{tokens:r,start:i,end:t}}consumeArgs(e,t){if(t){if(t.length!==e+1)throw new ParseError("The length of delimiters doesn't match the number of args!");for(var r=t[0],n=0;n<r.length;n++){var i=this.popToken();if(r[n]!==i.text)throw new ParseError("Use of the macro doesn't match its definition",i)}}for(var a=[],o=0;o<e;o++)a.push(this.consumeArg(t&&t[o+1]).tokens);return a}countExpansion(e){if(this.expansionCount+=e,this.expansionCount>this.settings.maxExpand)throw new ParseError("Too many expansions: infinite loop or need to increase maxExpand setting")}expandOnce(e){var t=this.popToken(),r=t.text,n=t.noexpand?null:this._getExpansion(r);if(null==n||e&&n.unexpandable){if(e&&null==n&&"\\"===r[0]&&!this.isDefined(r))throw new ParseError("Undefined control sequence: "+r);return this.pushToken(t),!1}this.countExpansion(1);var i=n.tokens,a=this.consumeArgs(n.numArgs,n.delimiters);if(n.numArgs){i=i.slice();for(var o=i.length-1;o>=0;--o){var l=i[o];if("#"===l.text){if(0===o)throw new ParseError("Incomplete placeholder at end of macro body",l);if("#"===(l=i[--o]).text)i.splice(o+1,1);else if(/^[1-9]$/.test(l.text))i.splice(o,2,...a[+l.text-1]);else throw new ParseError("Not a valid argument number",l)}}}return this.pushTokens(i),i.length}expandAfterFuture(){return this.expandOnce(),this.future()}expandNextToken(){for(;;)if(!1===this.expandOnce()){var e=this.stack.pop();return e.treatAsRelax&&(e.text="\\relax"),e}throw Error()}expandMacro(e){return this.macros.has(e)?this.expandTokens([new Token(e)]):void 0}expandTokens(e){var t=[],r=this.stack.length;for(this.pushTokens(e);this.stack.length>r;)if(!1===this.expandOnce(!0)){var n=this.stack.pop();n.treatAsRelax&&(n.noexpand=!1,n.treatAsRelax=!1),t.push(n)}return this.countExpansion(t.length),t}expandMacroAsText(e){var t=this.expandMacro(e);return t?t.map(e=>e.text).join(""):t}_getExpansion(e){var t=this.macros.get(e);if(null==t)return t;if(1===e.length){var r=this.lexer.catcodes[e];if(null!=r&&13!==r)return}var n="function"==typeof t?t(this):t;if("string"==typeof n){var i=0;if(-1!==n.indexOf("#"))for(var a=n.replace(/##/g,"");-1!==a.indexOf("#"+(i+1));)++i;for(var o=new Lexer(n,this.settings),l=[],s=o.lex();"EOF"!==s.text;)l.push(s),s=o.lex();return l.reverse(),{tokens:l,numArgs:i}}return n}isDefined(e){return this.macros.has(e)||eS.hasOwnProperty(e)||P.math.hasOwnProperty(e)||P.text.hasOwnProperty(e)||tm.hasOwnProperty(e)}isExpandable(e){var t=this.macros.get(e);return null!=t?"string"==typeof t||"function"==typeof t||!t.unexpandable:eS.hasOwnProperty(e)&&!eS[e].primitive}};var th=/^[₊₋₌₍₎₀₁₂₃₄₅₆₇₈₉ₐₑₕᵢⱼₖₗₘₙₒₚᵣₛₜᵤᵥₓᵦᵧᵨᵩᵪ]/,td=Object.freeze({"₊":"+","₋":"-","₌":"=","₍":"(","₎":")","₀":"0","₁":"1","₂":"2","₃":"3","₄":"4","₅":"5","₆":"6","₇":"7","₈":"8","₉":"9",ₐ:"a",ₑ:"e",ₕ:"h",ᵢ:"i",ⱼ:"j",ₖ:"k",ₗ:"l",ₘ:"m",ₙ:"n",ₒ:"o",ₚ:"p",ᵣ:"r",ₛ:"s",ₜ:"t",ᵤ:"u",ᵥ:"v",ₓ:"x",ᵦ:"β",ᵧ:"γ",ᵨ:"ρ",ᵩ:"ϕ",ᵪ:"χ","⁺":"+","⁻":"-","⁼":"=","⁽":"(","⁾":")","⁰":"0","\xb9":"1","\xb2":"2","\xb3":"3","⁴":"4","⁵":"5","⁶":"6","⁷":"7","⁸":"8","⁹":"9",ᴬ:"A",ᴮ:"B",ᴰ:"D",ᴱ:"E",ᴳ:"G",ᴴ:"H",ᴵ:"I",ᴶ:"J",ᴷ:"K",ᴸ:"L",ᴹ:"M",ᴺ:"N",ᴼ:"O",ᴾ:"P",ᴿ:"R",ᵀ:"T",ᵁ:"U",ⱽ:"V",ᵂ:"W",ᵃ:"a",ᵇ:"b",ᶜ:"c",ᵈ:"d",ᵉ:"e",ᶠ:"f",ᵍ:"g",ʰ:"h",ⁱ:"i",ʲ:"j",ᵏ:"k",ˡ:"l",ᵐ:"m",ⁿ:"n",ᵒ:"o",ᵖ:"p",ʳ:"r",ˢ:"s",ᵗ:"t",ᵘ:"u",ᵛ:"v",ʷ:"w",ˣ:"x",ʸ:"y",ᶻ:"z",ᵝ:"β",ᵞ:"γ",ᵟ:"δ",ᵠ:"ϕ",ᵡ:"χ",ᶿ:"θ"}),tc={"́":{text:"\\'",math:"\\acute"},"̀":{text:"\\`",math:"\\grave"},"̈":{text:'\\"',math:"\\ddot"},"̃":{text:"\\~",math:"\\tilde"},"̄":{text:"\\=",math:"\\bar"},"̆":{text:"\\u",math:"\\breve"},"̌":{text:"\\v",math:"\\check"},"̂":{text:"\\^",math:"\\hat"},"̇":{text:"\\.",math:"\\dot"},"̊":{text:"\\r",math:"\\mathring"},"̋":{text:"\\H"},"̧":{text:"\\c"}},tu={á:"á",à:"à",ä:"ä",ǟ:"ǟ",ã:"ã",ā:"ā",ă:"ă",ắ:"ắ",ằ:"ằ",ẵ:"ẵ",ǎ:"ǎ",â:"â",ấ:"ấ",ầ:"ầ",ẫ:"ẫ",ȧ:"ȧ",ǡ:"ǡ",å:"å",ǻ:"ǻ",ḃ:"ḃ",ć:"ć",ḉ:"ḉ",č:"č",ĉ:"ĉ",ċ:"ċ",ç:"ç",ď:"ď",ḋ:"ḋ",ḑ:"ḑ",é:"é",è:"è",ë:"ë",ẽ:"ẽ",ē:"ē",ḗ:"ḗ",ḕ:"ḕ",ĕ:"ĕ",ḝ:"ḝ",ě:"ě",ê:"ê",ế:"ế",ề:"ề",ễ:"ễ",ė:"ė",ȩ:"ȩ",ḟ:"ḟ",ǵ:"ǵ",ḡ:"ḡ",ğ:"ğ",ǧ:"ǧ",ĝ:"ĝ",ġ:"ġ",ģ:"ģ",ḧ:"ḧ",ȟ:"ȟ",ĥ:"ĥ",ḣ:"ḣ",ḩ:"ḩ",í:"í",ì:"ì",ï:"ï",ḯ:"ḯ",ĩ:"ĩ",ī:"ī",ĭ:"ĭ",ǐ:"ǐ",î:"î",ǰ:"ǰ",ĵ:"ĵ",ḱ:"ḱ",ǩ:"ǩ",ķ:"ķ",ĺ:"ĺ",ľ:"ľ",ļ:"ļ",ḿ:"ḿ",ṁ:"ṁ",ń:"ń",ǹ:"ǹ",ñ:"ñ",ň:"ň",ṅ:"ṅ",ņ:"ņ",ó:"ó",ò:"ò",ö:"ö",ȫ:"ȫ",õ:"õ",ṍ:"ṍ",ṏ:"ṏ",ȭ:"ȭ",ō:"ō",ṓ:"ṓ",ṑ:"ṑ",ŏ:"ŏ",ǒ:"ǒ",ô:"ô",ố:"ố",ồ:"ồ",ỗ:"ỗ",ȯ:"ȯ",ȱ:"ȱ",ő:"ő",ṕ:"ṕ",ṗ:"ṗ",ŕ:"ŕ",ř:"ř",ṙ:"ṙ",ŗ:"ŗ",ś:"ś",ṥ:"ṥ",š:"š",ṧ:"ṧ",ŝ:"ŝ",ṡ:"ṡ",ş:"ş",ẗ:"ẗ",ť:"ť",ṫ:"ṫ",ţ:"ţ",ú:"ú",ù:"ù",ü:"ü",ǘ:"ǘ",ǜ:"ǜ",ǖ:"ǖ",ǚ:"ǚ",ũ:"ũ",ṹ:"ṹ",ū:"ū",ṻ:"ṻ",ŭ:"ŭ",ǔ:"ǔ",û:"û",ů:"ů",ű:"ű",ṽ:"ṽ",ẃ:"ẃ",ẁ:"ẁ",ẅ:"ẅ",ŵ:"ŵ",ẇ:"ẇ",ẘ:"ẘ",ẍ:"ẍ",ẋ:"ẋ",ý:"ý",ỳ:"ỳ",ÿ:"ÿ",ỹ:"ỹ",ȳ:"ȳ",ŷ:"ŷ",ẏ:"ẏ",ẙ:"ẙ",ź:"ź",ž:"ž",ẑ:"ẑ",ż:"ż",Á:"Á",À:"À",Ä:"Ä",Ǟ:"Ǟ",Ã:"Ã",Ā:"Ā",Ă:"Ă",Ắ:"Ắ",Ằ:"Ằ",Ẵ:"Ẵ",Ǎ:"Ǎ",Â:"Â",Ấ:"Ấ",Ầ:"Ầ",Ẫ:"Ẫ",Ȧ:"Ȧ",Ǡ:"Ǡ",Å:"Å",Ǻ:"Ǻ",Ḃ:"Ḃ",Ć:"Ć",Ḉ:"Ḉ",Č:"Č",Ĉ:"Ĉ",Ċ:"Ċ",Ç:"Ç",Ď:"Ď",Ḋ:"Ḋ",Ḑ:"Ḑ",É:"É",È:"È",Ë:"Ë",Ẽ:"Ẽ",Ē:"Ē",Ḗ:"Ḗ",Ḕ:"Ḕ",Ĕ:"Ĕ",Ḝ:"Ḝ",Ě:"Ě",Ê:"Ê",Ế:"Ế",Ề:"Ề",Ễ:"Ễ",Ė:"Ė",Ȩ:"Ȩ",Ḟ:"Ḟ",Ǵ:"Ǵ",Ḡ:"Ḡ",Ğ:"Ğ",Ǧ:"Ǧ",Ĝ:"Ĝ",Ġ:"Ġ",Ģ:"Ģ",Ḧ:"Ḧ",Ȟ:"Ȟ",Ĥ:"Ĥ",Ḣ:"Ḣ",Ḩ:"Ḩ",Í:"Í",Ì:"Ì",Ï:"Ï",Ḯ:"Ḯ",Ĩ:"Ĩ",Ī:"Ī",Ĭ:"Ĭ",Ǐ:"Ǐ",Î:"Î",İ:"İ",Ĵ:"Ĵ",Ḱ:"Ḱ",Ǩ:"Ǩ",Ķ:"Ķ",Ĺ:"Ĺ",Ľ:"Ľ",Ļ:"Ļ",Ḿ:"Ḿ",Ṁ:"Ṁ",Ń:"Ń",Ǹ:"Ǹ",Ñ:"Ñ",Ň:"Ň",Ṅ:"Ṅ",Ņ:"Ņ",Ó:"Ó",Ò:"Ò",Ö:"Ö",Ȫ:"Ȫ",Õ:"Õ",Ṍ:"Ṍ",Ṏ:"Ṏ",Ȭ:"Ȭ",Ō:"Ō",Ṓ:"Ṓ",Ṑ:"Ṑ",Ŏ:"Ŏ",Ǒ:"Ǒ",Ô:"Ô",Ố:"Ố",Ồ:"Ồ",Ỗ:"Ỗ",Ȯ:"Ȯ",Ȱ:"Ȱ",Ő:"Ő",Ṕ:"Ṕ",Ṗ:"Ṗ",Ŕ:"Ŕ",Ř:"Ř",Ṙ:"Ṙ",Ŗ:"Ŗ",Ś:"Ś",Ṥ:"Ṥ",Š:"Š",Ṧ:"Ṧ",Ŝ:"Ŝ",Ṡ:"Ṡ",Ş:"Ş",Ť:"Ť",Ṫ:"Ṫ",Ţ:"Ţ",Ú:"Ú",Ù:"Ù",Ü:"Ü",Ǘ:"Ǘ",Ǜ:"Ǜ",Ǖ:"Ǖ",Ǚ:"Ǚ",Ũ:"Ũ",Ṹ:"Ṹ",Ū:"Ū",Ṻ:"Ṻ",Ŭ:"Ŭ",Ǔ:"Ǔ",Û:"Û",Ů:"Ů",Ű:"Ű",Ṽ:"Ṽ",Ẃ:"Ẃ",Ẁ:"Ẁ",Ẅ:"Ẅ",Ŵ:"Ŵ",Ẇ:"Ẇ",Ẍ:"Ẍ",Ẋ:"Ẋ",Ý:"Ý",Ỳ:"Ỳ",Ÿ:"Ÿ",Ỹ:"Ỹ",Ȳ:"Ȳ",Ŷ:"Ŷ",Ẏ:"Ẏ",Ź:"Ź",Ž:"Ž",Ẑ:"Ẑ",Ż:"Ż",ά:"ά",ὰ:"ὰ",ᾱ:"ᾱ",ᾰ:"ᾰ",έ:"έ",ὲ:"ὲ",ή:"ή",ὴ:"ὴ",ί:"ί",ὶ:"ὶ",ϊ:"ϊ",ΐ:"ΐ",ῒ:"ῒ",ῑ:"ῑ",ῐ:"ῐ",ό:"ό",ὸ:"ὸ",ύ:"ύ",ὺ:"ὺ",ϋ:"ϋ",ΰ:"ΰ",ῢ:"ῢ",ῡ:"ῡ",ῠ:"ῠ",ώ:"ώ",ὼ:"ὼ",Ύ:"Ύ",Ὺ:"Ὺ",Ϋ:"Ϋ",Ῡ:"Ῡ",Ῠ:"Ῠ",Ώ:"Ώ",Ὼ:"Ὼ"};let Parser=class Parser{constructor(e,t){this.mode=void 0,this.gullet=void 0,this.settings=void 0,this.leftrightDepth=void 0,this.nextToken=void 0,this.mode="math",this.gullet=new MacroExpander(e,t,this.mode),this.settings=t,this.leftrightDepth=0}expect(e,t){if(void 0===t&&(t=!0),this.fetch().text!==e)throw new ParseError("Expected '"+e+"', got '"+this.fetch().text+"'",this.fetch());t&&this.consume()}consume(){this.nextToken=null}fetch(){return null==this.nextToken&&(this.nextToken=this.gullet.expandNextToken()),this.nextToken}switchMode(e){this.mode=e,this.gullet.switchMode(e)}parse(){this.settings.globalGroup||this.gullet.beginGroup(),this.settings.colorIsTextColor&&this.gullet.macros.set("\\color","\\textcolor");try{var e=this.parseExpression(!1);return this.expect("EOF"),this.settings.globalGroup||this.gullet.endGroup(),e}finally{this.gullet.endGroups()}}subparse(e){var t=this.nextToken;this.consume(),this.gullet.pushToken(new Token("}")),this.gullet.pushTokens(e);var r=this.parseExpression(!1);return this.expect("}"),this.nextToken=t,r}parseExpression(e,t){for(var r=[];;){"math"===this.mode&&this.consumeSpaces();var n=this.fetch();if(-1!==Parser.endOfExpression.indexOf(n.text)||t&&n.text===t||e&&eS[n.text]&&eS[n.text].infix)break;var i=this.parseAtom(t);if(i){if("internal"===i.type)continue}else break;r.push(i)}return"text"===this.mode&&this.formLigatures(r),this.handleInfixNodes(r)}handleInfixNodes(e){for(var t=-1,r=0;r<e.length;r++)if("infix"===e[r].type){if(-1!==t)throw new ParseError("only one infix operator per group",e[r].token);t=r,n=e[r].replaceWith}if(-1===t||!n)return e;var n,i,a,o=e.slice(0,t),l=e.slice(t+1);return i=1===o.length&&"ordgroup"===o[0].type?o[0]:{type:"ordgroup",mode:this.mode,body:o},a=1===l.length&&"ordgroup"===l[0].type?l[0]:{type:"ordgroup",mode:this.mode,body:l},["\\\\abovefrac"===n?this.callFunction(n,[i,e[t],a],[]):this.callFunction(n,[i,a],[])]}handleSupSubscript(e){var t,r=this.fetch(),n=r.text;this.consume(),this.consumeSpaces();do t=this.parseGroup(e);while((null==t?void 0:t.type)==="internal");if(!t)throw new ParseError("Expected group after '"+n+"'",r);return t}formatUnsupportedCmd(e){for(var t=[],r=0;r<e.length;r++)t.push({type:"textord",mode:"text",text:e[r]});var n={type:"text",mode:this.mode,body:t};return{type:"color",mode:this.mode,color:this.settings.errorColor,body:[n]}}parseAtom(e){var t,r,n=this.parseGroup("atom",e);if((null==n?void 0:n.type)==="internal"||"text"===this.mode)return n;for(;;){this.consumeSpaces();var i=this.fetch();if("\\limits"===i.text||"\\nolimits"===i.text){if(n&&"op"===n.type){var a="\\limits"===i.text;n.limits=a,n.alwaysHandleSupSub=!0}else if(n&&"operatorname"===n.type)n.alwaysHandleSupSub&&(n.limits="\\limits"===i.text);else throw new ParseError("Limit controls must follow a math operator",i);this.consume()}else if("^"===i.text){if(t)throw new ParseError("Double superscript",i);t=this.handleSupSubscript("superscript")}else if("_"===i.text){if(r)throw new ParseError("Double subscript",i);r=this.handleSupSubscript("subscript")}else if("'"===i.text){if(t)throw new ParseError("Double superscript",i);var o={type:"textord",mode:this.mode,text:"\\prime"},l=[o];for(this.consume();"'"===this.fetch().text;)l.push(o),this.consume();"^"===this.fetch().text&&l.push(this.handleSupSubscript("superscript")),t={type:"ordgroup",mode:this.mode,body:l}}else if(td[i.text]){var s=th.test(i.text),m=[];for(m.push(new Token(td[i.text])),this.consume();;){var h=this.fetch().text;if(!td[h]||th.test(h)!==s)break;m.unshift(new Token(td[h])),this.consume()}var d=this.subparse(m);s?r={type:"ordgroup",mode:"math",body:d}:t={type:"ordgroup",mode:"math",body:d}}else break}return t||r?{type:"supsub",mode:this.mode,base:n,sup:t,sub:r}:n}parseFunction(e,t){var r=this.fetch(),n=r.text,i=eS[n];if(!i)return null;if(this.consume(),t&&"atom"!==t&&!i.allowedInArgument)throw new ParseError("Got function '"+n+"' with no arguments"+(t?" as "+t:""),r);if("text"!==this.mode||i.allowedInText){if("math"===this.mode&&!1===i.allowedInMath)throw new ParseError("Can't use function '"+n+"' in math mode",r)}else throw new ParseError("Can't use function '"+n+"' in text mode",r);var{args:a,optArgs:o}=this.parseArguments(n,i);return this.callFunction(n,a,o,r,e)}callFunction(e,t,r,n,i){var a=eS[e];if(a&&a.handler)return a.handler({funcName:e,parser:this,token:n,breakOnTokenText:i},t,r);throw new ParseError("No function handler for "+e)}parseArguments(e,t){var r=t.numArgs+t.numOptionalArgs;if(0===r)return{args:[],optArgs:[]};for(var n=[],i=[],a=0;a<r;a++){var o=t.argTypes&&t.argTypes[a],l=a<t.numOptionalArgs;(t.primitive&&null==o||"sqrt"===t.type&&1===a&&null==i[0])&&(o="primitive");var s=this.parseGroupOfType("argument to '"+e+"'",o,l);if(l)i.push(s);else if(null!=s)n.push(s);else throw new ParseError("Null argument, please report this as a bug")}return{args:n,optArgs:i}}parseGroupOfType(e,t,r){switch(t){case"color":return this.parseColorGroup(r);case"size":return this.parseSizeGroup(r);case"url":return this.parseUrlGroup(r);case"math":case"text":return this.parseArgumentGroup(r,t);case"hbox":var n=this.parseArgumentGroup(r,"text");return null!=n?{type:"styling",mode:n.mode,body:[n],style:"text"}:null;case"raw":var i=this.parseStringGroup("raw",r);return null!=i?{type:"raw",mode:"text",string:i.text}:null;case"primitive":if(r)throw new ParseError("A primitive argument cannot be optional");var a=this.parseGroup(e);if(null==a)throw new ParseError("Expected group as "+e,this.fetch());return a;case"original":case null:case void 0:return this.parseArgumentGroup(r);default:throw new ParseError("Unknown group type as "+e,this.fetch())}}consumeSpaces(){for(;" "===this.fetch().text;)this.consume()}parseStringGroup(e,t){var r,n=this.gullet.scanArgument(t);if(null==n)return null;for(var i="";"EOF"!==(r=this.fetch()).text;)i+=r.text,this.consume();return this.consume(),n.text=i,n}parseRegexGroup(e,t){for(var r,n=this.fetch(),i=n,a="";"EOF"!==(r=this.fetch()).text&&e.test(a+r.text);)a+=(i=r).text,this.consume();if(""===a)throw new ParseError("Invalid "+t+": '"+n.text+"'",n);return n.range(i,a)}parseColorGroup(e){var t=this.parseStringGroup("color",e);if(null==t)return null;var r=/^(#[a-f0-9]{3}|#?[a-f0-9]{6}|[a-z]+)$/i.exec(t.text);if(!r)throw new ParseError("Invalid color: '"+t.text+"'",t);var n=r[0];return/^[0-9a-f]{6}$/i.test(n)&&(n="#"+n),{type:"color-token",mode:this.mode,color:n}}parseSizeGroup(e){var t,r=!1;if(this.gullet.consumeSpaces(),!(t=e||"{"===this.gullet.future().text?this.parseStringGroup("size",e):this.parseRegexGroup(/^[-+]? *(?:$|\d+|\d+\.\d*|\.\d*) *[a-z]{0,2} *$/,"size")))return null;e||0!==t.text.length||(t.text="0pt",r=!0);var n=/([-+]?) *(\d+(?:\.\d*)?|\.\d+) *([a-z]{2})/.exec(t.text);if(!n)throw new ParseError("Invalid size: '"+t.text+"'",t);var i={number:+(n[1]+n[2]),unit:n[3]};if(!validUnit(i))throw new ParseError("Invalid unit: '"+i.unit+"'",t);return{type:"size",mode:this.mode,value:i,isBlank:r}}parseUrlGroup(e){this.gullet.lexer.setCatcode("%",13),this.gullet.lexer.setCatcode("~",12);var t=this.parseStringGroup("url",e);if(this.gullet.lexer.setCatcode("%",14),this.gullet.lexer.setCatcode("~",13),null==t)return null;var r=t.text.replace(/\\([#$%&~_^{}])/g,"$1");return{type:"url",mode:this.mode,url:r}}parseArgumentGroup(e,t){var r=this.gullet.scanArgument(e);if(null==r)return null;var n=this.mode;t&&this.switchMode(t),this.gullet.beginGroup();var i=this.parseExpression(!1,"EOF");this.expect("EOF"),this.gullet.endGroup();var a={type:"ordgroup",mode:this.mode,loc:r.loc,body:i};return t&&this.switchMode(n),a}parseGroup(e,t){var r,n=this.fetch(),i=n.text;if("{"===i||"\\begingroup"===i){this.consume();var a="{"===i?"}":"\\endgroup";this.gullet.beginGroup();var o=this.parseExpression(!1,a),l=this.fetch();this.expect(a),this.gullet.endGroup(),r={type:"ordgroup",mode:this.mode,loc:SourceLocation.range(n,l),body:o,semisimple:"\\begingroup"===i||void 0}}else if(null==(r=this.parseFunction(t,e)||this.parseSymbol())&&"\\"===i[0]&&!tm.hasOwnProperty(i)){if(this.settings.throwOnError)throw new ParseError("Undefined control sequence: "+i,n);r=this.formatUnsupportedCmd(i),this.consume()}return r}formLigatures(e){for(var t=e.length-1,r=0;r<t;++r){var n=e[r],i=n.text;"-"===i&&"-"===e[r+1].text&&(r+1<t&&"-"===e[r+2].text?(e.splice(r,3,{type:"textord",mode:"text",loc:SourceLocation.range(n,e[r+2]),text:"---"}),t-=2):(e.splice(r,2,{type:"textord",mode:"text",loc:SourceLocation.range(n,e[r+1]),text:"--"}),t-=1)),("'"===i||"`"===i)&&e[r+1].text===i&&(e.splice(r,2,{type:"textord",mode:"text",loc:SourceLocation.range(n,e[r+1]),text:i+i}),t-=1)}}parseSymbol(){var e=this.fetch(),t=e.text;if(/^\\verb[^a-zA-Z]/.test(t)){this.consume();var r=t.slice(5),n="*"===r.charAt(0);if(n&&(r=r.slice(1)),r.length<2||r.charAt(0)!==r.slice(-1))throw new ParseError("\\verb assertion failed --\n                    please report what input caused this bug");return{type:"verb",mode:"text",body:r=r.slice(1,-1),star:n}}tu.hasOwnProperty(t[0])&&!P[this.mode][t[0]]&&(this.settings.strict&&"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Accented Unicode text character "'+t[0]+'" used in math mode',e),t=tu[t[0]]+t.slice(1));var i=tn.exec(t);if(i&&("i"===(t=t.substring(0,i.index))?t="ı":"j"===t&&(t="ȷ")),P[this.mode][t]){this.settings.strict&&"math"===this.mode&&el.indexOf(t)>=0&&this.settings.reportNonstrict("unicodeTextInMathMode",'Latin-1/Unicode text character "'+t[0]+'" used in math mode',e);var a,o=P[this.mode][t].group,l=SourceLocation.range(e);a=C.hasOwnProperty(o)?{type:"atom",mode:this.mode,family:o,loc:l,text:t}:{type:o,mode:this.mode,loc:l,text:t}}else{if(!(t.charCodeAt(0)>=128))return null;this.settings.strict&&(supportedCodepoint(t.charCodeAt(0))?"math"===this.mode&&this.settings.reportNonstrict("unicodeTextInMathMode",'Unicode text character "'+t[0]+'" used in math mode',e):this.settings.reportNonstrict("unknownSymbol",'Unrecognized Unicode character "'+t[0]+'" ('+t.charCodeAt(0)+")",e)),a={type:"textord",mode:"text",loc:SourceLocation.range(e),text:t}}if(this.consume(),i)for(var s=0;s<i[0].length;s++){var m=i[0][s];if(!tc[m])throw new ParseError("Unknown accent ' "+m+"'",e);var h=tc[m][this.mode]||tc[m].text;if(!h)throw new ParseError("Accent "+m+" unsupported in "+this.mode+" mode",e);a={type:"accent",mode:this.mode,loc:SourceLocation.range(e),label:h,isStretchy:!1,isShifty:!0,base:a}}return a}};Parser.endOfExpression=["}","\\endgroup","\\end","\\right","&"];var parseTree=function(e,t){if(!("string"==typeof e||e instanceof String))throw TypeError("KaTeX can only parse string typed expression");var r=new Parser(e,t);delete r.gullet.macros.current["\\df@tag"];var n=r.parse();if(delete r.gullet.macros.current["\\current@color"],delete r.gullet.macros.current["\\color"],r.gullet.macros.get("\\df@tag")){if(!t.displayMode)throw new ParseError("\\tag works only in display equations");n=[{type:"tag",mode:"text",body:n,tag:r.subparse([new Token("\\df@tag")])}]}return n},render=function(e,t,r){t.textContent="";var n=renderToDomTree(e,r).toNode();t.appendChild(n)};"undefined"!=typeof document&&"CSS1Compat"!==document.compatMode&&("undefined"!=typeof console&&console.warn("Warning: KaTeX doesn't work in quirks mode. Make sure your website has a suitable doctype."),render=function(){throw new ParseError("KaTeX doesn't work in quirks mode.")});var renderError=function(e,t,r){if(r.throwOnError||!(e instanceof ParseError))throw e;var n=ep.makeSpan(["katex-error"],[new SymbolNode(t)]);return n.setAttribute("title",e.toString()),n.setAttribute("style","color:"+r.errorColor),n},renderToDomTree=function(e,t){var r=new Settings(t);try{var n=parseTree(e,r);return buildTree(n,e,r)}catch(t){return renderError(t,e,r)}},tp={version:"0.16.22",render,renderToString:function(e,t){return renderToDomTree(e,t).toMarkup()},ParseError,SETTINGS_SCHEMA:m,__parse:function(e,t){return parseTree(e,new Settings(t))},__renderToDomTree:renderToDomTree,__renderToHTMLTree:function(e,t){var r=new Settings(t);try{var n=parseTree(e,r);return buildHTMLTree(n,e,r)}catch(t){return renderError(t,e,r)}},__setFontMetrics:function(e,t){x[e]=t},__defineSymbol:defineSymbol,__defineFunction:defineFunction,__defineMacro:function(e,t){eZ[e]=t},__domTree:{Span,Anchor,SymbolNode,SvgNode,PathNode,LineNode}}}}]);