/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "dns":
/*!**********************!*\
  !*** external "dns" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("dns");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "http2":
/*!************************!*\
  !*** external "http2" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("http2");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(rsc)/./src/app/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AppearanceContext.tsx */ \"(ssr)/./src/contexts/AppearanceContext.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/AuthContext.tsx */ \"(ssr)/./src/contexts/AuthContext.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSGVucmklNUNEZXNrdG9wJTVDUmFmdGhvciU1Q1JhZnRob3JJQSU1Q25vZGVfbW9kdWxlcyU1Q25leHQlNUNmb250JTVDZ29vZ2xlJTVDdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlNUMlNUNhcHAlNUMlNUNsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1JhZnRob3IlNUNSYWZ0aG9ySUElNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1JhZnRob3IlNUNSYWZ0aG9ySUElNUNzcmMlNUNjb250ZXh0cyU1Q0FwcGVhcmFuY2VDb250ZXh0LnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0hlbnJpJTVDRGVza3RvcCU1Q1JhZnRob3IlNUNSYWZ0aG9ySUElNUNzcmMlNUNjb250ZXh0cyU1Q0F1dGhDb250ZXh0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0xBQXdIO0FBQ3hIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8/Mjc2YSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhlbnJpXFxcXERlc2t0b3BcXFxcUmFmdGhvclxcXFxSYWZ0aG9ySUFcXFxcc3JjXFxcXGNvbnRleHRzXFxcXEFwcGVhcmFuY2VDb250ZXh0LnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSGVucmlcXFxcRGVza3RvcFxcXFxSYWZ0aG9yXFxcXFJhZnRob3JJQVxcXFxzcmNcXFxcY29udGV4dHNcXFxcQXV0aENvbnRleHQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAppearanceContext.tsx&modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Ccontexts%5CAuthContext.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true!":
/*!**************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true! ***!
  \**************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/login/page.tsx */ \"(ssr)/./src/app/login/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDSGVucmklNUNEZXNrdG9wJTVDUmFmdGhvciU1Q1JhZnRob3JJQSU1Q3NyYyU1Q2FwcCU1Q2xvZ2luJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8/OWRmNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEhlbnJpXFxcXERlc2t0b3BcXFxcUmFmdGhvclxcXFxSYWZ0aG9ySUFcXFxcc3JjXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp%5Clogin%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\nfunction LoginPage() {\n    const [mode, setMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"login\");\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        username: \"\",\n        email: \"\",\n        password: \"\"\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const { user, loading: authLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    // Redirecionar se já estiver logado\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && user) {\n            router.push(\"/\");\n        }\n    }, [\n        user,\n        authLoading,\n        router\n    ]);\n    // Mostrar loading enquanto verifica autenticação\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-rafthor flex flex-col items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/80\",\n                        children: \"Carregando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    // Não renderizar se já estiver logado (será redirecionado)\n    if (user) {\n        return null;\n    }\n    const handleInputChange = (e)=>{\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setLoading(true);\n        setError(\"\");\n        try {\n            if (mode === \"register\") {\n                // Validar se o nome de usuário foi preenchido\n                if (!formData.username.trim()) {\n                    setError(\"Nome de usu\\xe1rio \\xe9 obrigat\\xf3rio\");\n                    return;\n                }\n                // Verificar se o username já existe\n                console.log(\"Verificando se o username j\\xe1 existe...\");\n                const existingUserDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", formData.username));\n                if (existingUserDoc.exists()) {\n                    setError(\"Este nome de usu\\xe1rio j\\xe1 est\\xe1 em uso. Escolha outro.\");\n                    return;\n                }\n                // Verificar se já existe um usuário com este email\n                console.log(\"Verificando se o email j\\xe1 est\\xe1 em uso no Firestore...\");\n                const usuariosRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.collection)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\");\n                const emailQuery = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.query)(usuariosRef, (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.where)(\"email\", \"==\", formData.email));\n                const emailQuerySnapshot = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDocs)(emailQuery);\n                if (!emailQuerySnapshot.empty) {\n                    setError(\"J\\xe1 existe uma conta com este email.\");\n                    return;\n                }\n                // Criar usuário no Firebase Auth\n                console.log(\"Criando usu\\xe1rio no Firebase Auth...\");\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth, formData.email, formData.password);\n                console.log(\"Usu\\xe1rio criado no Auth:\", userCredential.user.uid);\n                // Criar documento no Firestore\n                console.log(\"Criando documento no Firestore...\");\n                const userData = {\n                    id: userCredential.user.uid,\n                    username: formData.username,\n                    email: formData.email,\n                    balance: 0,\n                    createdAt: new Date().toISOString()\n                };\n                console.log(\"Dados do usu\\xe1rio:\", userData);\n                console.log(\"Caminho do documento:\", `usuarios/${formData.username}`);\n                try {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", formData.username), userData);\n                    console.log(\"Documento criado no Firestore com sucesso!\");\n                    // Pequeno delay para garantir que o Firestore processou\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                    // Verificar se o documento foi realmente criado\n                    const docRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", formData.username);\n                    const docSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(docRef);\n                    if (docSnap.exists()) {\n                        const docData = docSnap.data();\n                        console.log(\"Verifica\\xe7\\xe3o: Documento existe e cont\\xe9m:\", docData);\n                        // Verificar se todos os campos essenciais estão presentes\n                        const requiredFields = [\n                            \"id\",\n                            \"username\",\n                            \"email\",\n                            \"balance\",\n                            \"createdAt\"\n                        ];\n                        const missingFields = requiredFields.filter((field)=>!(field in docData));\n                        if (missingFields.length > 0) {\n                            console.error(\"ERRO: Campos obrigat\\xf3rios ausentes:\", missingFields);\n                            throw new Error(`Documento criado incompleto. Campos ausentes: ${missingFields.join(\", \")}`);\n                        }\n                        console.log(\"✅ Documento do usu\\xe1rio criado com sucesso e verificado!\");\n                    } else {\n                        console.error(\"ERRO: Documento n\\xe3o foi encontrado ap\\xf3s cria\\xe7\\xe3o!\");\n                        throw new Error(\"Falha ao criar documento do usu\\xe1rio\");\n                    }\n                } catch (firestoreError) {\n                    console.error(\"Erro espec\\xedfico do Firestore:\", firestoreError);\n                    throw firestoreError;\n                }\n                // Criar configurações padrão\n                console.log(\"Criando configura\\xe7\\xf5es padr\\xe3o...\");\n                const defaultConfigs = {\n                    aparencia: {\n                        fonte: \"Inter\",\n                        tamanhoFonte: 14,\n                        palavrasPorSessao: 5000\n                    },\n                    endpoints: {\n                        \"OpenRouter\": {\n                            nome: \"OpenRouter\",\n                            url: \"https://openrouter.ai/api/v1/chat/completions\",\n                            apiKey: \"\",\n                            modeloPadrao: \"meta-llama/llama-3.1-8b-instruct:free\",\n                            ativo: false\n                        },\n                        \"DeepSeek\": {\n                            nome: \"DeepSeek\",\n                            url: \"https://api.deepseek.com/v1/chat/completions\",\n                            apiKey: \"\",\n                            modeloPadrao: \"deepseek-chat\",\n                            ativo: false\n                        }\n                    },\n                    memorias: {},\n                    categorias: {}\n                };\n                try {\n                    await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.setDoc)((0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", formData.username, \"configuracoes\", \"settings\"), defaultConfigs);\n                    console.log(\"Configura\\xe7\\xf5es padr\\xe3o criadas com sucesso!\");\n                    // Verificar se as configurações foram criadas\n                    const configDocRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.db, \"usuarios\", formData.username, \"configuracoes\", \"settings\");\n                    const configDocSnap = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_3__.getDoc)(configDocRef);\n                    if (configDocSnap.exists()) {\n                        console.log(\"Verifica\\xe7\\xe3o: Configura\\xe7\\xf5es existem e cont\\xeam:\", configDocSnap.data());\n                    } else {\n                        console.error(\"AVISO: Configura\\xe7\\xf5es n\\xe3o foram encontradas ap\\xf3s cria\\xe7\\xe3o!\");\n                    }\n                } catch (configError) {\n                    console.error(\"Erro ao criar configura\\xe7\\xf5es padr\\xe3o:\", configError);\n                // Não bloquear o registro se houver erro nas configurações\n                }\n                // Criar estrutura de pastas no Storage\n                try {\n                    console.log(\"Criando estrutura de pastas no Storage...\");\n                    const userFolderRef = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.ref)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.storage, `usuarios/${formData.username}/.keep`);\n                    const emptyFile = new Uint8Array(0);\n                    await (0,firebase_storage__WEBPACK_IMPORTED_MODULE_4__.uploadBytes)(userFolderRef, emptyFile);\n                    console.log(\"Estrutura de pastas criada no Storage com sucesso!\");\n                } catch (storageError) {\n                    console.error(\"Erro ao criar estrutura no Storage:\", storageError);\n                // Não bloquear o registro se houver erro no Storage\n                }\n                router.push(\"/\");\n            } else {\n                // Login\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signInWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth, formData.email, formData.password);\n                console.log(\"Login realizado com sucesso!\");\n                router.push(\"/\");\n            }\n        } catch (error) {\n            console.error(\"Erro completo:\", error);\n            console.error(\"C\\xf3digo do erro:\", error.code);\n            console.error(\"Mensagem do erro:\", error.message);\n            // Traduzir erros do Firebase para português\n            let errorMessage = \"Erro ao processar solicita\\xe7\\xe3o\";\n            if (error.code === \"auth/email-already-in-use\") {\n                errorMessage = \"Este email j\\xe1 est\\xe1 em uso\";\n            } else if (error.code === \"auth/weak-password\") {\n                errorMessage = \"A senha deve ter pelo menos 6 caracteres\";\n            } else if (error.code === \"auth/invalid-email\") {\n                errorMessage = \"Email inv\\xe1lido\";\n            } else if (error.code === \"auth/user-not-found\") {\n                errorMessage = \"Usu\\xe1rio n\\xe3o encontrado\";\n            } else if (error.code === \"auth/wrong-password\") {\n                errorMessage = \"Senha incorreta\";\n            } else if (error.code === \"auth/invalid-credential\") {\n                errorMessage = \"Credenciais inv\\xe1lidas\";\n            } else if (error.code === \"permission-denied\") {\n                errorMessage = \"Permiss\\xe3o negada para escrever no Firestore. Verifique as regras de seguran\\xe7a.\";\n            } else if (error.code === \"unavailable\") {\n                errorMessage = \"Servi\\xe7o do Firestore indispon\\xedvel. Tente novamente.\";\n            } else if (error.message === \"Falha ao criar documento do usu\\xe1rio\") {\n                errorMessage = \"Erro ao criar perfil do usu\\xe1rio. Tente novamente.\";\n            } else {\n                errorMessage = `Erro: ${error.message}`;\n            }\n            setError(errorMessage);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const toggleMode = ()=>{\n        setMode(mode === \"login\" ? \"register\" : \"login\");\n        setError(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-rafthor relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-0 left-0 w-full h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/3 rounded-full blur-3xl animate-pulse delay-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 opacity-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0\",\n                            style: {\n                                backgroundImage: `radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0)`,\n                                backgroundSize: \"20px 20px\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex items-center justify-between p-4 sm:p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-white/10 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-white\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl sm:text-2xl font-bold text-white\",\n                                        children: \"Rafthor\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-white/60 text-xs sm:text-sm\",\n                                        children: \"AI Chatbot Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden sm:block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-sm\",\n                                    children: \"Vers\\xe3o Beta\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/40 text-xs\",\n                                    children: \"v1.0.0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 270,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 flex items-center justify-center min-h-[calc(100vh-200px)] px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-md mx-auto px-4 sm:px-0 animate-slide-in-up\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative bg-white/5 backdrop-blur-xl rounded-3xl p-6 sm:p-8 shadow-2xl border border-white/10 overflow-hidden hover-lift\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-white/5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -top-20 -right-20 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-20 -left-20 w-40 h-40 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center justify-center w-16 h-16 bg-white/10 rounded-2xl mb-4 backdrop-blur-sm border border-white/20 animate-float\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-8 h-8 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl sm:text-3xl font-bold text-white mb-2 tracking-tight\",\n                                                    children: mode === \"login\" ? \"Bem-vindo de volta\" : \"Criar sua conta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/60 text-sm sm:text-base\",\n                                                    children: mode === \"login\" ? \"Entre na sua conta do Rafthor\" : \"Junte-se \\xe0 revolu\\xe7\\xe3o da IA\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-5\",\n                                            children: [\n                                                mode === \"register\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"username\",\n                                                            className: \"block text-sm font-semibold text-white/90 mb-3\",\n                                                            children: \"Nome de usu\\xe1rio\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 332,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    id: \"username\",\n                                                                    name: \"username\",\n                                                                    value: formData.username,\n                                                                    onChange: handleInputChange,\n                                                                    required: true,\n                                                                    className: \"w-full pl-12 pr-4 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base\",\n                                                                    placeholder: \"Escolha um nome de usu\\xe1rio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 335,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 329,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"email\",\n                                                            className: \"block text-sm font-semibold text-white/90 mb-3\",\n                                                            children: mode === \"login\" ? \"Email\" : \"Endere\\xe7o de email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 355,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 354,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"email\",\n                                                                    id: \"email\",\n                                                                    name: \"email\",\n                                                                    value: formData.email,\n                                                                    onChange: handleInputChange,\n                                                                    required: true,\n                                                                    className: \"w-full pl-12 pr-4 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base\",\n                                                                    placeholder: \"<EMAIL>\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"password\",\n                                                            className: \"block text-sm font-semibold text-white/90 mb-3\",\n                                                            children: \"Senha\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"h-5 w-5 text-white/40 group-focus-within:text-white/70 transition-colors\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 379,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: showPassword ? \"text\" : \"password\",\n                                                                    id: \"password\",\n                                                                    name: \"password\",\n                                                                    value: formData.password,\n                                                                    onChange: handleInputChange,\n                                                                    required: true,\n                                                                    className: \"w-full pl-12 pr-12 py-4 bg-white/5 border border-white/20 rounded-2xl text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-white/30 focus:bg-white/10 transition-all duration-300 text-sm sm:text-base\",\n                                                                    placeholder: \"Crie uma senha segura\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    type: \"button\",\n                                                                    onClick: ()=>setShowPassword(!showPassword),\n                                                                    className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-white/40 hover:text-white/70 transition-colors\",\n                                                                    children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"h-5 w-5\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            strokeLinecap: \"round\",\n                                                                            strokeLinejoin: \"round\",\n                                                                            strokeWidth: 2,\n                                                                            d: \"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 27\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        className: \"h-5 w-5\",\n                                                                        fill: \"none\",\n                                                                        stroke: \"currentColor\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 403,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                                lineNumber: 404,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 402,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-500/10 border border-red-500/30 rounded-2xl p-4 backdrop-blur-sm animate-fade-in\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-5 w-5 text-red-400 flex-shrink-0\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-200 text-sm\",\n                                                                children: error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    type: \"submit\",\n                                                    disabled: loading,\n                                                    className: \"group relative w-full bg-gradient-to-r from-white/20 to-white/10 hover:from-white/30 hover:to-white/20 text-white font-semibold py-4 px-6 rounded-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm border border-white/20 hover:border-white/30 transform hover:scale-[1.02] active:scale-[0.98]\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center space-x-3\",\n                                                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"animate-spin h-5 w-5 text-white\",\n                                                                    fill: \"none\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                            className: \"opacity-25\",\n                                                                            cx: \"12\",\n                                                                            cy: \"12\",\n                                                                            r: \"10\",\n                                                                            stroke: \"currentColor\",\n                                                                            strokeWidth: \"4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                            className: \"opacity-75\",\n                                                                            fill: \"currentColor\",\n                                                                            d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                            lineNumber: 434,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Processando...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 436,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: mode === \"login\" ? \"Entrar na conta\" : \"Criar minha conta\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 440,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"h-5 w-5 group-hover:translate-x-1 transition-transform\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                        lineNumber: 442,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                                    lineNumber: 441,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative my-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-0 flex items-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full border-t border-white/10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative flex justify-center text-sm\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-4 bg-gradient-rafthor text-white/50\",\n                                                        children: mode === \"login\" ? \"Novo por aqui?\" : \"J\\xe1 tem uma conta?\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: toggleMode,\n                                            className: \"w-full text-center py-3 px-4 rounded-2xl border border-white/20 text-white/80 hover:text-white hover:bg-white/5 hover:border-white/30 transition-all duration-300 font-medium backdrop-blur-sm\",\n                                            children: mode === \"login\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 470,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Criar nova conta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 468,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Entrar na minha conta\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 p-4 sm:p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-white/40 text-xs sm:text-sm\",\n                            children: \"\\xa9 2025 Rafthor. Plataforma de chatbot com m\\xfaltiplas IAs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-4 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-white/30 hover:text-white/60 transition-colors text-xs\",\n                                    children: \"Privacidade\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 496,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white/20\",\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-white/30 hover:text-white/60 transition-colors text-xs\",\n                                    children: \"Termos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-white/20\",\n                                    children: \"•\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"#\",\n                                    className: \"text-white/30 hover:text-white/60 transition-colors text-xs\",\n                                    children: \"Suporte\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 491,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n                lineNumber: 490,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppearanceProvider: () => (/* binding */ AppearanceProvider),\n/* harmony export */   useAppearance: () => (/* binding */ useAppearance)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ useAppearance,AppearanceProvider auto */ \n\n\n\n\n// Configurações padrão\nconst DEFAULT_SETTINGS = {\n    fonte: \"Inter\",\n    tamanhoFonte: 14,\n    palavrasPorSessao: 5000,\n    sessionsEnabled: true\n};\n// Criar contexto\nconst AppearanceContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Hook para usar o contexto\nconst useAppearance = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppearanceContext);\n    if (!context) {\n        throw new Error(\"useAppearance must be used within an AppearanceProvider\");\n    }\n    return context;\n};\n// Função para obter username do Firestore\nconst getUsernameFromFirestore = async (userEmail)=>{\n    try {\n        // Buscar documento pelo campo email (não pelo ID do documento)\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\"));\n        const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n        const q = query(usuariosRef, where(\"email\", \"==\", userEmail));\n        const querySnapshot = await getDocs(q);\n        if (!querySnapshot.empty) {\n            const userDoc = querySnapshot.docs[0];\n            const userData = userDoc.data();\n            return userData.username || userEmail.split(\"@\")[0];\n        }\n        return userEmail.split(\"@\")[0]; // fallback\n    } catch (error) {\n        console.error(\"Erro ao obter username:\", error);\n        return userEmail.split(\"@\")[0]; // fallback\n    }\n};\n// Provider do contexto\nconst AppearanceProvider = ({ children })=>{\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(DEFAULT_SETTINGS);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Carregar configurações do Firestore\n    const loadSettings = async ()=>{\n        if (!user?.email) {\n            setSettings(DEFAULT_SETTINGS);\n            setIsLoading(false);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const username = await getUsernameFromFirestore(user.email);\n            const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n            const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n            if (configDoc.exists()) {\n                const config = configDoc.data();\n                if (config.aparencia) {\n                    setSettings({\n                        fonte: config.aparencia.fonte || DEFAULT_SETTINGS.fonte,\n                        tamanhoFonte: config.aparencia.tamanhoFonte || DEFAULT_SETTINGS.tamanhoFonte,\n                        palavrasPorSessao: config.aparencia.palavrasPorSessao || DEFAULT_SETTINGS.palavrasPorSessao,\n                        sessionsEnabled: config.aparencia.sessionsEnabled !== undefined ? config.aparencia.sessionsEnabled : DEFAULT_SETTINGS.sessionsEnabled\n                    });\n                } else {\n                    setSettings(DEFAULT_SETTINGS);\n                }\n            } else {\n                setSettings(DEFAULT_SETTINGS);\n            }\n        } catch (error) {\n            console.error(\"Erro ao carregar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            setSettings(DEFAULT_SETTINGS);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Carregar configurações quando o usuário mudar\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadSettings();\n    }, [\n        user\n    ]);\n    // Aplicar configurações às variáveis CSS globais\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading) {\n            const root = document.documentElement;\n            root.style.setProperty(\"--chat-font-family\", settings.fonte);\n            root.style.setProperty(\"--chat-font-size\", `${settings.tamanhoFonte}px`);\n            root.style.setProperty(\"--chat-words-per-session\", settings.palavrasPorSessao.toString());\n            root.style.setProperty(\"--chat-sessions-enabled\", settings.sessionsEnabled ? \"1\" : \"0\");\n        }\n    }, [\n        settings,\n        isLoading\n    ]);\n    // Função para atualizar configurações\n    const updateSettings = async (newSettings)=>{\n        const updatedSettings = {\n            ...settings,\n            ...newSettings\n        };\n        setSettings(updatedSettings);\n        // Salvar no Firestore automaticamente\n        if (user) {\n            try {\n                const username = await getUsernameFromFirestore(user.email);\n                const configRef = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.doc)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\", username, \"configuracoes\", \"settings\");\n                // Buscar configurações existentes\n                const configDoc = await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getDoc)(configRef);\n                const existingConfig = configDoc.exists() ? configDoc.data() : {};\n                // Atualizar apenas a seção de aparência\n                await (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.setDoc)(configRef, {\n                    ...existingConfig,\n                    aparencia: updatedSettings,\n                    updatedAt: new Date().toISOString()\n                }, {\n                    merge: true\n                });\n                console.log(\"✅ Configura\\xe7\\xf5es de apar\\xeancia salvas automaticamente\");\n            } catch (error) {\n                console.error(\"❌ Erro ao salvar configura\\xe7\\xf5es de apar\\xeancia:\", error);\n            }\n        }\n    };\n    // Função para aplicar configurações a um elemento específico\n    const applyToElement = (element)=>{\n        element.style.fontFamily = settings.fonte;\n        element.style.fontSize = `${settings.tamanhoFonte}px`;\n    };\n    // Função para obter variáveis CSS como objeto\n    const getCSSVariables = ()=>{\n        return {\n            \"--chat-font-family\": settings.fonte,\n            \"--chat-font-size\": `${settings.tamanhoFonte}px`,\n            \"--chat-words-per-session\": settings.palavrasPorSessao.toString()\n        };\n    };\n    const value = {\n        settings,\n        updateSettings,\n        isLoading,\n        applyToElement,\n        getCSSVariables\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppearanceContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\contexts\\\\AppearanceContext.tsx\",\n        lineNumber: 179,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AppearanceContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(ssr)/./src/lib/firebase.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    user: null,\n    loading: true,\n    logout: async ()=>{}\n});\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n};\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unsubscribe = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.onAuthStateChanged)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth, (user)=>{\n            setUser(user);\n            setLoading(false);\n        });\n        return ()=>unsubscribe();\n    }, []);\n    const logout = async ()=>{\n        try {\n            await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_2__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.auth);\n        } catch (error) {\n            console.error(\"Erro ao fazer logout:\", error);\n        }\n    };\n    const value = {\n        user,\n        loading,\n        logout\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUV1RTtBQUNMO0FBQzVCO0FBUXRDLE1BQU1PLDRCQUFjUCxvREFBYUEsQ0FBa0I7SUFDakRRLE1BQU07SUFDTkMsU0FBUztJQUNUQyxRQUFRLFdBQWE7QUFDdkI7QUFFTyxNQUFNQyxVQUFVO0lBQ3JCLE1BQU1DLFVBQVVYLGlEQUFVQSxDQUFDTTtJQUMzQixJQUFJLENBQUNLLFNBQVM7UUFDWixNQUFNLElBQUlDLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNULEVBQUU7QUFFSyxNQUFNRSxlQUFlLENBQUMsRUFBRUMsUUFBUSxFQUFpQztJQUN0RSxNQUFNLENBQUNQLE1BQU1RLFFBQVEsR0FBR2IsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDTSxTQUFTUSxXQUFXLEdBQUdkLCtDQUFRQSxDQUFDO0lBRXZDRCxnREFBU0EsQ0FBQztRQUNSLE1BQU1nQixjQUFjZCxpRUFBa0JBLENBQUNFLCtDQUFJQSxFQUFFLENBQUNFO1lBQzVDUSxRQUFRUjtZQUNSUyxXQUFXO1FBQ2I7UUFFQSxPQUFPLElBQU1DO0lBQ2YsR0FBRyxFQUFFO0lBRUwsTUFBTVIsU0FBUztRQUNiLElBQUk7WUFDRixNQUFNTCxzREFBT0EsQ0FBQ0MsK0NBQUlBO1FBQ3BCLEVBQUUsT0FBT2EsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMseUJBQXlCQTtRQUN6QztJQUNGO0lBRUEsTUFBTUUsUUFBUTtRQUNaYjtRQUNBQztRQUNBQztJQUNGO0lBRUEscUJBQ0UsOERBQUNILFlBQVllLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQzFCTjs7Ozs7O0FBR1AsRUFBRSIsInNvdXJjZXMiOlsid2VicGFjazovL3JhZnRob3IvLi9zcmMvY29udGV4dHMvQXV0aENvbnRleHQudHN4PzFmYTIiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVXNlciwgb25BdXRoU3RhdGVDaGFuZ2VkLCBzaWduT3V0IH0gZnJvbSAnZmlyZWJhc2UvYXV0aCc7XG5pbXBvcnQgeyBhdXRoIH0gZnJvbSAnQC9saWIvZmlyZWJhc2UnO1xuXG5pbnRlcmZhY2UgQXV0aENvbnRleHRUeXBlIHtcbiAgdXNlcjogVXNlciB8IG51bGw7XG4gIGxvYWRpbmc6IGJvb2xlYW47XG4gIGxvZ291dDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbn1cblxuY29uc3QgQXV0aENvbnRleHQgPSBjcmVhdGVDb250ZXh0PEF1dGhDb250ZXh0VHlwZT4oe1xuICB1c2VyOiBudWxsLFxuICBsb2FkaW5nOiB0cnVlLFxuICBsb2dvdXQ6IGFzeW5jICgpID0+IHt9LFxufSk7XG5cbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4ge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChBdXRoQ29udGV4dCk7XG4gIGlmICghY29udGV4dCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlQXV0aCBtdXN0IGJlIHVzZWQgd2l0aGluIGFuIEF1dGhQcm92aWRlcicpO1xuICB9XG4gIHJldHVybiBjb250ZXh0O1xufTtcblxuZXhwb3J0IGNvbnN0IEF1dGhQcm92aWRlciA9ICh7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSA9PiB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1bnN1YnNjcmliZSA9IG9uQXV0aFN0YXRlQ2hhbmdlZChhdXRoLCAodXNlcikgPT4ge1xuICAgICAgc2V0VXNlcih1c2VyKTtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH0pO1xuXG4gICAgcmV0dXJuICgpID0+IHVuc3Vic2NyaWJlKCk7XG4gIH0sIFtdKTtcblxuICBjb25zdCBsb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHNpZ25PdXQoYXV0aCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm8gYW8gZmF6ZXIgbG9nb3V0OicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgdXNlcixcbiAgICBsb2FkaW5nLFxuICAgIGxvZ291dCxcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxBdXRoQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvQXV0aENvbnRleHQuUHJvdmlkZXI+XG4gICk7XG59O1xuIl0sIm5hbWVzIjpbImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJvbkF1dGhTdGF0ZUNoYW5nZWQiLCJzaWduT3V0IiwiYXV0aCIsIkF1dGhDb250ZXh0IiwidXNlciIsImxvYWRpbmciLCJsb2dvdXQiLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXRVc2VyIiwic2V0TG9hZGluZyIsInVuc3Vic2NyaWJlIiwiZXJyb3IiLCJjb25zb2xlIiwidmFsdWUiLCJQcm92aWRlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/firebase.ts":
/*!*****************************!*\
  !*** ./src/lib/firebase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   functions: () => (/* binding */ functions),\n/* harmony export */   storage: () => (/* binding */ storage)\n/* harmony export */ });\n/* harmony import */ var firebase_app__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! firebase/app */ \"(ssr)/./node_modules/firebase/app/dist/index.mjs\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! firebase/auth */ \"(ssr)/./node_modules/firebase/auth/dist/index.mjs\");\n/* harmony import */ var firebase_firestore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! firebase/firestore */ \"(ssr)/./node_modules/firebase/firestore/dist/index.mjs\");\n/* harmony import */ var firebase_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! firebase/storage */ \"(ssr)/./node_modules/firebase/storage/dist/index.mjs\");\n/* harmony import */ var firebase_functions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! firebase/functions */ \"(ssr)/./node_modules/firebase/functions/dist/index.mjs\");\n\n\n\n\n\n// Configuração do Firebase - novo app criado - ATUALIZADO\nconst firebaseConfig = {\n    apiKey: \"AIzaSyA4ojPmlKBkDDl2hcfNPDXG23tEgolgCv8\",\n    authDomain: \"rafthor-0001.firebaseapp.com\",\n    projectId: \"rafthor-0001\",\n    storageBucket: \"rafthor-0001.firebasestorage.app\",\n    messagingSenderId: \"863587500028\",\n    appId: \"1:863587500028:web:ea161ddd3a1a024a7f3c79\"\n};\n// Verificar se a configuração está correta\nif (!firebaseConfig.apiKey || firebaseConfig.apiKey.length < 30) {\n    throw new Error(\"Firebase API Key inv\\xe1lida ou n\\xe3o configurada\");\n}\n// Inicializar Firebase\nconst app = (0,firebase_app__WEBPACK_IMPORTED_MODULE_0__.initializeApp)(firebaseConfig);\n// Inicializar serviços\nconst auth = (0,firebase_auth__WEBPACK_IMPORTED_MODULE_1__.getAuth)(app);\nconst db = (0,firebase_firestore__WEBPACK_IMPORTED_MODULE_2__.getFirestore)(app);\nconst storage = (0,firebase_storage__WEBPACK_IMPORTED_MODULE_3__.getStorage)(app);\nconst functions = (0,firebase_functions__WEBPACK_IMPORTED_MODULE_4__.getFunctions)(app);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (app);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/firebase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"aa15e3c3bbf4\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/OTIyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImFhMTVlM2MzYmJmNFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(rsc)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AppearanceContext */ \"(rsc)/./src/contexts/AppearanceContext.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"Rafthor - AI Chatbot Platform\",\n    description: \"Uma plataforma de chatbot com m\\xfaltiplas IAs\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"pt-BR\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AppearanceContext__WEBPACK_IMPORTED_MODULE_3__.AppearanceProvider, {\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUMrQjtBQUNZO0FBSTFELE1BQU1HLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdYLCtKQUFlO3NCQUM5Qiw0RUFBQ0MsK0RBQVlBOzBCQUNYLDRFQUFDQywyRUFBa0JBOzhCQUNoQks7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmFmdGhvci8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tICduZXh0L2ZvbnQvZ29vZ2xlJ1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IEFwcGVhcmFuY2VQcm92aWRlciB9IGZyb20gJ0AvY29udGV4dHMvQXBwZWFyYW5jZUNvbnRleHQnXG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbJ2xhdGluJ10gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdSYWZ0aG9yIC0gQUkgQ2hhdGJvdCBQbGF0Zm9ybScsXG4gIGRlc2NyaXB0aW9uOiAnVW1hIHBsYXRhZm9ybWEgZGUgY2hhdGJvdCBjb20gbcO6bHRpcGxhcyBJQXMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwicHQtQlJcIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPEF1dGhQcm92aWRlcj5cbiAgICAgICAgICA8QXBwZWFyYW5jZVByb3ZpZGVyPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvQXBwZWFyYW5jZVByb3ZpZGVyPlxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkF1dGhQcm92aWRlciIsIkFwcGVhcmFuY2VQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/login/page.tsx":
/*!********************************!*\
  !*** ./src/app/login/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\app\login\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/contexts/AppearanceContext.tsx":
/*!********************************************!*\
  !*** ./src/contexts/AppearanceContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AppearanceProvider: () => (/* binding */ e1),
/* harmony export */   useAppearance: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\contexts\AppearanceContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["useAppearance"];

const e1 = proxy["AppearanceProvider"];


/***/ }),

/***/ "(rsc)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e1),
/* harmony export */   useAuth: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Rafthor\RafthorIA\src\contexts\AuthContext.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = proxy["useAuth"];

const e1 = proxy["AuthProvider"];


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@firebase","vendor-chunks/@grpc","vendor-chunks/firebase","vendor-chunks/protobufjs","vendor-chunks/long","vendor-chunks/@protobufjs","vendor-chunks/lodash.camelcase","vendor-chunks/idb"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHenri%5CDesktop%5CRafthor%5CRafthorIA&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();