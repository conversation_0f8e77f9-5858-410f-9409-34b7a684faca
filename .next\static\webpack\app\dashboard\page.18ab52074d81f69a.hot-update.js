"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(4);\n    const MODELS_PER_PAGE = 4;\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true\n    });\n    // Load user endpoints apenas se necessário\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            // Verificar se temos cache válido\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                // Selecionar endpoint se ainda não tiver um selecionado\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    const deepSeekEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"DeepSeek\");\n                    if (openRouterEndpoint) {\n                        setSelectedEndpoint(openRouterEndpoint);\n                    } else if (deepSeekEndpoint) {\n                        setSelectedEndpoint(deepSeekEndpoint);\n                    } else {\n                        setSelectedEndpoint(endpointsCache.endpoints[0]);\n                    }\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    // Load models when endpoint changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            // Verificar se temos cache válido para este endpoint\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n                // Extrair favoritos do cache\n                const cachedFavorites = new Set(cachedData.models.filter((m)=>m.isFavorite).map((m)=>m.id));\n                setFavoriteModelIds(cachedFavorites);\n            } else {\n                // Só carregar se mudou de endpoint ou não há cache válido\n                if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                    if (selectedEndpoint.name === \"OpenRouter\") {\n                        loadOpenRouterModels();\n                    } else if (selectedEndpoint.name === \"DeepSeek\") {\n                        loadDeepSeekModels();\n                    }\n                }\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    // Load smart categories\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSmartCategories(_lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getSmartCategories());\n    }, []);\n    // Função utilitária para buscar username correto\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        try {\n            const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n            const usuariosRef = collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\");\n            const q = query(usuariosRef, where(\"email\", \"==\", user.email));\n            const querySnapshot = await getDocs(q);\n            if (!querySnapshot.empty) {\n                const userDoc = querySnapshot.docs[0];\n                return userDoc.data().username || userDoc.id;\n            }\n            return \"unknown\";\n        } catch (error) {\n            console.error(\"Erro ao buscar username:\", error);\n            return \"unknown\";\n        }\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) {\n            console.log(\"No user found\");\n            return;\n        }\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            // Salvar no cache\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            // Select first available endpoint by default (OpenRouter or DeepSeek)\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            const deepSeekEndpoint = userEndpoints.find((ep)=>ep.name === \"DeepSeek\");\n            if (openRouterEndpoint) {\n                setSelectedEndpoint(openRouterEndpoint);\n            } else if (deepSeekEndpoint) {\n                setSelectedEndpoint(deepSeekEndpoint);\n            } else if (userEndpoints.length > 0) {\n                setSelectedEndpoint(userEndpoints[0]);\n            }\n        } catch (error) {\n            console.error(\"Error loading endpoints:\", error);\n            setError(\"Erro ao carregar endpoints: \" + error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from OpenRouter\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            // Load favorite model IDs\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            // Mark favorite models\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading models:\", error);\n            setError(\"Erro ao carregar modelos\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            // Load models from DeepSeek\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            // Salvar no cache\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            console.error(\"Error loading DeepSeek models:\", error);\n            setError(\"Erro ao carregar modelos DeepSeek\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint) return;\n        // Prevenir múltiplas chamadas simultâneas para o mesmo modelo\n        if (favoriteToggling.has(model.id)) {\n            console.log(\"Already toggling favorite for model:\", model.id);\n            return;\n        }\n        console.log(\"Toggling favorite: \".concat(model.id, \" (\").concat(model.isFavorite ? \"removing\" : \"adding\", \")\"));\n        // Calcular o novo status otimisticamente\n        const optimisticNewStatus = !model.isFavorite;\n        try {\n            // Marcar como em processo\n            setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n            // ATUALIZAÇÃO OTIMISTA: Atualizar a UI imediatamente\n            const updatedFavoriteIds = new Set(favoriteModelIds);\n            if (optimisticNewStatus) {\n                updatedFavoriteIds.add(model.id);\n            } else {\n                updatedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(updatedFavoriteIds);\n            // Atualizar o array de modelos imediatamente\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: optimisticNewStatus\n                    } : m));\n            // Agora fazer a operação no Firestore\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            // Se o status real for diferente do otimista, corrigir\n            if (actualNewStatus !== optimisticNewStatus) {\n                console.warn(\"Optimistic update was incorrect, correcting...\");\n                // Corrigir o estado dos favoritos\n                const correctedFavoriteIds = new Set(favoriteModelIds);\n                if (actualNewStatus) {\n                    correctedFavoriteIds.add(model.id);\n                } else {\n                    correctedFavoriteIds.delete(model.id);\n                }\n                setFavoriteModelIds(correctedFavoriteIds);\n                // Corrigir o array de modelos\n                setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                            ...m,\n                            isFavorite: actualNewStatus\n                        } : m));\n            }\n        } catch (error) {\n            console.error(\"Error toggling favorite:\", error);\n            // Em caso de erro, reverter a atualização otimista\n            const revertedFavoriteIds = new Set(favoriteModelIds);\n            if (!optimisticNewStatus) {\n                revertedFavoriteIds.add(model.id);\n            } else {\n                revertedFavoriteIds.delete(model.id);\n            }\n            setFavoriteModelIds(revertedFavoriteIds);\n            // Reverter o array de modelos\n            setModels((prevModels)=>prevModels.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: !optimisticNewStatus\n                    } : m));\n        } finally{\n            // Remover do estado de processamento\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    // Function to check if a model is expensive (over $20 per million tokens)\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        const totalPrice = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model);\n        return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token\n    };\n    const handleSelectModel = (model)=>{\n        // Check if it's an expensive OpenRouter model\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleCancelExpensiveModel = ()=>{\n        setShowExpensiveModelModal(false);\n        setPendingExpensiveModel(null);\n    };\n    const handleLoadMoreModels = ()=>{\n        setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    };\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    // Função para forçar refresh dos modelos\n    const handleRefreshModels = ()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>{\n                const newCache = new Map(prev);\n                newCache.delete(cacheKey);\n                return newCache;\n            });\n            if (selectedEndpoint.name === \"OpenRouter\") {\n                loadOpenRouterModels();\n            } else if (selectedEndpoint.name === \"DeepSeek\") {\n                loadDeepSeekModels();\n            }\n        }\n    };\n    // Função para obter modelos filtrados\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        // Primeiro, aplicar filtros de categoria base (paid/free/favorites)\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\") {\n            if (filters.category === \"favorites\") {\n                filtered = [];\n            } else {\n                filtered = _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.filterByCategory(filtered, filters.category);\n            }\n        } else {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultModels = searchResults.map((result)=>result.model);\n            // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base\n            filtered = searchResultModels.filter((model)=>filtered.some((f)=>f.id === model.id));\n        } else if (selectedCategory) {\n            // Se há categoria inteligente selecionada, aplicar filtro adicional\n            const categoryFiltered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n            filtered = categoryFiltered;\n        }\n        // Aplicar ordenação se não há busca ativa\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-7xl max-h-[90vh] overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-b border-blue-700/30 relative z-10\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 459,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-blue-100\",\n                                            children: \"Selecionar Modelo\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"text-sm font-medium text-blue-200\",\n                                                    children: \"Endpoint:\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                                    onChange: (e)=>{\n                                                        const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                                        setSelectedEndpoint(endpoint || null);\n                                                    },\n                                                    className: \"bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-3 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Selecione um endpoint\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: endpoint.id,\n                                                                className: \"bg-blue-900 text-blue-100\",\n                                                                children: endpoint.name\n                                                            }, endpoint.id, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, undefined))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleRefreshModels,\n                                                    disabled: loading || !selectedEndpoint,\n                                                    className: \"p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    title: \"Atualizar modelos\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: onClose,\n                                            className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-5 h-5\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M6 18L18 6M6 6l12 12\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 466,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full max-h-[calc(90vh-120px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-64 border-r border-blue-700/30 bg-blue-900/20 backdrop-blur-sm relative z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 space-y-4\",\n                                    children: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3\",\n                                                children: \"Categorias\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            [\n                                                \"paid\",\n                                                \"free\",\n                                                \"favorites\"\n                                            ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setFilters((prev)=>({\n                                                                ...prev,\n                                                                category\n                                                            })),\n                                                    className: \"w-full text-left py-3 px-4 rounded-xl text-sm font-medium transition-all duration-200 flex items-center space-x-3 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full bg-current\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 527,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, category, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-300\",\n                                        children: \"Conte\\xfado principal aqui...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 451,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: ()=>{},\n                onCancel: ()=>{}\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 547,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 450,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"jlw8EWqkrBmgQiVJdSRGhw1B1SQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});