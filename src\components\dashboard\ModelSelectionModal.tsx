'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/firebase';
import { getUserAPIEndpoints, type APIEndpoint } from '@/lib/services/settingsService';
import { openRouterService } from '@/lib/services/openRouterService';
import { deepSeekService } from '@/lib/services/deepSeekService';
import { modelFavoritesService } from '@/lib/services/modelFavoritesService';
import { AIModel, ModelCategory, ModelSortBy, ModelFilters } from '@/lib/types/chat';
import { useAdvancedSearch } from '@/hooks/useAdvancedSearch';
import { advancedFiltersService, SmartCategory } from '@/lib/services/advancedFiltersService';
import AdvancedSearchInput, { HighlightedText } from '@/components/AdvancedSearchInput';
import ExpensiveModelConfirmationModal from './ExpensiveModelConfirmationModal';

interface ModelSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentModel: string;
  onModelSelect: (modelId: string) => void;
}

// Constantes para cache
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos
const ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos

const ModelSelectionModal = ({ isOpen, onClose, currentModel, onModelSelect }: ModelSelectionModalProps) => {
  const { user } = useAuth();
  const [endpoints, setEndpoints] = useState<APIEndpoint[]>([]);
  const [selectedEndpoint, setSelectedEndpoint] = useState<APIEndpoint | null>(null);
  const [models, setModels] = useState<AIModel[]>([]);
  const [favoriteModelIds, setFavoriteModelIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [displayedModelsCount, setDisplayedModelsCount] = useState(4);
  const MODELS_PER_PAGE = 4;
  const [customModelId, setCustomModelId] = useState('');
  const [showExpensiveModelModal, setShowExpensiveModelModal] = useState(false);
  const [pendingExpensiveModel, setPendingExpensiveModel] = useState<AIModel | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [smartCategories, setSmartCategories] = useState<SmartCategory[]>([]);
  const [favoriteToggling, setFavoriteToggling] = useState<Set<string>>(new Set());
  const [lastLoadedEndpoint, setLastLoadedEndpoint] = useState<string | null>(null);
  const [modelsCache, setModelsCache] = useState<Map<string, { models: AIModel[], timestamp: number }>>(new Map());
  const [endpointsCache, setEndpointsCache] = useState<{ endpoints: APIEndpoint[], timestamp: number } | null>(null);

  const [filters, setFilters] = useState<ModelFilters>({
    category: 'paid',
    sortBy: 'newest',
    searchTerm: ''
  });

  // Hook de busca avançada
  const {
    searchTerm,
    setSearchTerm,
    searchResults,
    suggestions,
    isSearching,
    hasSearched,
    clearSearch
  } = useAdvancedSearch(models, {
    debounceMs: 300,
    enableSuggestions: false,
    cacheResults: true,
    fuzzyThreshold: 0.6,
    maxResults: 50,
    boostFavorites: true
  });

  // Load user endpoints apenas se necessário
  useEffect(() => {
    if (user && isOpen) {
      // Verificar se temos cache válido
      if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {
        setEndpoints(endpointsCache.endpoints);

        // Selecionar endpoint se ainda não tiver um selecionado
        if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {
          const openRouterEndpoint = endpointsCache.endpoints.find(ep => ep.name === 'OpenRouter');
          const deepSeekEndpoint = endpointsCache.endpoints.find(ep => ep.name === 'DeepSeek');

          if (openRouterEndpoint) {
            setSelectedEndpoint(openRouterEndpoint);
          } else if (deepSeekEndpoint) {
            setSelectedEndpoint(deepSeekEndpoint);
          } else {
            setSelectedEndpoint(endpointsCache.endpoints[0]);
          }
        }
      } else {
        loadEndpoints();
      }
    }
  }, [user, isOpen]);

  // Estado para controlar se já carregamos favoritos para este endpoint
  const [favoritesLoadedForEndpoint, setFavoritesLoadedForEndpoint] = useState<string | null>(null);

  // Atualizar favoritos apenas quando necessário
  useEffect(() => {
    if (isOpen && selectedEndpoint && selectedEndpoint.name === 'OpenRouter' && user && models.length > 0) {
      const endpointKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;

      // Só carregar se não carregamos ainda para este endpoint
      if (favoritesLoadedForEndpoint !== endpointKey) {
        console.log('Loading favorites for endpoint:', endpointKey);
        setFavoritesLoadedForEndpoint(endpointKey);

        const timer = setTimeout(() => {
          updateFavoritesFromFirestore();
        }, 100);

        return () => clearTimeout(timer);
      }
    }
  }, [isOpen, selectedEndpoint, user, models.length, favoritesLoadedForEndpoint]);

  // Reset do estado quando o endpoint muda
  useEffect(() => {
    if (selectedEndpoint) {
      const endpointKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
      if (favoritesLoadedForEndpoint && favoritesLoadedForEndpoint !== endpointKey) {
        setFavoritesLoadedForEndpoint(null);
      }
    }
  }, [selectedEndpoint]);

  // Load models when endpoint changes ou não há cache
  useEffect(() => {
    if (selectedEndpoint) {
      const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
      const cachedData = modelsCache.get(cacheKey);

      // Verificar se temos cache válido para este endpoint
      if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {
        setModels(cachedData.models);
        setLastLoadedEndpoint(selectedEndpoint.id);

        // Extrair favoritos do cache
        const cachedFavorites = new Set(
          cachedData.models.filter(m => m.isFavorite).map(m => m.id)
        );
        setFavoriteModelIds(cachedFavorites);
      } else {
        // Só carregar se mudou de endpoint ou não há cache válido
        if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {
          if (selectedEndpoint.name === 'OpenRouter') {
            loadOpenRouterModels();
          } else if (selectedEndpoint.name === 'DeepSeek') {
            loadDeepSeekModels();
          }
        }
      }
    }
  }, [selectedEndpoint, lastLoadedEndpoint, modelsCache]);

  // Função para atualizar apenas os favoritos sem recarregar todos os modelos
  const updateFavoritesFromFirestore = async () => {
    if (!selectedEndpoint || !user || selectedEndpoint.name !== 'OpenRouter') return;

    try {
      const username = await getUsernameFromFirestore();
      const favoriteIds = await modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);

      // Só fazer log se houver mudança nos favoritos
      const currentFavoriteIds = Array.from(favoriteModelIds);
      const newFavoriteIds = Array.from(favoriteIds);
      const hasChanged = currentFavoriteIds.length !== newFavoriteIds.length ||
                        !currentFavoriteIds.every(id => favoriteIds.has(id));

      if (hasChanged) {
        console.log('Updating favorites from Firestore:', newFavoriteIds);
      }

      // Atualizar o estado dos favoritos
      setFavoriteModelIds(favoriteIds);

      // Atualizar os modelos com o novo status de favorito
      setModels(prevModels => {
        const updatedModels = prevModels.map(model => ({
          ...model,
          isFavorite: favoriteIds.has(model.id)
        }));

        // Log apenas se houver mudança real
        if (hasChanged) {
          const favoritedModels = updatedModels.filter(m => m.isFavorite);
          console.log('Models updated with favorites:', favoritedModels.map(m => m.id));
        }

        return updatedModels;
      });

      // Atualizar o cache
      const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
      setModelsCache(prev => {
        const currentCache = prev.get(cacheKey);
        if (currentCache) {
          const updatedModels = currentCache.models.map(model => ({
            ...model,
            isFavorite: favoriteIds.has(model.id)
          }));
          return new Map(prev).set(cacheKey, {
            models: updatedModels,
            timestamp: currentCache.timestamp
          });
        }
        return prev;
      });
    } catch (error) {
      console.error('Error updating favorites from Firestore:', error);
    }
  };

  // Load smart categories
  useEffect(() => {
    setSmartCategories(advancedFiltersService.getSmartCategories());
  }, []);

  // Função utilitária para buscar username correto
  const getUsernameFromFirestore = async (): Promise<string> => {
    if (!user?.email) return 'unknown';

    try {
      const { collection, query, where, getDocs } = await import('firebase/firestore');
      const usuariosRef = collection(db, 'usuarios');
      const q = query(usuariosRef, where('email', '==', user.email));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const userDoc = querySnapshot.docs[0];
        return userDoc.data().username || userDoc.id;
      }

      return 'unknown';
    } catch (error) {
      console.error('Erro ao buscar username:', error);
      return 'unknown';
    }
  };

  const loadEndpoints = async () => {
    if (!user) {
      console.log('No user found');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const username = await getUsernameFromFirestore();
      const userEndpoints = await getUserAPIEndpoints(username);

      // Salvar no cache
      setEndpointsCache({
        endpoints: userEndpoints,
        timestamp: Date.now()
      });

      setEndpoints(userEndpoints);

      // Select first available endpoint by default (OpenRouter or DeepSeek)
      const openRouterEndpoint = userEndpoints.find(ep => ep.name === 'OpenRouter');
      const deepSeekEndpoint = userEndpoints.find(ep => ep.name === 'DeepSeek');

      if (openRouterEndpoint) {
        setSelectedEndpoint(openRouterEndpoint);
      } else if (deepSeekEndpoint) {
        setSelectedEndpoint(deepSeekEndpoint);
      } else if (userEndpoints.length > 0) {
        setSelectedEndpoint(userEndpoints[0]);
      }
    } catch (error) {
      console.error('Error loading endpoints:', error);
      setError('Erro ao carregar endpoints: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const loadOpenRouterModels = async () => {
    if (!selectedEndpoint || !user) return;

    setLoading(true);
    setError(null);

    try {
      // Load models from OpenRouter
      const openRouterModels = await openRouterService.fetchModels();

      // Load favorite model IDs - sempre buscar do Firestore para garantir dados atualizados
      const username = await getUsernameFromFirestore();
      const favoriteIds = await modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);

      if (favoriteIds.size > 0) {
        console.log('Loaded favorite IDs:', Array.from(favoriteIds));
      }

      // Mark favorite models
      const modelsWithFavorites = openRouterModels.map(model => ({
        ...model,
        isFavorite: favoriteIds.has(model.id)
      }));

      // Salvar no cache
      const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
      setModelsCache(prev => new Map(prev).set(cacheKey, {
        models: modelsWithFavorites,
        timestamp: Date.now()
      }));

      setModels(modelsWithFavorites);
      setFavoriteModelIds(favoriteIds);
      setLastLoadedEndpoint(selectedEndpoint.id);

      const favoritedCount = modelsWithFavorites.filter(m => m.isFavorite).length;
      if (favoritedCount > 0) {
        console.log('Models loaded with favorites:', favoritedCount, 'favorites found');
      }
    } catch (error) {
      console.error('Error loading models:', error);
      setError('Erro ao carregar modelos');
    } finally {
      setLoading(false);
    }
  };

  const loadDeepSeekModels = async () => {
    if (!selectedEndpoint || !user) return;

    setLoading(true);
    setError(null);

    try {
      // Load models from DeepSeek
      const deepSeekModels = await deepSeekService.fetchModels();

      // Salvar no cache
      const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
      setModelsCache(prev => new Map(prev).set(cacheKey, {
        models: deepSeekModels,
        timestamp: Date.now()
      }));

      setModels(deepSeekModels);
      setLastLoadedEndpoint(selectedEndpoint.id);
    } catch (error) {
      console.error('Error loading DeepSeek models:', error);
      setError('Erro ao carregar modelos DeepSeek');
    } finally {
      setLoading(false);
    }
  };

  // Função para obter modelos filtrados
  const getFilteredModels = () => {
    let filtered = [...models];

    // Primeiro, aplicar filtros de categoria base (paid/free/favorites)
    if (selectedEndpoint?.name === 'DeepSeek') {
      if (filters.category === 'favorites') {
        filtered = [];
      } else {
        filtered = deepSeekService.filterByCategory(filtered, filters.category as 'paid' | 'free');
      }
    } else {
      filtered = openRouterService.filterByCategory(filtered, filters.category);
    }

    // Se há busca ativa, usar resultados da busca avançada (mas ainda respeitando a categoria base)
    if (hasSearched && searchTerm.trim()) {
      const searchResultModels = searchResults.map(result => result.model);
      // Filtrar os resultados de busca para manter apenas os que passam pelo filtro de categoria base
      filtered = searchResultModels.filter(model => filtered.some(f => f.id === model.id));
    } else if (selectedCategory) {
      // Se há categoria inteligente selecionada, aplicar filtro adicional
      const categoryFiltered = advancedFiltersService.getModelsByCategory(filtered, selectedCategory);
      filtered = categoryFiltered;
    }

    // Aplicar ordenação se não há busca ativa
    if (!hasSearched || !searchTerm.trim()) {
      const service = selectedEndpoint?.name === 'DeepSeek' ? deepSeekService : openRouterService;
      filtered = service.sortModels(filtered, filters.sortBy);
    }

    return filtered;
  };

  const filteredModels = getFilteredModels();

  const handleToggleFavorite = async (model: AIModel) => {
    if (!user || !selectedEndpoint) return;

    // Prevenir múltiplas chamadas simultâneas para o mesmo modelo
    if (favoriteToggling.has(model.id)) {
      console.log('Already toggling favorite for model:', model.id);
      return;
    }

    console.log(`Toggling favorite: ${model.id} (${model.isFavorite ? 'removing' : 'adding'})`);

    // Calcular o novo status otimisticamente
    const optimisticNewStatus = !model.isFavorite;

    try {
      // Marcar como em processo
      setFavoriteToggling(prev => new Set(prev).add(model.id));

      // ATUALIZAÇÃO OTIMISTA: Atualizar a UI imediatamente
      const updatedFavoriteIds = new Set(favoriteModelIds);
      if (optimisticNewStatus) {
        updatedFavoriteIds.add(model.id);
      } else {
        updatedFavoriteIds.delete(model.id);
      }
      setFavoriteModelIds(updatedFavoriteIds);

      // Atualizar o array de modelos imediatamente
      setModels(prevModels =>
        prevModels.map(m =>
          m.id === model.id ? { ...m, isFavorite: optimisticNewStatus } : m
        )
      );

      // Atualizar o cache imediatamente
      if (selectedEndpoint) {
        const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
        setModelsCache(prev => {
          const currentCache = prev.get(cacheKey);
          if (currentCache) {
            const updatedModels = currentCache.models.map(m =>
              m.id === model.id ? { ...m, isFavorite: optimisticNewStatus } : m
            );
            return new Map(prev).set(cacheKey, {
              models: updatedModels,
              timestamp: currentCache.timestamp
            });
          }
          return prev;
        });
      }

      // Agora fazer a operação no Firestore
      const username = await getUsernameFromFirestore();
      const actualNewStatus = await modelFavoritesService.toggleFavorite(
        username,
        selectedEndpoint.id,
        model.id,
        model.name
      );

      // Log apenas se houver diferença
      if (actualNewStatus !== optimisticNewStatus) {
        console.log('Status mismatch - Optimistic:', optimisticNewStatus, 'Actual:', actualNewStatus);
      }

      // Se o status real for diferente do otimista, corrigir
      if (actualNewStatus !== optimisticNewStatus) {
        console.warn('Optimistic update was incorrect, correcting...');

        // Corrigir o estado dos favoritos
        const correctedFavoriteIds = new Set(favoriteModelIds);
        if (actualNewStatus) {
          correctedFavoriteIds.add(model.id);
        } else {
          correctedFavoriteIds.delete(model.id);
        }
        setFavoriteModelIds(correctedFavoriteIds);

        // Corrigir o array de modelos
        setModels(prevModels =>
          prevModels.map(m =>
            m.id === model.id ? { ...m, isFavorite: actualNewStatus } : m
          )
        );

        // Corrigir o cache
        if (selectedEndpoint) {
          const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
          setModelsCache(prev => {
            const currentCache = prev.get(cacheKey);
            if (currentCache) {
              const correctedModels = currentCache.models.map(m =>
                m.id === model.id ? { ...m, isFavorite: actualNewStatus } : m
              );
              return new Map(prev).set(cacheKey, {
                models: correctedModels,
                timestamp: currentCache.timestamp
              });
            }
            return prev;
          });
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);

      // Em caso de erro, reverter a atualização otimista
      const revertedFavoriteIds = new Set(favoriteModelIds);
      if (!optimisticNewStatus) {
        revertedFavoriteIds.add(model.id);
      } else {
        revertedFavoriteIds.delete(model.id);
      }
      setFavoriteModelIds(revertedFavoriteIds);

      // Reverter o array de modelos
      setModels(prevModels =>
        prevModels.map(m =>
          m.id === model.id ? { ...m, isFavorite: !optimisticNewStatus } : m
        )
      );

      // Reverter o cache
      if (selectedEndpoint) {
        const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
        setModelsCache(prev => {
          const currentCache = prev.get(cacheKey);
          if (currentCache) {
            const revertedModels = currentCache.models.map(m =>
              m.id === model.id ? { ...m, isFavorite: !optimisticNewStatus } : m
            );
            return new Map(prev).set(cacheKey, {
              models: revertedModels,
              timestamp: currentCache.timestamp
            });
          }
          return prev;
        });
      }
    } finally {
      // Remover do estado de processamento
      setFavoriteToggling(prev => {
        const newSet = new Set(prev);
        newSet.delete(model.id);
        return newSet;
      });
    }
  };

  // Function to check if a model is expensive (over $20 per million tokens)
  const isExpensiveModel = (model: AIModel): boolean => {
    if (selectedEndpoint?.name !== 'OpenRouter') return false;
    const totalPrice = openRouterService.getTotalPrice(model);
    return totalPrice > 0.00002; // $20 por 1M tokens = $0.00002 por token
  };

  const handleSelectModel = (model: AIModel) => {
    // Check if it's an expensive OpenRouter model
    if (isExpensiveModel(model)) {
      setPendingExpensiveModel(model);
      setShowExpensiveModelModal(true);
    } else {
      onModelSelect(model.id);
      onClose();
    }
  };

  const handleConfirmExpensiveModel = () => {
    if (pendingExpensiveModel) {
      onModelSelect(pendingExpensiveModel.id);
      setShowExpensiveModelModal(false);
      setPendingExpensiveModel(null);
      onClose();
    }
  };

  const handleCancelExpensiveModel = () => {
    setShowExpensiveModelModal(false);
    setPendingExpensiveModel(null);
  };

  const handleLoadMoreModels = () => {
    setDisplayedModelsCount(prev => prev + MODELS_PER_PAGE);
  };

  const handleUseCustomModel = () => {
    if (customModelId.trim()) {
      onModelSelect(customModelId.trim());
      onClose();
    }
  };

  // Função para forçar refresh dos modelos
  const handleRefreshModels = () => {
    if (selectedEndpoint) {
      const cacheKey = `${selectedEndpoint.id}_${selectedEndpoint.name}`;
      setModelsCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(cacheKey);
        return newCache;
      });


      if (selectedEndpoint.name === 'OpenRouter') {
        loadOpenRouterModels();
      } else if (selectedEndpoint.name === 'DeepSeek') {
        loadDeepSeekModels();
      }
    }
  };





  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-blue-950/95 via-blue-900/95 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-7xl max-h-[85vh] overflow-hidden relative">
        {/* Efeito de brilho sutil */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl"></div>

        {/* Header */}
        <div className="p-6 border-b border-blue-700/30 relative z-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold text-blue-100">Selecionar Modelo</h2>
            </div>
            <div className="flex items-center space-x-3">
              {/* Endpoint Selection */}
              <div className="flex items-center space-x-3">
                <label className="text-sm font-medium text-blue-200">Endpoint:</label>
                <select
                  value={selectedEndpoint?.id || ''}
                  onChange={(e) => {
                    const endpoint = endpoints.find(ep => ep.id === e.target.value);
                    setSelectedEndpoint(endpoint || null);
                  }}
                  className="bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-3 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200"
                >
                  <option value="">Selecione um endpoint</option>
                  {endpoints.map(endpoint => (
                    <option key={endpoint.id} value={endpoint.id} className="bg-blue-900 text-blue-100">
                      {endpoint.name}
                    </option>
                  ))}
                </select>
              </div>
              <button
                onClick={handleRefreshModels}
                disabled={loading || !selectedEndpoint}
                className="p-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
                title="Atualizar modelos"
              >
                <svg className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
              </button>
              <button
                onClick={onClose}
                className="p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content - Layout Horizontal */}
        <div className="flex h-full max-h-[calc(85vh-120px)]">

        {selectedEndpoint?.name === 'OpenRouter' && (
          <>
            {/* Filters */}
            <div className="p-6 border-b border-blue-700/30 space-y-6 relative z-10">
              {/* Custom Model Section */}
              <div className="p-4 bg-blue-900/30 backdrop-blur-sm rounded-xl border border-blue-600/30">
                <h4 className="text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2">
                  <svg className="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                  </svg>
                  <span>Modelo Customizado</span>
                </h4>
                <div className="flex space-x-3">
                  <input
                    type="text"
                    placeholder="openai/gpt-4-1"
                    value={customModelId}
                    onChange={(e) => setCustomModelId(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleUseCustomModel()}
                    className="flex-1 bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 placeholder:text-blue-300/60 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200"
                  />
                  <button
                    onClick={handleUseCustomModel}
                    disabled={!customModelId.trim()}
                    className="px-6 py-3 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:from-blue-800 disabled:to-blue-800 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 disabled:hover:scale-100"
                  >
                    Usar
                  </button>
                </div>
                <p className="text-xs text-blue-300/70 mt-3">
                  Digite o ID completo do modelo (ex: openai/gpt-4, anthropic/claude-3-sonnet)
                </p>
              </div>

              {/* Category Tabs */}
              <div className="flex space-x-1 bg-blue-900/30 backdrop-blur-sm rounded-xl p-1 border border-blue-600/20">
                {(['paid', 'free', 'favorites'] as ModelCategory[]).map(category => (
                  <button
                    key={category}
                    onClick={() => setFilters(prev => ({ ...prev, category }))}
                    className={`flex-1 py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 ${
                      filters.category === category
                        ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg'
                        : 'text-blue-300 hover:text-blue-200 hover:bg-blue-800/30'
                    }`}
                  >
                    {category === 'paid' ? 'Pagos' : category === 'free' ? 'Grátis' : 'Favoritos'}
                  </button>
                ))}
              </div>

              {/* Advanced Search */}
              <div className="space-y-4">
                <div className="flex space-x-4">
                  <div className="flex-1">
                    <AdvancedSearchInput
                      value={searchTerm}
                      onChange={setSearchTerm}
                      suggestions={[]}
                      isSearching={isSearching}
                      placeholder="Buscar modelos... (ex: 'gpt-4', 'vision', 'cheap')"
                      showSuggestions={false}
                    />
                  </div>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value as ModelSortBy }))}
                    className="bg-blue-800/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500/50 transition-all duration-200"
                    disabled={hasSearched && searchTerm.trim().length > 0}
                  >
                    <option value="newest" className="bg-blue-900 text-blue-100">Mais recentes</option>
                    <option value="price_low" className="bg-blue-900 text-blue-100">Menor preço</option>
                    <option value="price_high" className="bg-blue-900 text-blue-100">Maior preço</option>
                    <option value="context_high" className="bg-blue-900 text-blue-100">Maior contexto</option>
                  </select>
                </div>

                {/* Smart Categories */}
                {!hasSearched && (
                  <div className="flex flex-wrap gap-2">
                    <button
                      onClick={() => setSelectedCategory(null)}
                      className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                        !selectedCategory
                          ? 'bg-blue-600 text-white'
                          : 'bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200'
                      }`}
                    >
                      Todos
                    </button>
                    {smartCategories.slice(0, 6).map(category => (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 ${
                          selectedCategory === category.id
                            ? 'bg-blue-600 text-white'
                            : 'bg-blue-800/40 text-blue-300 hover:bg-blue-700/50 hover:text-blue-200'
                        }`}
                        title={category.description}
                      >
                        <span>{category.icon}</span>
                        <span>{category.name}</span>
                      </button>
                    ))}
                  </div>
                )}

                {/* Search Results Info */}
                {hasSearched && searchTerm.trim() && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-blue-300">
                      {filteredModels.length} resultado{filteredModels.length !== 1 ? 's' : ''} para "{searchTerm}"
                    </span>
                    <button
                      onClick={clearSearch}
                      className="text-blue-400 hover:text-blue-300 transition-colors duration-200"
                    >
                      Limpar busca
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Models List */}
            <div className="flex-1 overflow-y-scroll p-6 max-h-[32rem] relative z-10">
              {loading && (
                <div className="flex justify-center py-8">
                  <div className="flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"></div>
                    <span className="text-blue-200 text-sm font-medium">Carregando modelos...</span>
                  </div>
                </div>
              )}

              {error && (
                <div className="bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center">
                  <div className="flex items-center justify-center space-x-3">
                    <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center">
                      <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <span className="text-red-300 font-medium">{error}</span>
                  </div>
                </div>
              )}

              {!loading && !error && filteredModels.length === 0 && (
                <div className="text-center py-8">
                  <div className="w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <p className="text-blue-300 font-medium">
                    {hasSearched && searchTerm.trim()
                      ? `Nenhum resultado para "${searchTerm}"`
                      : selectedCategory
                      ? `Nenhum modelo na categoria selecionada`
                      : 'Nenhum modelo encontrado'
                    }
                  </p>
                  <div className="text-blue-400/70 text-sm mt-2 space-y-1">
                    {hasSearched && searchTerm.trim() ? (
                      <>
                        <p>Tente:</p>
                        <ul className="list-disc list-inside space-y-1 text-left max-w-xs mx-auto">
                          <li>Verificar a ortografia</li>
                          <li>Usar termos mais genéricos</li>
                          <li>Explorar as categorias</li>
                          <li>Limpar filtros ativos</li>
                        </ul>
                      </>
                    ) : (
                      <p>Tente ajustar os filtros ou usar as categorias</p>
                    )}
                  </div>


                </div>
              )}

              <div className="space-y-3">
                {filteredModels.slice(0, displayedModelsCount).map(model => {
                  // Encontrar o resultado da busca para este modelo (se houver)
                  const searchResult = hasSearched ? searchResults.find(r => r.model.id === model.id) : null;

                  return (
                    <ModelCard
                      key={model.id}
                      model={model}
                      isSelected={currentModel === model.id}
                      onSelect={() => handleSelectModel(model)}
                      onToggleFavorite={() => handleToggleFavorite(model)}
                      isToggling={favoriteToggling.has(model.id)}
                      service={openRouterService}
                      searchTerm={hasSearched ? searchTerm : ''}
                      searchResult={searchResult}
                    />
                  );
                })}
              </div>

              {/* Load More Button */}
              {!loading && !error && filteredModels.length > displayedModelsCount && (
                <div className="flex justify-center mt-6">
                  <button
                    onClick={handleLoadMoreModels}
                    className="px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 backdrop-blur-sm border border-blue-600/30 hover:border-blue-500/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all duration-200 flex items-center space-x-2 hover:scale-105"
                  >
                    <span>Carregar mais modelos</span>
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              )}

              {/* Models count info */}
              {!loading && !error && filteredModels.length > 0 && (
                <div className="text-center mt-4 space-y-2">
                  <div className="text-sm text-blue-400/70">
                    Mostrando {Math.min(displayedModelsCount, filteredModels.length)} de {filteredModels.length} modelos
                    {models.length !== filteredModels.length && (
                      <span className="ml-2 text-blue-300">
                        ({models.length} total)
                      </span>
                    )}
                  </div>

                  {/* Search performance indicator */}
                  {hasSearched && searchTerm.trim() && (
                    <div className="flex items-center justify-center space-x-4 text-xs text-blue-400/60">
                      <span>🔍 Busca: {searchTerm}</span>
                      {searchResults.length > 0 && (
                        <span>⚡ {searchResults.length} resultados</span>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </>
        )}

        {selectedEndpoint?.name === 'DeepSeek' && (
          <>
            {/* DeepSeek Header and Filters */}
            <div className="p-6 border-b border-slate-700/30 space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-medium text-slate-100 mb-2">Modelos DeepSeek</h3>
                <p className="text-sm text-slate-400">Escolha entre nossos modelos especializados</p>
              </div>


            </div>

            {/* DeepSeek Models */}
            <div className="flex-1 overflow-y-auto p-6">
              {loading && (
                <div className="flex justify-center py-8">
                  <div className="flex items-center space-x-3 bg-blue-900/50 backdrop-blur-sm border border-blue-600/30 rounded-xl px-4 py-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-400"></div>
                    <span className="text-blue-200 text-sm font-medium">Carregando modelos...</span>
                  </div>
                </div>
              )}

              {error && (
                <div className="bg-red-900/30 backdrop-blur-sm border border-red-600/40 rounded-xl p-4 text-center">
                  <div className="flex items-center justify-center space-x-3">
                    <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center">
                      <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <span className="text-red-300 font-medium">{error}</span>
                  </div>
                </div>
              )}

              {!loading && !error && models.length === 0 && (
                <div className="text-center py-8">
                  <div className="w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4">
                    <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <p className="text-blue-300 font-medium">Nenhum modelo encontrado</p>
                  <p className="text-blue-400/70 text-sm mt-1">Tente ajustar os filtros de busca</p>
                </div>
              )}

              {!loading && !error && models.length > 0 && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {models.map(model => (
                    <DeepSeekModelCard
                      key={model.id}
                      model={model}
                      isSelected={currentModel === model.id}
                      onSelect={() => handleSelectModel(model)}
                    />
                  ))}
                </div>
              )}
            </div>
          </>
        )}

        {selectedEndpoint?.name !== 'OpenRouter' && selectedEndpoint?.name !== 'DeepSeek' && selectedEndpoint && (
          <div className="p-8 text-center relative z-10">
            <div className="w-16 h-16 mx-auto rounded-full bg-blue-900/30 flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <p className="text-blue-300 font-medium">Seleção de modelos disponível para OpenRouter e DeepSeek</p>
            <p className="text-blue-400/70 text-sm mt-1">Selecione um desses endpoints para ver os modelos disponíveis</p>
          </div>
        )}
      </div>

      {/* Expensive Model Confirmation Modal */}
      <ExpensiveModelConfirmationModal
        isOpen={showExpensiveModelModal}
        model={pendingExpensiveModel}
        onConfirm={handleConfirmExpensiveModel}
        onCancel={handleCancelExpensiveModel}
      />
    </div>
  );
};

// Model Card Component
interface ModelCardProps {
  model: AIModel;
  isSelected: boolean;
  onSelect: () => void;
  onToggleFavorite: () => void;
  isToggling?: boolean;
  service?: typeof openRouterService | typeof deepSeekService;
  searchTerm?: string;
  searchResult?: { model: AIModel; score: number; matchedFields: string[]; highlightedName?: string; highlightedDescription?: string; } | null;
}

const ModelCard = ({
  model,
  isSelected,
  onSelect,
  onToggleFavorite,
  isToggling = false,
  service = openRouterService,
  searchTerm = '',
  searchResult
}: ModelCardProps) => {
  const isExpensive = service === openRouterService && openRouterService.getTotalPrice(model) > 0.00002; // $20 por 1M tokens

  // Log para debug (apenas quando necessário)
  // console.log(`ModelCard ${model.id}: isFavorite=${model.isFavorite}, isToggling=${isToggling}`);

  return (
    <div
      data-model-id={model.id}
      className={`p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative ${
        isSelected
          ? 'bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20'
          : 'bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40'
      }`}>
      {/* Expensive Model Warning Badge */}
      {isExpensive && (
        <div className="absolute -top-2 -right-2 z-10">
          <div className="bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg border-2 border-slate-800">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
        </div>
      )}

      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-3 mb-3">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <h3 className="font-semibold text-blue-100 truncate">
                  {searchResult?.highlightedName ? (
                    <span dangerouslySetInnerHTML={{ __html: searchResult.highlightedName }} />
                  ) : searchTerm ? (
                    <HighlightedText text={model.name} highlight={searchTerm} />
                  ) : (
                    model.name
                  )}
                </h3>
                {isExpensive && (
                  <span className="text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full border border-amber-500/30 font-medium">
                    CARO
                  </span>
                )}
                {searchResult && searchResult.matchedFields.length > 0 && (
                  <div className="flex space-x-1">
                    {searchResult.matchedFields.slice(0, 3).map(field => (
                      <span
                        key={field}
                        className="text-xs bg-green-500/20 text-green-300 px-1.5 py-0.5 rounded border border-green-500/30"
                        title={`Encontrado em: ${field}`}
                      >
                        {field === 'name' ? '📝' : field === 'description' ? '📄' : field === 'provider' ? '🏢' : field === 'tags' ? '🏷️' : '🔍'}
                      </span>
                    ))}
                  </div>
                )}
              </div>
              <p className="text-xs text-blue-300/70 truncate mt-1 font-mono">
                <HighlightedText text={model.id} highlight={searchTerm} />
              </p>
            </div>
            {service.isFreeModel(model) && (
              <span className="px-3 py-1 bg-green-600/30 text-green-300 text-xs rounded-full border border-green-500/30 font-medium">
                Grátis
              </span>
            )}
          </div>

          {/* Description with highlighting */}
          {model.description && (
            <div className="mt-3 mb-3">
              <p className="text-sm text-blue-300/80 line-clamp-2">
                {searchResult?.highlightedDescription ? (
                  <span dangerouslySetInnerHTML={{ __html: searchResult.highlightedDescription }} />
                ) : searchTerm ? (
                  <HighlightedText text={model.description} highlight={searchTerm} />
                ) : (
                  model.description
                )}
              </p>
            </div>
          )}

          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="bg-blue-800/30 rounded-lg p-3 border border-blue-600/20">
              <span className="block text-xs text-blue-300/70 font-medium mb-1">Contexto</span>
              <span className="text-blue-200 font-semibold">{service.formatContextLength(model.context_length)}</span>
            </div>
            <div className="bg-blue-800/30 rounded-lg p-3 border border-blue-600/20">
              <span className="block text-xs text-blue-300/70 font-medium mb-1">Input</span>
              <span className="text-blue-200 font-semibold">{service.formatPrice(model.pricing.prompt)}/1M</span>
            </div>
            <div className="bg-blue-800/30 rounded-lg p-3 border border-blue-600/20">
              <span className="block text-xs text-blue-300/70 font-medium mb-1">Output</span>
              <span className="text-blue-200 font-semibold">{service.formatPrice(model.pricing.completion)}/1M</span>
            </div>
          </div>
        </div>

        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={onToggleFavorite}
            disabled={isToggling}
            className={`p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed ${
              model.isFavorite
                ? 'text-yellow-400 hover:text-yellow-300 bg-yellow-500/20 border border-yellow-500/30'
                : 'text-blue-300 hover:text-yellow-400 bg-blue-800/30 border border-blue-600/20 hover:bg-yellow-500/20 hover:border-yellow-500/30'
            }`}
            title={isToggling ? 'Processando...' : (model.isFavorite ? 'Remover dos favoritos' : 'Adicionar aos favoritos')}
          >
            {isToggling ? (
              <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current"></div>
            ) : (
              <svg className="w-5 h-5" fill={model.isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
            )}
          </button>

          <button
            onClick={onSelect}
            className="px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105 shadow-lg hover:shadow-blue-500/30"
          >
            Selecionar
          </button>
        </div>
      </div>
    </div>
  );
};

// DeepSeek Model Card Component
interface DeepSeekModelCardProps {
  model: AIModel;
  isSelected: boolean;
  onSelect: () => void;
}

const DeepSeekModelCard = ({ model, isSelected, onSelect }: DeepSeekModelCardProps) => {
  const getModelIcon = (modelId: string) => {
    if (modelId === 'deepseek-chat') {
      return (
        <svg className="w-8 h-8 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      );
    } else {
      return (
        <svg className="w-8 h-8 text-cyan-400" fill="currentColor" viewBox="0 0 24 24">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
      );
    }
  };

  return (
    <div className={`relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] ${
      isSelected
        ? 'bg-gradient-to-br from-blue-600/30 to-cyan-600/20 border-blue-500/50 shadow-lg shadow-blue-500/20'
        : 'bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40 hover:border-blue-500/40'
    }`}
    onClick={onSelect}>
      {/* Background gradient */}
      <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-br from-blue-600/10 to-cyan-600/10"></div>

      {/* Content */}
      <div className="relative z-10">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
            {getModelIcon(model.id)}
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-blue-100 text-lg">{model.name}</h3>
            <p className="text-sm text-blue-300/70 mt-1">{model.description}</p>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center">
            <div className="text-xs text-blue-300/70 font-medium mb-1">Contexto</div>
            <div className="text-sm font-semibold text-blue-200">
              {deepSeekService.formatContextLength(model.context_length)}
            </div>
          </div>
          <div className="bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center">
            <div className="text-xs text-blue-300/70 font-medium mb-1">Input</div>
            <div className="text-sm font-semibold text-blue-200">
              {deepSeekService.formatPrice(model.pricing.prompt)}/1M
            </div>
          </div>
          <div className="bg-blue-800/30 rounded-lg p-3 border border-blue-600/20 text-center">
            <div className="text-xs text-blue-300/70 font-medium mb-1">Output</div>
            <div className="text-sm font-semibold text-blue-200">
              {deepSeekService.formatPrice(model.pricing.completion)}/1M
            </div>
          </div>
        </div>

        {/* Select Button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onSelect();
          }}
          className={`w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 hover:scale-105 ${
            isSelected
              ? 'bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg shadow-blue-500/30'
              : 'bg-blue-800/40 text-blue-200 hover:bg-gradient-to-r hover:from-blue-600 hover:to-cyan-600 hover:text-white border border-blue-600/30'
          }`}
        >
          {isSelected ? 'Selecionado' : 'Selecionar Modelo'}
        </button>
      </div>
    </div>
  );
};

export default ModelSelectionModal;
