"use strict";(()=>{var e={};e.id=820,e.ids=[820,888,660],e.modules={46051:(e,t,r)=>{r.r(t),r.d(t,{config:()=>d,default:()=>c,getServerSideProps:()=>S,getStaticPaths:()=>g,getStaticProps:()=>_,reportWebVitals:()=>P,routeModule:()=>f,unstable_getServerProps:()=>x,unstable_getServerSideProps:()=>h,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>m,unstable_getStaticProps:()=>b});var a=r(87093),s=r(35244),i=r(1323),l=r(28676),o=r.n(l),n=r(2840),p=r.n(n),u=r(92534);let c=(0,i.l)(u,"default"),_=(0,i.l)(u,"getStaticProps"),g=(0,i.l)(u,"getStaticPaths"),S=(0,i.l)(u,"getServerSideProps"),d=(0,i.l)(u,"config"),P=(0,i.l)(u,"reportWebVitals"),b=(0,i.l)(u,"unstable_getStaticProps"),m=(0,i.l)(u,"unstable_getStaticPaths"),v=(0,i.l)(u,"unstable_getStaticParams"),x=(0,i.l)(u,"unstable_getServerProps"),h=(0,i.l)(u,"unstable_getServerSideProps"),f=new a.PagesRouteModule({definition:{kind:s.x.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:p(),Document:o()},userland:u})},62785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},16689:e=>{e.exports=require("react")},71017:e=>{e.exports=require("path")}};var t=require("../webpack-runtime.js");t.C(e);var __webpack_exec__=e=>t(t.s=e),r=t.X(0,[310,676,840,2],()=>__webpack_exec__(46051));module.exports=r})();