"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx":
/*!**********************************************************!*\
  !*** ./src/components/dashboard/ModelSelectionModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/services/settingsService */ \"(app-pages-browser)/./src/lib/services/settingsService.ts\");\n/* harmony import */ var _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/openRouterService */ \"(app-pages-browser)/./src/lib/services/openRouterService.ts\");\n/* harmony import */ var _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/deepSeekService */ \"(app-pages-browser)/./src/lib/services/deepSeekService.ts\");\n/* harmony import */ var _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/services/modelFavoritesService */ \"(app-pages-browser)/./src/lib/services/modelFavoritesService.ts\");\n/* harmony import */ var _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useAdvancedSearch */ \"(app-pages-browser)/./src/hooks/useAdvancedSearch.ts\");\n/* harmony import */ var _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/services/advancedFiltersService */ \"(app-pages-browser)/./src/lib/services/advancedFiltersService.ts\");\n/* harmony import */ var _components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/AdvancedSearchInput */ \"(app-pages-browser)/./src/components/AdvancedSearchInput.tsx\");\n/* harmony import */ var _ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./ExpensiveModelConfirmationModal */ \"(app-pages-browser)/./src/components/dashboard/ExpensiveModelConfirmationModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Constantes para cache\nconst CACHE_DURATION = 5 * 60 * 1000; // 5 minutos\nconst ENDPOINTS_CACHE_DURATION = 10 * 60 * 1000; // 10 minutos\nconst MODELS_PER_PAGE = 10; // Mais modelos por página para o layout maior\n// Componente Principal\nconst ModelSelectionModal = (param)=>{\n    let { isOpen, onClose, currentModel, onModelSelect } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [endpoints, setEndpoints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedEndpoint, setSelectedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [models, setModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteModelIds, setFavoriteModelIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [displayedModelsCount, setDisplayedModelsCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(MODELS_PER_PAGE);\n    const [customModelId, setCustomModelId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showExpensiveModelModal, setShowExpensiveModelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pendingExpensiveModel, setPendingExpensiveModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [smartCategories, setSmartCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoriteToggling, setFavoriteToggling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const [lastLoadedEndpoint, setLastLoadedEndpoint] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [modelsCache, setModelsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const [endpointsCache, setEndpointsCache] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"paid\",\n        sortBy: \"newest\",\n        searchTerm: \"\"\n    });\n    // Hook de busca avançada\n    const { searchTerm, setSearchTerm, searchResults, suggestions, isSearching, hasSearched, clearSearch } = (0,_hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch)(models, {\n        debounceMs: 300,\n        enableSuggestions: false,\n        cacheResults: true,\n        fuzzyThreshold: 0.6,\n        maxResults: 50,\n        boostFavorites: true\n    });\n    // (A lógica de hooks 'useEffect' e as funções de manipulação de dados permanecem as mesmas)\n    // --- Início da lógica de dados (sem alterações) ---\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (user && isOpen) {\n            if (endpointsCache && Date.now() - endpointsCache.timestamp < ENDPOINTS_CACHE_DURATION) {\n                setEndpoints(endpointsCache.endpoints);\n                if (!selectedEndpoint && endpointsCache.endpoints.length > 0) {\n                    const openRouterEndpoint = endpointsCache.endpoints.find((ep)=>ep.name === \"OpenRouter\");\n                    setSelectedEndpoint(openRouterEndpoint || endpointsCache.endpoints[0]);\n                }\n            } else {\n                loadEndpoints();\n            }\n        }\n    }, [\n        user,\n        isOpen\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (selectedEndpoint) {\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            const cachedData = modelsCache.get(cacheKey);\n            if (cachedData && Date.now() - cachedData.timestamp < CACHE_DURATION) {\n                setModels(cachedData.models);\n                setLastLoadedEndpoint(selectedEndpoint.id);\n            } else if (lastLoadedEndpoint !== selectedEndpoint.id || !cachedData) {\n                if (selectedEndpoint.name === \"OpenRouter\") loadOpenRouterModels();\n                else if (selectedEndpoint.name === \"DeepSeek\") loadDeepSeekModels();\n            }\n        }\n    }, [\n        selectedEndpoint,\n        lastLoadedEndpoint,\n        modelsCache\n    ]);\n    const getUsernameFromFirestore = async ()=>{\n        if (!(user === null || user === void 0 ? void 0 : user.email)) return \"unknown\";\n        const { collection, query, where, getDocs } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! firebase/firestore */ \"(app-pages-browser)/./node_modules/firebase/firestore/dist/esm/index.esm.js\"));\n        const q = query(collection(_lib_firebase__WEBPACK_IMPORTED_MODULE_3__.db, \"usuarios\"), where(\"email\", \"==\", user.email));\n        const querySnapshot = await getDocs(q);\n        if (!querySnapshot.empty) return querySnapshot.docs[0].data().username || querySnapshot.docs[0].id;\n        return \"unknown\";\n    };\n    const loadEndpoints = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            const username = await getUsernameFromFirestore();\n            const userEndpoints = await (0,_lib_services_settingsService__WEBPACK_IMPORTED_MODULE_4__.getUserAPIEndpoints)(username);\n            setEndpointsCache({\n                endpoints: userEndpoints,\n                timestamp: Date.now()\n            });\n            setEndpoints(userEndpoints);\n            const openRouterEndpoint = userEndpoints.find((ep)=>ep.name === \"OpenRouter\");\n            setSelectedEndpoint(openRouterEndpoint || userEndpoints[0] || null);\n        } catch (error) {\n            setError(\"Erro ao carregar endpoints.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadOpenRouterModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            const openRouterModels = await _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.fetchModels();\n            const username = await getUsernameFromFirestore();\n            const favoriteIds = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.getFavoriteModelIds(username, selectedEndpoint.id);\n            const modelsWithFavorites = openRouterModels.map((model)=>({\n                    ...model,\n                    isFavorite: favoriteIds.has(model.id)\n                }));\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: modelsWithFavorites,\n                    timestamp: Date.now()\n                }));\n            setModels(modelsWithFavorites);\n            setFavoriteModelIds(favoriteIds);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            setError(\"Erro ao carregar modelos OpenRouter.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const loadDeepSeekModels = async ()=>{\n        if (!selectedEndpoint || !user) return;\n        setLoading(true);\n        setError(null);\n        try {\n            const deepSeekModels = await _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.fetchModels();\n            const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n            setModelsCache((prev)=>new Map(prev).set(cacheKey, {\n                    models: deepSeekModels,\n                    timestamp: Date.now()\n                }));\n            setModels(deepSeekModels);\n            setLastLoadedEndpoint(selectedEndpoint.id);\n        } catch (error) {\n            setError(\"Erro ao carregar modelos DeepSeek.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getFilteredModels = ()=>{\n        let filtered = [\n            ...models\n        ];\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\") {\n            filtered = _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.filterByCategory(filtered, filters.category);\n        }\n        if (hasSearched && searchTerm.trim()) {\n            const searchResultIds = new Set(searchResults.map((result)=>result.model.id));\n            filtered = filtered.filter((model)=>searchResultIds.has(model.id));\n        } else if (selectedCategory) {\n            filtered = _lib_services_advancedFiltersService__WEBPACK_IMPORTED_MODULE_9__.advancedFiltersService.getModelsByCategory(filtered, selectedCategory);\n        }\n        if (!hasSearched || !searchTerm.trim()) {\n            const service = (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" ? _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService : _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService;\n            filtered = service.sortModels(filtered, filters.sortBy);\n        }\n        return filtered;\n    };\n    const filteredModels = getFilteredModels();\n    const handleToggleFavorite = async (model)=>{\n        if (!user || !selectedEndpoint || favoriteToggling.has(model.id)) return;\n        setFavoriteToggling((prev)=>new Set(prev).add(model.id));\n        const optimisticNewStatus = !model.isFavorite;\n        const updateUI = (status)=>{\n            setFavoriteModelIds((prev)=>{\n                const newSet = new Set(prev);\n                if (status) newSet.add(model.id);\n                else newSet.delete(model.id);\n                return newSet;\n            });\n            setModels((prev)=>prev.map((m)=>m.id === model.id ? {\n                        ...m,\n                        isFavorite: status\n                    } : m));\n        };\n        updateUI(optimisticNewStatus);\n        try {\n            const username = await getUsernameFromFirestore();\n            const actualNewStatus = await _lib_services_modelFavoritesService__WEBPACK_IMPORTED_MODULE_7__.modelFavoritesService.toggleFavorite(username, selectedEndpoint.id, model.id, model.name);\n            if (actualNewStatus !== optimisticNewStatus) updateUI(actualNewStatus);\n        } catch (error) {\n            updateUI(!optimisticNewStatus); // Revert on error\n        } finally{\n            setFavoriteToggling((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(model.id);\n                return newSet;\n            });\n        }\n    };\n    const isExpensiveModel = (model)=>{\n        if ((selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) !== \"OpenRouter\") return false;\n        return _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService.getTotalPrice(model) > 0.00002;\n    };\n    const handleSelectModel = (model)=>{\n        if (isExpensiveModel(model)) {\n            setPendingExpensiveModel(model);\n            setShowExpensiveModelModal(true);\n        } else {\n            onModelSelect(model.id);\n            onClose();\n        }\n    };\n    const handleConfirmExpensiveModel = ()=>{\n        if (pendingExpensiveModel) {\n            onModelSelect(pendingExpensiveModel.id);\n            setShowExpensiveModelModal(false);\n            setPendingExpensiveModel(null);\n            onClose();\n        }\n    };\n    const handleLoadMoreModels = ()=>setDisplayedModelsCount((prev)=>prev + MODELS_PER_PAGE);\n    const handleUseCustomModel = ()=>{\n        if (customModelId.trim()) {\n            onModelSelect(customModelId.trim());\n            onClose();\n        }\n    };\n    const handleRefreshModels = ()=>{\n        if (!selectedEndpoint) return;\n        const cacheKey = \"\".concat(selectedEndpoint.id, \"_\").concat(selectedEndpoint.name);\n        setModelsCache((prev)=>{\n            const newCache = new Map(prev);\n            newCache.delete(cacheKey);\n            return newCache;\n        });\n        if (selectedEndpoint.name === \"OpenRouter\") loadOpenRouterModels();\n        else if (selectedEndpoint.name === \"DeepSeek\") loadDeepSeekModels();\n    };\n    // --- Fim da lógica de dados ---\n    if (!isOpen) return null;\n    // ==================================================================\n    // INÍCIO DA RENDERIZAÇÃO DO JSX REESTRUTURADO\n    // ==================================================================\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black/60 backdrop-blur-md z-50 flex items-center justify-center p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-blue-950/95 via-black/80 to-blue-950/95 backdrop-blur-xl rounded-2xl border border-blue-600/30 shadow-2xl w-full max-w-7xl h-[90vh] overflow-hidden relative flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-cyan-500/5 pointer-events-none rounded-2xl\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 border-b border-blue-700/30 flex-shrink-0 flex items-center justify-between z-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 text-white\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 107\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-blue-100\",\n                                        children: \"Selecionar Modelo\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-2 rounded-xl hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 hover:scale-105\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 94\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-56 p-6 border-r border-blue-700/30 flex flex-col space-y-6 bg-black/20\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-blue-200 mb-2\",\n                                                children: \"Endpoint\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.id) || \"\",\n                                                onChange: (e)=>{\n                                                    const endpoint = endpoints.find((ep)=>ep.id === e.target.value);\n                                                    setSelectedEndpoint(endpoint || null);\n                                                },\n                                                className: \"w-full bg-blue-900/40 backdrop-blur-sm border border-blue-600/30 rounded-xl px-3 py-2 text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"Selecione\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    endpoints.map((endpoint)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: endpoint.id,\n                                                            className: \"bg-blue-900 text-blue-100\",\n                                                            children: endpoint.name\n                                                        }, endpoint.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 19\n                                                        }, undefined))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-sm font-medium text-blue-200 mb-3\",\n                                                children: \"Categorias\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col space-y-2\",\n                                                children: [\n                                                    \"paid\",\n                                                    \"free\",\n                                                    \"favorites\"\n                                                ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    category\n                                                                })),\n                                                        className: \"w-full py-3 px-4 rounded-lg text-sm font-medium transition-all duration-200 text-left flex items-center space-x-3 \".concat(filters.category === category ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white shadow-lg\" : \"text-blue-300 hover:text-blue-200 hover:bg-blue-800/30\"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: category === \"paid\" ? \"\\uD83D\\uDCB0\" : category === \"free\" ? \"\\uD83C\\uDF81\" : \"⭐\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: category === \"paid\" ? \"Pagos\" : category === \"free\" ? \"Gr\\xe1tis\" : \"Favoritos\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                lineNumber: 308,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, category, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 bg-blue-900/20 rounded-lg border border-blue-700/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-slate-100 mb-2\",\n                                                children: \"DeepSeek\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-400\",\n                                                children: \"Modelos especializados de alta performance.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-auto pt-4 border-t border-blue-800/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleRefreshModels,\n                                            disabled: loading || !selectedEndpoint,\n                                            className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-lg hover:bg-blue-800/40 text-blue-300 hover:text-blue-200 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            title: \"Atualizar modelos\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4 \".concat(loading ? \"animate-spin\" : \"\"),\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 135\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: loading ? \"Atualizando...\" : \"Atualizar\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex flex-col overflow-hidden\",\n                                children: [\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"OpenRouter\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 border-b border-blue-700/30 space-y-4 flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-900/30 rounded-xl border border-blue-600/30\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-sm font-medium text-blue-200 mb-3 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                            className: \"w-4 h-4 text-blue-400\",\n                                                                            fill: \"none\",\n                                                                            stroke: \"currentColor\",\n                                                                            viewBox: \"0 0 24 24\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                                strokeLinecap: \"round\",\n                                                                                strokeLinejoin: \"round\",\n                                                                                strokeWidth: 2,\n                                                                                d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                                lineNumber: 343,\n                                                                                columnNumber: 118\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Modelo Customizado\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 344,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"text\",\n                                                                            placeholder: \"provedor/nome-do-modelo\",\n                                                                            value: customModelId,\n                                                                            onChange: (e)=>setCustomModelId(e.target.value),\n                                                                            onKeyDown: (e)=>e.key === \"Enter\" && handleUseCustomModel(),\n                                                                            className: \"flex-1 bg-blue-800/40 border border-blue-600/30 rounded-xl px-4 py-2 text-sm text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: handleUseCustomModel,\n                                                                            disabled: !customModelId.trim(),\n                                                                            className: \"px-5 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 disabled:opacity-50 text-white rounded-xl transition-all font-medium text-sm\",\n                                                                            children: \"Usar\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 348,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 346,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 341,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                    value: searchTerm,\n                                                                    onChange: setSearchTerm,\n                                                                    suggestions: suggestions,\n                                                                    isSearching: isSearching,\n                                                                    placeholder: \"Buscar por nome, tag, provedor...\",\n                                                                    showSuggestions: false\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 352,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: filters.sortBy,\n                                                                    onChange: (e)=>setFilters((prev)=>({\n                                                                                ...prev,\n                                                                                sortBy: e.target.value\n                                                                            })),\n                                                                    className: \"w-full bg-blue-800/40 border border-blue-600/30 rounded-xl px-4 py-2 text-sm text-blue-100 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                                    disabled: hasSearched && searchTerm.trim().length > 0,\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"newest\",\n                                                                            children: \"Mais Recentes\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 354,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"price_low\",\n                                                                            children: \"Menor Pre\\xe7o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 355,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"price_high\",\n                                                                            children: \"Maior Pre\\xe7o\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"context_high\",\n                                                                            children: \"Maior Contexto\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                            lineNumber: 357,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                                    lineNumber: 353,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 overflow-y-auto p-6 space-y-3\",\n                                                children: [\n                                                    loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center items-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-300\",\n                                                            children: \"Carregando modelos...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 364,\n                                                            columnNumber: 88\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 364,\n                                                        columnNumber: 31\n                                                    }, undefined),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center items-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-red-400\",\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 86\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 365,\n                                                        columnNumber: 29\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center items-center h-full\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-blue-400\",\n                                                            children: \"Nenhum modelo encontrado.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 130\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 73\n                                                    }, undefined),\n                                                    !loading && !error && filteredModels.slice(0, displayedModelsCount).map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModelCard, {\n                                                            model: model,\n                                                            isSelected: currentModel === model.id,\n                                                            onSelect: ()=>handleSelectModel(model),\n                                                            onToggleFavorite: ()=>handleToggleFavorite(model),\n                                                            isToggling: favoriteToggling.has(model.id),\n                                                            service: _lib_services_openRouterService__WEBPACK_IMPORTED_MODULE_5__.openRouterService,\n                                                            searchTerm: searchTerm\n                                                        }, model.id, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, undefined)),\n                                                    !loading && !error && filteredModels.length > displayedModelsCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center pt-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleLoadMoreModels,\n                                                            className: \"px-6 py-3 bg-blue-800/40 hover:bg-blue-700/50 rounded-xl text-blue-200 hover:text-blue-100 transition-all\",\n                                                            children: \"Carregar mais\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 63\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true),\n                                    (selectedEndpoint === null || selectedEndpoint === void 0 ? void 0 : selectedEndpoint.name) === \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 overflow-y-auto p-6\",\n                                        children: [\n                                            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center items-center h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-300\",\n                                                    children: \"Carregando modelos...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 90\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 33\n                                            }, undefined),\n                                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center items-center h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-red-400\",\n                                                    children: error\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 88\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 31\n                                            }, undefined),\n                                            !loading && !error && models.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center items-center h-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-blue-400\",\n                                                    children: \"Nenhum modelo encontrado.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 381,\n                                                    columnNumber: 124\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 67\n                                            }, undefined),\n                                            !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n                                                children: models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DeepSeekModelCard, {\n                                                        model: model,\n                                                        isSelected: currentModel === model.id,\n                                                        onSelect: ()=>handleSelectModel(model)\n                                                    }, model.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 33\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 383,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    selectedEndpoint && selectedEndpoint.name !== \"OpenRouter\" && selectedEndpoint.name !== \"DeepSeek\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center items-center h-full p-8 text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-300 font-medium\",\n                                                    children: \"Sele\\xe7\\xe3o de modelos n\\xe3o dispon\\xedvel para este endpoint.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-blue-400/70 text-sm mt-1\",\n                                                    children: \"Selecione OpenRouter ou DeepSeek para ver os modelos.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 18\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ExpensiveModelConfirmationModal__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                isOpen: showExpensiveModelModal,\n                model: pendingExpensiveModel,\n                onConfirm: handleConfirmExpensiveModel,\n                onCancel: ()=>setShowExpensiveModelModal(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 251,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ModelSelectionModal, \"/bHmu8QxlPh63CHLKyEkFuO7EiY=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth,\n        _hooks_useAdvancedSearch__WEBPACK_IMPORTED_MODULE_8__.useAdvancedSearch\n    ];\n});\n_c = ModelSelectionModal;\nconst ModelCard = (param)=>{\n    let { model, isSelected, onSelect, onToggleFavorite, isToggling = false, service, searchTerm = \"\", searchResult } = param;\n    const isExpensive = service.getTotalPrice(model) > 0.00002;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-5 rounded-xl border transition-all duration-200 backdrop-blur-sm hover:scale-[1.02] relative \".concat(isSelected ? \"bg-blue-600/20 border-blue-500/50\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40\"),\n        children: [\n            isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-2 -right-2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-gradient-to-r from-amber-500 to-orange-500 rounded-full p-1.5 shadow-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-white\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 252\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 162\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 428,\n                    columnNumber: 70\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 428,\n                columnNumber: 23\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-w-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-blue-100 truncate\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                            text: model.name,\n                                            highlight: searchTerm\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 66\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    isExpensive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs bg-amber-500/20 text-amber-300 px-2 py-0.5 rounded-full font-medium\",\n                                        children: \"CARO\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 29\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-blue-300/70 truncate mt-1 font-mono\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                    text: model.id,\n                                    highlight: searchTerm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 75\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, undefined),\n                            model.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-300/80 line-clamp-2 mt-3 mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSearchInput__WEBPACK_IMPORTED_MODULE_10__.HighlightedText, {\n                                    text: model.description,\n                                    highlight: searchTerm\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 96\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 text-sm mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 mb-1\",\n                                                children: \"Contexto\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 60\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: service.formatContextLength(model.context_length)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 129\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 mb-1\",\n                                                children: \"Input\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 60\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.prompt),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 126\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-800/30 rounded-lg p-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"block text-xs text-blue-300/70 mb-1\",\n                                                children: \"Output\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 60\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-200 font-semibold\",\n                                                children: [\n                                                    service.formatPrice(model.pricing.completion),\n                                                    \"/1M\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 127\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center space-y-3 ml-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onSelect,\n                                className: \"w-full px-6 py-2.5 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-500 hover:to-cyan-500 text-white rounded-xl transition-all duration-200 font-medium hover:scale-105\",\n                                children: \"Selecionar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggleFavorite,\n                                disabled: isToggling,\n                                className: \"p-2.5 rounded-xl transition-all duration-200 hover:scale-105 disabled:opacity-50 \".concat(model.isFavorite ? \"text-yellow-400 bg-yellow-500/20\" : \"text-blue-300 hover:text-yellow-400 bg-blue-800/30\"),\n                                title: model.isFavorite ? \"Remover dos favoritos\" : \"Adicionar aos favoritos\",\n                                children: isToggling ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-current\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 27\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5\",\n                                    fill: model.isFavorite ? \"currentColor\" : \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 241\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 124\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 427,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = ModelCard;\nconst DeepSeekModelCard = (param)=>/*#__PURE__*/ {\n    let { model, isSelected, onSelect } = param;\n    return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative p-6 rounded-2xl border transition-all duration-300 cursor-pointer group backdrop-blur-sm hover:scale-[1.02] \".concat(isSelected ? \"bg-blue-600/20 border-blue-500/50\" : \"bg-blue-900/30 border-blue-600/30 hover:bg-blue-800/40\"),\n        onClick: onSelect,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative z-10\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold text-blue-100 text-lg\",\n                                children: model.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 33\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-blue-300/70 mt-1\",\n                                children: model.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 102\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 463,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-4 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-800/30 rounded-lg p-3 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-300/70 mb-1\",\n                                    children: \"Contexto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 68\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-semibold text-blue-200\",\n                                    children: _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatContextLength(model.context_length)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 129\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-800/30 rounded-lg p-3 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-300/70 mb-1\",\n                                    children: \"Input\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 68\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-semibold text-blue-200\",\n                                    children: [\n                                        _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.prompt),\n                                        \"/1M\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 126\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-800/30 rounded-lg p-3 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-blue-300/70 mb-1\",\n                                    children: \"Output\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 68\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-semibold text-blue-200\",\n                                    children: [\n                                        _lib_services_deepSeekService__WEBPACK_IMPORTED_MODULE_6__.deepSeekService.formatPrice(model.pricing.completion),\n                                        \"/1M\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 127\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                            lineNumber: 469,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: (e)=>{\n                        e.stopPropagation();\n                        onSelect();\n                    },\n                    className: \"w-full py-3 px-4 rounded-xl font-medium transition-all \".concat(isSelected ? \"bg-gradient-to-r from-blue-600 to-cyan-600 text-white\" : \"bg-blue-800/40 text-blue-200 hover:bg-blue-700/60\"),\n                    children: isSelected ? \"Selecionado\" : \"Selecionar Modelo\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n                    lineNumber: 471,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n            lineNumber: 462,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Rafthor\\\\RafthorIA\\\\src\\\\components\\\\dashboard\\\\ModelSelectionModal.tsx\",\n        lineNumber: 461,\n        columnNumber: 3\n    }, undefined);\n};\n_c2 = DeepSeekModelCard;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ModelSelectionModal);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ModelSelectionModal\");\n$RefreshReg$(_c1, \"ModelCard\");\n$RefreshReg$(_c2, \"DeepSeekModelCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/ModelSelectionModal.tsx\n"));

/***/ })

});